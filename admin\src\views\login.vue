<template>
    <div class="login-container">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧信息区域 -->
            <div class="info-section">
                <div class="brand-info">
                    <div class="logo-container">
                        <i class="el-icon-trophy-1"></i>
                        <h1>体育馆管理系统</h1>
                    </div>
                    <p class="brand-description">
                        现代化的体育场馆预约管理平台<br>
                        为您提供便捷、高效的场地预约服务
                    </p>
                    <div class="features">
                        <div class="feature-item">
                            <i class="el-icon-time"></i>
                            <span>24小时在线预约</span>
                        </div>
                        <div class="feature-item">
                            <i class="el-icon-location"></i>
                            <span>多场地智能管理</span>
                        </div>
                        <div class="feature-item">
                            <i class="el-icon-user"></i>
                            <span>用户友好界面</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧登录表单 -->
            <div class="login-section">
                <div class="login-card">
                    <div class="card-header">
                        <h2>欢迎登录</h2>
                        <p>请输入您的账户信息</p>
                    </div>

                    <el-form class="login-form" :model="rulesForm" @submit.native.prevent="login">
                        <el-form-item class="form-item">
                            <div class="input-wrapper">
                                <i class="el-icon-user input-icon"></i>
                                <el-input
                                    v-model="rulesForm.username"
                                    placeholder="请输入用户名"
                                    size="large"
                                    clearable>
                                </el-input>
                            </div>
                        </el-form-item>

                        <el-form-item class="form-item">
                            <div class="input-wrapper">
                                <i class="el-icon-lock input-icon"></i>
                                <el-input
                                    v-model="rulesForm.password"
                                    type="password"
                                    placeholder="请输入密码"
                                    size="large"
                                    show-password
                                    clearable>
                                </el-input>
                            </div>
                        </el-form-item>

                        <el-form-item v-if="showCaptcha" class="form-item captcha-item">
                            <div class="input-wrapper">
                                <i class="el-icon-key input-icon"></i>
                                <el-input
                                    v-model="rulesForm.code"
                                    placeholder="请输入验证码"
                                    size="large"
                                    clearable>
                                </el-input>
                                <div class="captcha-code" @click="getRandCode(4)">
                                    <span v-for="(item, index) in codes" :key="index"
                                          :style="{color:item.color,transform:item.rotate,fontSize:item.size}">
                                        {{ item.num }}
                                    </span>
                                </div>
                            </div>
                        </el-form-item>

                        <el-form-item class="form-item role-item">
                            <label class="role-label">选择角色</label>
                            <div class="role-options">
                                <el-radio-group v-model="rulesForm.role" class="role-group">
                                    <el-radio
                                        v-for="item in menus"
                                        v-if="item.hasBackLogin=='是'"
                                        :key="item.roleName"
                                        :label="item.roleName"
                                        class="role-radio">
                                        {{ item.roleName }}
                                    </el-radio>
                                </el-radio-group>
                            </div>
                        </el-form-item>

                        <el-button
                            type="primary"
                            @click="login()"
                            class="login-button"
                            size="large"
                            :loading="loading">
                            <i class="el-icon-right"></i>
                            立即登录
                        </el-button>

                        <div class="form-footer">
                            <div class="register-link" @click="register('yonghu')">
                                <i class="el-icon-user-solid"></i>
                                还没有账户？立即注册
                            </div>
                        </div>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    import menu from "@/utils/menu";
    export default {
        data() {
            return {
                loading: false,
                showCaptcha: false, // 是否显示验证码
                rulesForm: {
                    username: "",
                    password: "",
                    role: "",
                    code: '',
                },
                menus: [],
                tableName: "",
                codes: [{
                    num: 1,
                    color: '#00c292',
                    rotate: '10deg',
                    size: '16px'
                },{
                    num: 2,
                    color: '#00c292',
                    rotate: '10deg',
                    size: '16px'
                },{
                    num: 3,
                    color: '#00c292',
                    rotate: '10deg',
                    size: '16px'
                },{
                    num: 4,
                    color: '#00c292',
                    rotate: '10deg',
                    size: '16px'
                }],
            };
        },
        mounted() {
            let menus = menu.list();
            this.menus = menus;
        },
        created() {
            this.setInputColor()
            this.getRandCode()
        },
        methods: {
            setInputColor(){
                this.$nextTick(()=>{
                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{
                        el.style.backgroundColor = "rgba(255, 255, 255, 1)"
                        el.style.color = "rgba(51, 51, 51, 1)"
                        el.style.height = "44px"
                        el.style.lineHeight = "44px"
                        el.style.borderRadius = "30px"
                    })
                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{
                        el.style.height = "44px"
                        el.style.lineHeight = "44px"
                    })
                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{
                        el.style.color = "rgba(59, 160, 215, 1)"
                    })
                    setTimeout(()=>{
                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{
                            el.style.color = "#fff"
                        })
                    },350)
                })

            },
            register(tableName){
                this.$storage.set("loginTable", tableName);
                this.$router.push({path:'/register'})
            },
            // 登陆
            login() {
                // 验证码检查
                if (this.showCaptcha) {
                    let code = ''
                    for(let i in this.codes) {
                        code += this.codes[i].num
                    }
                    if (!this.rulesForm.code) {
                        this.$message.error("请输入验证码");
                        return;
                    }
                    if (this.rulesForm.code.toLowerCase() != code.toLowerCase()) {
                        this.$message.error("验证码输入有误");
                        this.getRandCode()
                        return;
                    }
                }

                // 表单验证
                if (!this.rulesForm.username) {
                    this.$message.error("请输入用户名");
                    return;
                }
                if (!this.rulesForm.password) {
                    this.$message.error("请输入密码");
                    return;
                }
                if (!this.rulesForm.role) {
                    this.$message.error("请选择角色");
                    return;
                }

                // 获取表名
                let menus = this.menus;
                for (let i = 0; i < menus.length; i++) {
                    if (menus[i].roleName == this.rulesForm.role) {
                        this.tableName = menus[i].tableName;
                    }
                }

                // 开始登录
                this.loading = true;
                this.$http({
                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,
                    method: "post"
                }).then(({ data }) => {
                    this.loading = false;
                    if (data && data.code === 0) {
                        this.$storage.set("Token", data.token);
                        this.$storage.set("role", this.rulesForm.role);
                        this.$storage.set("sessionTable", this.tableName);
                        this.$storage.set("adminName", this.rulesForm.username);

                        this.$message({
                            message: "登录成功！",
                            type: "success",
                            duration: 1500,
                            onClose: () => {
                                this.$router.replace({ path: "/index/" });
                            }
                        });
                    } else {
                        this.$message.error(data.msg || "登录失败，请检查用户名和密码");
                    }
                }).catch(() => {
                    this.loading = false;
                    this.$message.error("网络错误，请稍后重试");
                });
            },
            getRandCode(len = 4){
                this.randomString(len)
            },
            randomString(len = 4) {
                let chars = [
                    "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k",
                    "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v",
                    "w", "x", "y", "z", "A", "B", "C", "D", "E", "F", "G",
                    "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R",
                    "S", "T", "U", "V", "W", "X", "Y", "Z", "0", "1", "2",
                    "3", "4", "5", "6", "7", "8", "9"
                ]
                let colors = ["0", "1", "2","3", "4", "5", "6", "7", "8", "9", "a", "b", "c", "d", "e", "f"]
                let sizes = ['14', '15', '16', '17', '18']

                let output = [];
                for (let i = 0; i < len; i++) {
                    // 随机验证码
                    let key = Math.floor(Math.random()*chars.length)
                    this.codes[i].num = chars[key]
                    // 随机验证码颜色
                    let code = '#'
                    for (let j = 0; j < 6; j++) {
                        let key = Math.floor(Math.random()*colors.length)
                        code += colors[key]
                    }
                    this.codes[i].color = code
                    // 随机验证码方向
                    let rotate = Math.floor(Math.random()*60)
                    let plus = Math.floor(Math.random()*2)
                    if(plus == 1) rotate = '-'+rotate
                    this.codes[i].rotate = 'rotate('+rotate+'deg)'
                    // 随机验证码字体大小
                    let size = Math.floor(Math.random()*sizes.length)
                    this.codes[i].size = sizes[size]+'px'
                }
            },
        }
    };
</script>
<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  // 背景装饰
  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 40px;
    gap: 60px;
    align-items: center;

    // 左侧信息区域
    .info-section {
      flex: 1;
      color: white;

      .brand-info {
        .logo-container {
          display: flex;
          align-items: center;
          margin-bottom: 30px;

          i {
            font-size: 48px;
            color: #00c292;
            margin-right: 20px;
          }

          h1 {
            font-size: 36px;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(45deg, #00c292, #00e5ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }

        .brand-description {
          font-size: 18px;
          line-height: 1.6;
          margin-bottom: 40px;
          opacity: 0.9;
        }

        .features {
          .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            i {
              font-size: 20px;
              color: #00c292;
              margin-right: 15px;
              width: 24px;
            }

            span {
              font-size: 16px;
              opacity: 0.9;
            }
          }
        }
      }
    }

    // 右侧登录表单
    .login-section {
      flex: 0 0 450px;

      .login-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);

        .card-header {
          text-align: center;
          margin-bottom: 40px;

          h2 {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 10px 0;
          }

          p {
            color: #666;
            font-size: 16px;
            margin: 0;
          }
        }

        .login-form {
          .form-item {
            margin-bottom: 25px;

            .input-wrapper {
              position: relative;

              .input-icon {
                position: absolute;
                left: 15px;
                top: 50%;
                transform: translateY(-50%);
                color: #00c292;
                font-size: 18px;
                z-index: 2;
              }

              ::v-deep .el-input__inner {
                height: 50px;
                padding-left: 50px;
                border: 2px solid #e8f4f8;
                border-radius: 12px;
                font-size: 16px;
                transition: all 0.3s ease;
                background: #f8fffe;

                &:focus {
                  border-color: #00c292;
                  box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);
                  background: white;
                }

                &::placeholder {
                  color: #999;
                }
              }
            }

            // 验证码特殊样式
            &.captcha-item {
              .input-wrapper {
                display: flex;
                gap: 10px;

                .el-input {
                  flex: 1;
                }

                .captcha-code {
                  width: 120px;
                  height: 50px;
                  background: linear-gradient(45deg, #00c292, #00a085);
                  border-radius: 12px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  cursor: pointer;
                  transition: all 0.3s ease;

                  &:hover {
                    transform: scale(1.05);
                  }

                  span {
                    font-weight: 600;
                    color: white;
                    margin: 0 2px;
                  }
                }
              }
            }

            // 角色选择样式
            &.role-item {
              .role-label {
                display: block;
                margin-bottom: 15px;
                font-weight: 600;
                color: #2c3e50;
                font-size: 16px;
              }

              .role-options {
                .role-group {
                  display: flex;
                  flex-wrap: wrap;
                  gap: 15px;

                  .role-radio {
                    margin: 0;

                    ::v-deep .el-radio__input.is-checked .el-radio__inner {
                      background-color: #00c292;
                      border-color: #00c292;
                    }

                    ::v-deep .el-radio__input.is-checked + .el-radio__label {
                      color: #00c292;
                      font-weight: 600;
                    }

                    ::v-deep .el-radio__label {
                      font-size: 16px;
                      color: #666;
                    }
                  }
                }
              }
            }
          }

          .login-button {
            width: 100%;
            height: 50px;
            font-size: 18px;
            font-weight: 600;
            border-radius: 12px;
            background: linear-gradient(45deg, #00c292, #00a085);
            border: none;
            margin-top: 20px;
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 8px 25px rgba(0, 194, 146, 0.3);
            }

            &:active {
              transform: translateY(0);
            }

            i {
              margin-right: 8px;
            }
          }

          .form-footer {
            text-align: center;
            margin-top: 30px;

            .register-link {
              color: #00c292;
              cursor: pointer;
              font-size: 16px;
              transition: all 0.3s ease;
              display: inline-flex;
              align-items: center;

              &:hover {
                color: #00a085;
                transform: translateY(-1px);
              }

              i {
                margin-right: 8px;
                font-size: 18px;
              }
            }
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 1024px) {
  .login-container .main-content {
    flex-direction: column;
    gap: 40px;
    padding: 20px;

    .info-section {
      text-align: center;

      .brand-info .logo-container {
        justify-content: center;

        h1 {
          font-size: 28px;
        }
      }
    }

    .login-section {
      flex: none;
      width: 100%;
      max-width: 450px;
    }
  }
}

@media (max-width: 768px) {
  .login-container .main-content {
    padding: 15px;

    .login-section .login-card {
      padding: 30px 20px;

      .card-header h2 {
        font-size: 24px;
      }
    }

    .info-section .brand-info .logo-container h1 {
      font-size: 24px;
    }
  }
}
</style>

