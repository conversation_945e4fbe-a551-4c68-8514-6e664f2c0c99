{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\changdi-booking.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\changdi-booking.vue", "mtime": 1750603756615}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["changdi-booking.vue"], "names": [], "mappings": ";AAoQA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "changdi-booking.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"booking-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"title-section\">\n          <h1><i class=\"el-icon-date\"></i> 场地预约</h1>\n          <p>选择您喜欢的场地，享受运动的乐趣</p>\n        </div>\n        <div class=\"stats-cards\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ totalVenues }}</h3>\n              <p>可用场地</p>\n            </div>\n          </div>\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\">\n              <i class=\"el-icon-time\"></i>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ availableSlots }}</h3>\n              <p>可预约时段</p>\n            </div>\n          </div>\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ myBookings }}</h3>\n              <p>我的预约</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 搜索筛选区域 -->\n    <div class=\"search-section\">\n      <el-card class=\"search-card\">\n        <div class=\"search-form\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"6\">\n              <div class=\"search-item\">\n                <label>场地类型</label>\n                <el-select v-model=\"searchForm.changdiTypes\" placeholder=\"请选择场地类型\" clearable>\n                  <el-option label=\"全部类型\" value=\"\"></el-option>\n                  <el-option\n                    v-for=\"item in changdiTypesOptions\"\n                    :key=\"item.codeIndex\"\n                    :label=\"item.indexName\"\n                    :value=\"item.codeIndex\">\n                  </el-option>\n                </el-select>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"search-item\">\n                <label>预约日期</label>\n                <el-date-picker\n                  v-model=\"searchForm.bookingDate\"\n                  type=\"date\"\n                  placeholder=\"选择预约日期\"\n                  :picker-options=\"datePickerOptions\"\n                  clearable>\n                </el-date-picker>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"search-item\">\n                <label>价格范围</label>\n                <el-select v-model=\"searchForm.priceRange\" placeholder=\"请选择价格范围\" clearable>\n                  <el-option label=\"全部价格\" value=\"\"></el-option>\n                  <el-option label=\"0-50元\" value=\"0-50\"></el-option>\n                  <el-option label=\"50-100元\" value=\"50-100\"></el-option>\n                  <el-option label=\"100-200元\" value=\"100-200\"></el-option>\n                  <el-option label=\"200元以上\" value=\"200+\"></el-option>\n                </el-select>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"search-actions\">\n                <el-button type=\"primary\" @click=\"searchVenues\" :loading=\"searchLoading\">\n                  <i class=\"el-icon-search\"></i>\n                  搜索场地\n                </el-button>\n                <el-button @click=\"resetSearch\">\n                  <i class=\"el-icon-refresh\"></i>\n                  重置\n                </el-button>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 场地列表 -->\n    <div class=\"venues-section\">\n      <div class=\"section-header\">\n        <h2>可预约场地</h2>\n        <div class=\"view-toggle\">\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\n            <el-radio-button label=\"grid\">\n              <i class=\"el-icon-menu\"></i> 网格视图\n            </el-radio-button>\n            <el-radio-button label=\"list\">\n              <i class=\"el-icon-tickets\"></i> 列表视图\n            </el-radio-button>\n          </el-radio-group>\n        </div>\n      </div>\n\n      <!-- 网格视图 -->\n      <div v-if=\"viewMode === 'grid'\" class=\"venues-grid\" v-loading=\"dataListLoading\">\n        <div v-if=\"dataList.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-basketball\"></i>\n          <h3>暂无可预约场地</h3>\n          <p>请尝试调整搜索条件</p>\n        </div>\n        <div v-else class=\"venue-cards\">\n          <div v-for=\"venue in dataList\" :key=\"venue.id\" class=\"venue-card\">\n            <div class=\"venue-image\">\n              <img :src=\"venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" :alt=\"venue.changdiName\">\n              <div class=\"venue-status\" :class=\"venue.shangxiaTypes === 1 ? 'available' : 'unavailable'\">\n                {{ venue.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}\n              </div>\n            </div>\n            <div class=\"venue-info\">\n              <h3>{{ venue.changdiName }}</h3>\n              <div class=\"venue-details\">\n                <div class=\"detail-item\">\n                  <i class=\"el-icon-location\"></i>\n                  <span>{{ venue.changdiValue }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"el-icon-time\"></i>\n                  <span>{{ venue.shijianduan }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"el-icon-star-on\"></i>\n                  <span>{{ venue.banquanValue }}</span>\n                </div>\n              </div>\n              <div class=\"venue-price\">\n                <span class=\"current-price\">¥{{ venue.changdiNewMoney }}</span>\n                <span v-if=\"venue.changdiOldMoney !== venue.changdiNewMoney\" class=\"original-price\">\n                  ¥{{ venue.changdiOldMoney }}\n                </span>\n              </div>\n              <div class=\"venue-actions\">\n                <el-button type=\"primary\" @click=\"bookVenue(venue)\" :disabled=\"venue.shangxiaTypes !== 1\">\n                  <i class=\"el-icon-date\"></i>\n                  立即预约\n                </el-button>\n                <el-button\n                  :type=\"isCollected(venue.id) ? 'warning' : 'success'\"\n                  @click=\"toggleCollection(venue)\"\n                  :loading=\"venue.collectionLoading\">\n                  <i :class=\"isCollected(venue.id) ? 'el-icon-star-on' : 'el-icon-star-off'\"></i>\n                  {{ isCollected(venue.id) ? '已收藏' : '收藏' }}\n                </el-button>\n                <el-button type=\"text\" @click=\"viewVenueDetails(venue)\">\n                  <i class=\"el-icon-view\"></i>\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 列表视图 -->\n      <div v-if=\"viewMode === 'list'\" class=\"venues-list\" v-loading=\"dataListLoading\">\n        <el-table :data=\"dataList\" style=\"width: 100%\">\n          <el-table-column prop=\"changdiPhoto\" label=\"场地图片\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <img :src=\"scope.row.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" \n                   style=\"width: 80px; height: 60px; object-fit: cover; border-radius: 4px;\">\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"changdiName\" label=\"场地名称\" min-width=\"150\"></el-table-column>\n          <el-table-column prop=\"changdiValue\" label=\"场地类型\" width=\"120\"></el-table-column>\n          <el-table-column prop=\"shijianduan\" label=\"时间段\" width=\"150\"></el-table-column>\n          <el-table-column prop=\"banquanValue\" label=\"半全场\" width=\"100\"></el-table-column>\n          <el-table-column label=\"价格\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <div class=\"price-cell\">\n                <span class=\"current-price\">¥{{ scope.row.changdiNewMoney }}</span>\n                <span v-if=\"scope.row.changdiOldMoney !== scope.row.changdiNewMoney\" class=\"original-price\">\n                  ¥{{ scope.row.changdiOldMoney }}\n                </span>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"状态\" width=\"100\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.shangxiaTypes === 1 ? 'success' : 'danger'\">\n                {{ scope.row.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"280\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"primary\" size=\"mini\" @click=\"bookVenue(scope.row)\"\n                         :disabled=\"scope.row.shangxiaTypes !== 1\">\n                立即预约\n              </el-button>\n              <el-button\n                :type=\"isCollected(scope.row.id) ? 'warning' : 'success'\"\n                size=\"mini\"\n                @click=\"toggleCollection(scope.row)\"\n                :loading=\"scope.row.collectionLoading\">\n                <i :class=\"isCollected(scope.row.id) ? 'el-icon-star-on' : 'el-icon-star-off'\"></i>\n                {{ isCollected(scope.row.id) ? '已收藏' : '收藏' }}\n              </el-button>\n              <el-button type=\"text\" size=\"mini\" @click=\"viewVenueDetails(scope.row)\">\n                查看详情\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          @size-change=\"sizeChangeHandle\"\n          @current-change=\"currentChangeHandle\"\n          :current-page=\"pageIndex\"\n          :page-sizes=\"[12, 24, 48]\"\n          :page-size=\"pageSize\"\n          :total=\"totalPage\"\n          layout=\"total, sizes, prev, pager, next, jumper\">\n        </el-pagination>\n      </div>\n    </div>\n\n    <!-- 预约对话框 -->\n    <booking-dialog\n      :visible.sync=\"bookingDialogVisible\"\n      :venue=\"selectedVenue\"\n      @booking-success=\"onBookingSuccess\">\n    </booking-dialog>\n\n    <!-- 场地详情对话框 -->\n    <venue-details-dialog\n      v-if=\"detailsDialogVisible\"\n      :visible.sync=\"detailsDialogVisible\"\n      :venue=\"selectedVenue\"\n      @book-venue=\"bookVenue\">\n    </venue-details-dialog>\n  </div>\n</template>\n\n<script>\nimport BookingDialog from './components/BookingDialog.vue'\nimport VenueDetailsDialog from './components/VenueDetailsDialog.vue'\nimport collectionStorage from '@/utils/collection-storage'\n\nexport default {\n  name: 'ChangdiBooking',\n  components: {\n    BookingDialog,\n    VenueDetailsDialog\n  },\n  data() {\n    return {\n      // 统计数据\n      totalVenues: 0,\n      availableSlots: 0,\n      myBookings: 0,\n      \n      // 搜索表单\n      searchForm: {\n        changdiTypes: '',\n        bookingDate: '',\n        priceRange: ''\n      },\n      \n      // 视图模式\n      viewMode: 'grid',\n      \n      // 数据列表\n      dataList: [],\n      pageIndex: 1,\n      pageSize: 12,\n      totalPage: 0,\n      dataListLoading: false,\n      searchLoading: false,\n      \n      // 选项数据\n      changdiTypesOptions: [],\n      \n      // 对话框状态\n      bookingDialogVisible: false,\n      detailsDialogVisible: false,\n      selectedVenue: null,\n      \n      // 日期选择器配置\n      datePickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期\n        }\n      }\n    }\n  },\n  \n  created() {\n    this.init()\n    this.getDataList()\n    this.getStats()\n  },\n  \n  methods: {\n    // 初始化\n    init() {\n      this.getChangdiTypesOptions()\n    },\n    \n    // 获取场地类型选项\n    getChangdiTypesOptions() {\n      this.$http({\n        url: 'dictionary/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 100,\n          dicCode: 'changdi_types'\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.changdiTypesOptions = data.data.list || []\n        }\n      })\n    },\n    \n    // 获取统计数据\n    getStats() {\n      // 获取总场地数\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalVenues = data.data.total || 0\n        }\n      })\n      \n      // 获取我的预约数（如果是用户登录）\n      const role = this.$storage.get('role')\n      if (role === '用户') {\n        this.$http({\n          url: 'changdiOrder/page',\n          method: 'get',\n          params: { page: 1, limit: 1 }\n        }).then(({ data }) => {\n          if (data && data.code === 0) {\n            this.myBookings = data.data.total || 0\n          }\n        })\n      }\n    },\n\n    // 获取场地列表\n    getDataList() {\n      this.dataListLoading = true\n      const params = {\n        page: this.pageIndex,\n        limit: this.pageSize,\n        shangxiaTypes: 1 // 只获取上架的场地\n      }\n\n      // 添加搜索条件\n      if (this.searchForm.changdiTypes) {\n        params.changdiTypes = this.searchForm.changdiTypes\n      }\n\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: params\n      }).then(({ data }) => {\n        this.dataListLoading = false\n        if (data && data.code === 0) {\n          this.dataList = data.data.list || []\n          this.totalPage = data.data.total || 0\n          this.availableSlots = this.dataList.length * 8 // 假设每个场地有8个时段\n        } else {\n          this.$message.error(data.msg || '获取场地列表失败')\n        }\n      }).catch(() => {\n        this.dataListLoading = false\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n\n    // 搜索场地\n    searchVenues() {\n      this.searchLoading = true\n      this.pageIndex = 1\n      setTimeout(() => {\n        this.getDataList()\n        this.searchLoading = false\n      }, 500)\n    },\n\n    // 重置搜索\n    resetSearch() {\n      this.searchForm = {\n        changdiTypes: '',\n        bookingDate: '',\n        priceRange: ''\n      }\n      this.pageIndex = 1\n      this.getDataList()\n    },\n\n    // 预约场地\n    bookVenue(venue) {\n      console.log('预约场地被点击', venue)\n      const role = this.$storage.get('role')\n      const userId = this.$storage.get('userId')\n      const sessionTable = this.$storage.get('sessionTable')\n\n      console.log('用户信息:', { role, userId, sessionTable })\n\n      // 检查登录状态\n      if (!role) {\n        this.$message.warning('请先登录账户')\n        this.$router.push('/login')\n        return\n      }\n\n      // 检查用户角色 - 允许用户和管理员预约\n      if (role !== '用户' && role !== '管理员') {\n        this.$message.warning('请使用有效的账户预约场地，当前角色：' + role)\n        return\n      }\n\n      // 显示预约对话框\n      this.selectedVenue = venue\n      this.bookingDialogVisible = true\n    },\n\n    // 查看场地详情\n    viewVenueDetails(venue) {\n      this.selectedVenue = venue\n      this.detailsDialogVisible = true\n    },\n\n    // 预约成功回调\n    onBookingSuccess() {\n      this.$message.success('预约成功！')\n      this.getStats() // 刷新统计数据\n    },\n\n    // 分页处理\n    sizeChangeHandle(val) {\n      this.pageSize = val\n      this.pageIndex = 1\n      this.getDataList()\n    },\n\n    currentChangeHandle(val) {\n      this.pageIndex = val\n      this.getDataList()\n    },\n\n    // 检查是否已收藏\n    isCollected(venueId) {\n      return collectionStorage.isCollected(venueId)\n    },\n\n    // 切换收藏状态\n    async toggleCollection(venue) {\n      // 检查登录状态\n      if (!this.$storage.get('role')) {\n        this.$message.warning('请先登录账户')\n        this.$router.push('/login')\n        return\n      }\n\n      // 设置加载状态\n      this.$set(venue, 'collectionLoading', true)\n\n      try {\n        if (this.isCollected(venue.id)) {\n          // 取消收藏\n          await this.removeCollection(venue)\n        } else {\n          // 添加收藏\n          await this.addCollection(venue)\n        }\n      } finally {\n        this.$set(venue, 'collectionLoading', false)\n      }\n    },\n\n    // 添加收藏\n    async addCollection(venue) {\n      try {\n        // 先尝试服务器API\n        const response = await this.$http({\n          url: 'changdiCollection/save',\n          method: 'post',\n          data: {\n            yonghuId: this.$storage.get('userid'),\n            changdiId: venue.id,\n            changdiCollectionTypes: 1\n          }\n        })\n\n        if (response.data && response.data.code === 0) {\n          this.$message.success('收藏成功！')\n        } else {\n          throw new Error(response.data.msg || '收藏失败')\n        }\n      } catch (error) {\n        console.log('服务器收藏失败，使用本地存储:', error.message)\n\n        // 使用本地存储\n        const result = collectionStorage.addCollection({\n          id: venue.id,\n          changdiName: venue.changdiName,\n          changdiPhoto: venue.changdiPhoto,\n          changdiNewMoney: venue.changdiNewMoney,\n          changdiValue: venue.changdiValue\n        })\n\n        if (result.success) {\n          this.$message.success('收藏成功！（已保存到本地）')\n        } else {\n          this.$message.error(result.message)\n        }\n      }\n    },\n\n    // 移除收藏\n    async removeCollection(venue) {\n      try {\n        // 先查询收藏记录\n        const listResponse = await this.$http({\n          url: 'changdiCollection/list',\n          method: 'get',\n          params: {\n            page: 1,\n            limit: 1,\n            changdiId: venue.id,\n            yonghuId: this.$storage.get('userid')\n          }\n        })\n\n        if (listResponse.data && listResponse.data.code === 0 && listResponse.data.data.list.length > 0) {\n          const collectionId = listResponse.data.data.list[0].id\n\n          // 删除收藏\n          const deleteResponse = await this.$http({\n            url: 'changdiCollection/delete',\n            method: 'post',\n            data: [collectionId]\n          })\n\n          if (deleteResponse.data && deleteResponse.data.code === 0) {\n            this.$message.success('取消收藏成功！')\n          } else {\n            throw new Error(deleteResponse.data.msg || '取消收藏失败')\n          }\n        } else {\n          throw new Error('未找到收藏记录')\n        }\n      } catch (error) {\n        console.log('服务器取消收藏失败，使用本地存储:', error.message)\n\n        // 使用本地存储\n        const result = collectionStorage.removeCollection(venue.id)\n\n        if (result.success) {\n          this.$message.success('取消收藏成功！（本地存储）')\n        } else {\n          this.$message.error(result.message)\n        }\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.booking-container {\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: calc(100vh - 84px);\n\n  .page-header {\n    margin-bottom: 30px;\n\n    .header-content {\n      .title-section {\n        text-align: center;\n        margin-bottom: 30px;\n\n        h1 {\n          font-size: 32px;\n          color: #2c3e50;\n          margin: 0 0 10px 0;\n\n          i {\n            color: #00c292;\n            margin-right: 15px;\n          }\n        }\n\n        p {\n          color: #909399;\n          font-size: 18px;\n          margin: 0;\n        }\n      }\n\n      .stats-cards {\n        display: flex;\n        justify-content: center;\n        gap: 30px;\n\n        .stat-card {\n          background: white;\n          border-radius: 12px;\n          padding: 25px;\n          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n          display: flex;\n          align-items: center;\n          gap: 20px;\n          min-width: 200px;\n          transition: all 0.3s ease;\n\n          &:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n          }\n\n          .stat-icon {\n            width: 60px;\n            height: 60px;\n            border-radius: 50%;\n            background: linear-gradient(45deg, #00c292, #00a085);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n\n            i {\n              font-size: 24px;\n              color: white;\n            }\n          }\n\n          .stat-info {\n            h3 {\n              font-size: 28px;\n              font-weight: 700;\n              color: #2c3e50;\n              margin: 0 0 5px 0;\n            }\n\n            p {\n              color: #909399;\n              margin: 0;\n              font-size: 14px;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .search-section {\n    margin-bottom: 30px;\n\n    .search-card {\n      border-radius: 12px;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n\n      .search-form {\n        .search-item {\n          label {\n            display: block;\n            margin-bottom: 8px;\n            font-weight: 600;\n            color: #2c3e50;\n            font-size: 14px;\n          }\n\n          ::v-deep .el-input__inner,\n          ::v-deep .el-select .el-input__inner {\n            border-radius: 8px;\n            border: 2px solid #e8f4f8;\n            transition: all 0.3s ease;\n\n            &:focus {\n              border-color: #00c292;\n              box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.1);\n            }\n          }\n        }\n\n        .search-actions {\n          display: flex;\n          gap: 10px;\n          align-items: end;\n          height: 100%;\n          padding-top: 22px;\n\n          .el-button {\n            border-radius: 8px;\n            font-weight: 600;\n\n            &.el-button--primary {\n              background: linear-gradient(45deg, #00c292, #00a085);\n              border: none;\n\n              &:hover {\n                background: linear-gradient(45deg, #00a085, #008f75);\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .venues-section {\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 25px;\n\n      h2 {\n        font-size: 24px;\n        color: #2c3e50;\n        margin: 0;\n      }\n\n      .view-toggle {\n        ::v-deep .el-radio-group {\n          .el-radio-button__inner {\n            border-radius: 8px;\n            border: 2px solid #e8f4f8;\n            background: white;\n            color: #666;\n\n            &:hover {\n              color: #00c292;\n              border-color: #00c292;\n            }\n          }\n\n          .el-radio-button__orig-radio:checked + .el-radio-button__inner {\n            background: #00c292;\n            border-color: #00c292;\n            color: white;\n          }\n        }\n      }\n    }\n\n    .venues-grid {\n      .empty-state {\n        text-align: center;\n        padding: 80px 20px;\n        color: #909399;\n\n        i {\n          font-size: 64px;\n          margin-bottom: 20px;\n          color: #ddd;\n        }\n\n        h3 {\n          font-size: 20px;\n          margin: 0 0 10px 0;\n        }\n\n        p {\n          margin: 0;\n          font-size: 14px;\n        }\n      }\n\n      .venue-cards {\n        display: grid;\n        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n        gap: 25px;\n\n        .venue-card {\n          background: white;\n          border-radius: 16px;\n          overflow: hidden;\n          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n          transition: all 0.3s ease;\n\n          &:hover {\n            transform: translateY(-8px);\n            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n          }\n\n          .venue-image {\n            position: relative;\n            height: 200px;\n            overflow: hidden;\n\n            img {\n              width: 100%;\n              height: 100%;\n              object-fit: cover;\n              transition: transform 0.3s ease;\n            }\n\n            &:hover img {\n              transform: scale(1.05);\n            }\n\n            .venue-status {\n              position: absolute;\n              top: 15px;\n              right: 15px;\n              padding: 6px 12px;\n              border-radius: 20px;\n              font-size: 12px;\n              font-weight: 600;\n\n              &.available {\n                background: rgba(0, 194, 146, 0.9);\n                color: white;\n              }\n\n              &.unavailable {\n                background: rgba(245, 108, 108, 0.9);\n                color: white;\n              }\n            }\n          }\n\n          .venue-info {\n            padding: 25px;\n\n            h3 {\n              font-size: 20px;\n              font-weight: 600;\n              color: #2c3e50;\n              margin: 0 0 15px 0;\n            }\n\n            .venue-details {\n              margin-bottom: 20px;\n\n              .detail-item {\n                display: flex;\n                align-items: center;\n                margin-bottom: 8px;\n                color: #666;\n                font-size: 14px;\n\n                i {\n                  color: #00c292;\n                  margin-right: 8px;\n                  width: 16px;\n                }\n              }\n            }\n\n            .venue-price {\n              margin-bottom: 20px;\n\n              .current-price {\n                font-size: 24px;\n                font-weight: 700;\n                color: #00c292;\n              }\n\n              .original-price {\n                font-size: 16px;\n                color: #999;\n                text-decoration: line-through;\n                margin-left: 10px;\n              }\n            }\n\n            .venue-actions {\n              display: flex;\n              gap: 10px;\n\n              .el-button {\n                border-radius: 8px;\n                font-weight: 600;\n\n                &.el-button--primary {\n                  background: linear-gradient(45deg, #00c292, #00a085);\n                  border: none;\n                  flex: 1;\n\n                  &:hover:not(:disabled) {\n                    background: linear-gradient(45deg, #00a085, #008f75);\n                  }\n\n                  &:disabled {\n                    background: #ddd;\n                    color: #999;\n                  }\n                }\n\n                &.el-button--text {\n                  color: #00c292;\n\n                  &:hover {\n                    color: #00a085;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .venues-list {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n\n      ::v-deep .el-table {\n        .price-cell {\n          .current-price {\n            font-size: 16px;\n            font-weight: 600;\n            color: #00c292;\n          }\n\n          .original-price {\n            font-size: 12px;\n            color: #999;\n            text-decoration: line-through;\n            display: block;\n          }\n        }\n\n        .el-button {\n          border-radius: 6px;\n          font-weight: 600;\n\n          &.el-button--primary {\n            background: linear-gradient(45deg, #00c292, #00a085);\n            border: none;\n\n            &:hover:not(:disabled) {\n              background: linear-gradient(45deg, #00a085, #008f75);\n            }\n          }\n        }\n      }\n    }\n\n    .pagination-wrapper {\n      margin-top: 30px;\n      text-align: center;\n\n      ::v-deep .el-pagination {\n        .el-pager li {\n          border-radius: 8px;\n          margin: 0 4px;\n\n          &.active {\n            background: #00c292;\n            border-color: #00c292;\n          }\n        }\n\n        .btn-prev,\n        .btn-next {\n          border-radius: 8px;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .booking-container {\n    .page-header .header-content .stats-cards {\n      flex-direction: column;\n      align-items: center;\n      gap: 20px;\n    }\n\n    .venues-section .venue-cards {\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .booking-container {\n    padding: 15px;\n\n    .search-section .search-form {\n      .el-row {\n        flex-direction: column;\n\n        .el-col {\n          width: 100%;\n          margin-bottom: 15px;\n        }\n      }\n    }\n\n    .venues-section {\n      .section-header {\n        flex-direction: column;\n        gap: 15px;\n        text-align: center;\n      }\n\n      .venue-cards {\n        grid-template-columns: 1fr;\n      }\n    }\n  }\n}\n</style>\n"]}]}