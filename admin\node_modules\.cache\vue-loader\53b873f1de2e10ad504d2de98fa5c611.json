{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\gonggao\\list.vue?vue&type=template&id=4c563f5b&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\gonggao\\list.vue", "mtime": 1750591970322}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}