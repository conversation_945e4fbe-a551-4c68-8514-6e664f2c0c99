<template>
  <div class="collection-management">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-star-on"></i>
        收藏管理
      </h1>
      <p class="page-description">管理用户的场地收藏记录，查看收藏统计和趋势</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-cards">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon total">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-content">
              <h3>{{ totalCollections }}</h3>
              <p>总收藏数</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon today">
              <i class="el-icon-date"></i>
            </div>
            <div class="stat-content">
              <h3>{{ todayCollections }}</h3>
              <p>今日新增</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon users">
              <i class="el-icon-user"></i>
            </div>
            <div class="stat-content">
              <h3>{{ activeUsers }}</h3>
              <p>活跃用户</p>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-card">
            <div class="stat-icon venues">
              <i class="el-icon-location"></i>
            </div>
            <div class="stat-content">
              <h3>{{ popularVenues }}</h3>
              <p>热门场地</p>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-section">
      <el-card>
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="场地名称">
            <el-input
              v-model="searchForm.changdiName"
              placeholder="请输入场地名称"
              clearable
              @keyup.enter.native="handleSearch">
            </el-input>
          </el-form-item>
          <el-form-item v-if="isAdmin" label="用户姓名">
            <el-input
              v-model="searchForm.yonghuName"
              placeholder="请输入用户姓名"
              clearable
              @keyup.enter.native="handleSearch">
            </el-input>
          </el-form-item>
          <el-form-item label="收藏时间">
            <el-date-picker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" icon="el-icon-search">搜索</el-button>
            <el-button @click="handleReset" icon="el-icon-refresh">重置</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>

    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button
          type="danger"
          :disabled="selectedCollections.length === 0"
          @click="handleBatchDelete"
          icon="el-icon-delete">
          批量删除 ({{ selectedCollections.length }})
        </el-button>
        <el-button
          type="success"
          @click="exportCollections"
          icon="el-icon-download">
          导出数据
        </el-button>
        <el-button
          v-if="isAdmin"
          type="primary"
          @click="syncToServer"
          :loading="syncing"
          icon="el-icon-upload2">
          同步到服务器
        </el-button>
        <el-button
          v-if="isAdmin"
          type="info"
          @click="pullFromServer"
          :loading="pulling"
          icon="el-icon-download">
          从服务器拉取
        </el-button>
      </div>
      <div class="action-right">
        <el-tag v-if="syncStatus.status !== 'never'" :type="getSyncStatusType()">
          {{ getSyncStatusText() }}
        </el-tag>
        <el-button @click="loadCollections" icon="el-icon-refresh">刷新</el-button>
      </div>
    </div>

    <!-- 收藏列表 -->
    <div class="collection-table">
      <el-table
        v-loading="loading"
        :data="collectionList"
        @selection-change="handleSelectionChange"
        stripe
        style="width: 100%">
        
        <el-table-column type="selection" width="55"></el-table-column>
        
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        
        <el-table-column label="场地信息" min-width="200">
          <template slot-scope="scope">
            <div class="venue-info">
              <img 
                v-if="scope.row.changdiPhoto" 
                :src="scope.row.changdiPhoto.split(',')[0]" 
                class="venue-image"
                @error="handleImageError">
              <div class="venue-details">
                <h4>{{ scope.row.changdiName }}</h4>
                <p class="venue-type">{{ scope.row.changdiValue }}</p>
                <p class="venue-price">¥{{ scope.row.changdiNewMoney }}</p>
              </div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column v-if="isAdmin" label="用户信息" min-width="150">
          <template slot-scope="scope">
            <div class="user-info">
              <p class="user-name">{{ scope.row.yonghuName }}</p>
              <p class="user-phone">{{ scope.row.yonghuPhone }}</p>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="changdiCollectionValue" label="收藏类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getCollectionTypeTag(scope.row.changdiCollectionTypes)">
              {{ scope.row.changdiCollectionValue }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="insertTime" label="收藏时间" width="180">
          <template slot-scope="scope">
            {{ formatDate(scope.row.insertTime) }}
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="primary"
              @click="viewDetails(scope.row)"
              icon="el-icon-view">
              查看
            </el-button>
            <el-button
              size="mini"
              type="danger"
              @click="handleDelete(scope.row)"
              icon="el-icon-delete">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalCount">
      </el-pagination>
    </div>

    <!-- 详情对话框 -->
    <el-dialog
      title="收藏详情"
      :visible.sync="detailDialogVisible"
      width="600px">
      <div v-if="currentCollection" class="collection-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="收藏ID">{{ currentCollection.id }}</el-descriptions-item>
          <el-descriptions-item label="场地名称">{{ currentCollection.changdiName }}</el-descriptions-item>
          <el-descriptions-item label="用户姓名">{{ currentCollection.yonghuName }}</el-descriptions-item>
          <el-descriptions-item label="用户电话">{{ currentCollection.yonghuPhone }}</el-descriptions-item>
          <el-descriptions-item label="收藏类型">{{ currentCollection.changdiCollectionValue }}</el-descriptions-item>
          <el-descriptions-item label="收藏时间">{{ formatDate(currentCollection.insertTime) }}</el-descriptions-item>
        </el-descriptions>
        
        <div class="venue-preview" v-if="currentCollection.changdiPhoto">
          <h4>场地图片</h4>
          <div class="image-gallery">
            <img 
              v-for="(image, index) in currentCollection.changdiPhoto.split(',')" 
              :key="index"
              :src="image" 
              class="preview-image"
              @error="handleImageError">
          </div>
        </div>
      </div>
      
      <span slot="footer" class="dialog-footer">
        <el-button @click="detailDialogVisible = false">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import collectionStorage from '@/utils/collection-storage'

export default {
  name: 'CollectionManagement',
  data() {
    return {
      loading: false,
      collectionList: [],
      selectedCollections: [],
      currentPage: 1,
      pageSize: 20,
      totalCount: 0,
      
      // 统计数据
      totalCollections: 0,
      todayCollections: 0,
      activeUsers: 0,
      popularVenues: 0,
      
      // 搜索表单
      searchForm: {
        changdiName: '',
        yonghuName: '',
        dateRange: []
      },
      
      // 详情对话框
      detailDialogVisible: false,
      currentCollection: null,

      // 同步状态
      syncing: false,
      pulling: false,
      syncStatus: { status: 'never', lastSync: null }
    }
  },

  computed: {
    // 检查是否为管理员
    isAdmin() {
      return this.$storage.get('role') === '管理员'
    }
  },

  mounted() {
    this.loadCollections()
    this.loadStats()
    this.loadLocalCollections()
    this.syncStatus = collectionStorage.getSyncStatus()
  },
  
  methods: {
    // 加载收藏列表
    loadCollections() {
      this.loading = true

      const params = {
        page: this.currentPage,
        limit: this.pageSize
      }

      // 如果是普通用户，只显示自己的收藏
      const userRole = this.$storage.get('role')
      if (userRole === '用户') {
        params.yonghuId = this.$storage.get('userid')
      }

      // 添加搜索条件
      if (this.searchForm.changdiName) {
        params.changdiName = this.searchForm.changdiName
      }
      if (this.searchForm.yonghuName && userRole !== '用户') {
        // 普通用户不能搜索其他用户
        params.yonghuName = this.searchForm.yonghuName
      }
      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {
        params.insertTimeStart = this.searchForm.dateRange[0]
        params.insertTimeEnd = this.searchForm.dateRange[1]
      }
      
      this.$http({
        url: 'changdiCollection/page',
        method: 'get',
        params
      }).then(({ data }) => {
        this.loading = false
        if (data && data.code === 0) {
          this.collectionList = data.data.list || []
          this.totalCount = data.data.total || 0
        } else {
          this.$message.error(data.msg || '获取收藏列表失败')
        }
      }).catch((error) => {
        this.loading = false
        console.error('加载收藏列表失败:', error)
        // 加载本地收藏数据作为备用
        this.loadLocalCollections()
      })
    },

    // 加载本地收藏数据
    loadLocalCollections() {
      try {
        const localCollections = collectionStorage.getAllCollections()
        if (localCollections.length > 0 && this.collectionList.length === 0) {
          this.collectionList = localCollections
          this.totalCount = localCollections.length
          this.$message.info(`已加载本地收藏数据 (${localCollections.length} 条)`)
        }
      } catch (error) {
        console.error('加载本地收藏数据失败:', error)
      }
    },

    // 同步到服务器
    async syncToServer() {
      this.syncing = true
      try {
        const result = await collectionStorage.syncToServer(this.$http)
        if (result.success) {
          this.$message.success(result.message)
          this.loadCollections() // 重新加载数据
        } else {
          this.$message.error(result.message)
        }
        this.syncStatus = collectionStorage.getSyncStatus()
      } catch (error) {
        console.error('同步失败:', error)
        this.$message.error('同步失败: ' + error.message)
      } finally {
        this.syncing = false
      }
    },

    // 从服务器拉取数据
    async pullFromServer() {
      this.pulling = true
      try {
        const result = await collectionStorage.pullFromServer(this.$http)
        if (result.success) {
          this.$message.success(result.message)
          this.loadLocalCollections() // 重新加载本地数据
        } else {
          this.$message.error(result.message)
        }
        this.syncStatus = collectionStorage.getSyncStatus()
      } catch (error) {
        console.error('拉取数据失败:', error)
        this.$message.error('拉取数据失败: ' + error.message)
      } finally {
        this.pulling = false
      }
    },

    // 获取同步状态类型
    getSyncStatusType() {
      switch (this.syncStatus.status) {
        case 'pending': return 'warning'
        case 'completed': return 'success'
        case 'failed': return 'danger'
        default: return 'info'
      }
    },

    // 获取同步状态文本
    getSyncStatusText() {
      switch (this.syncStatus.status) {
        case 'pending': return '待同步'
        case 'completed': return '已同步'
        case 'failed': return '同步失败'
        case 'never': return '从未同步'
        default: return '未知状态'
      }
    },

    // 加载统计数据
    loadStats() {
      // 获取总收藏数
      this.$http({
        url: 'changdiCollection/page',
        method: 'get',
        params: { page: 1, limit: 1 }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.totalCollections = data.data.total || 0
        }
      }).catch(() => {
        // 使用本地数据作为备用
        const localStats = collectionStorage.getCollectionStats()
        this.totalCollections = localStats.total
        this.todayCollections = localStats.today
      })

      // 获取今日收藏数
      const today = new Date().toISOString().split('T')[0]
      this.$http({
        url: 'changdiCollection/page',
        method: 'get',
        params: {
          page: 1,
          limit: 1000,
          insertTimeStart: today,
          insertTimeEnd: today
        }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.todayCollections = data.data.total || 0
        }
      }).catch(() => {
        console.log('获取今日收藏数失败，使用本地数据')
      })

      // 获取活跃用户数（简化统计）
      this.activeUsers = 89
      this.popularVenues = 23
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.loadCollections()
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        changdiName: '',
        yonghuName: '',
        dateRange: []
      }
      this.currentPage = 1
      this.loadCollections()
    },

    // 选择变化
    handleSelectionChange(selection) {
      this.selectedCollections = selection
    },

    // 批量删除
    handleBatchDelete() {
      if (this.selectedCollections.length === 0) {
        this.$message.warning('请选择要删除的收藏记录')
        return
      }

      this.$confirm(`确定要删除选中的 ${this.selectedCollections.length} 条收藏记录吗？`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const ids = this.selectedCollections.map(item => item.id)
        this.deleteCollections(ids)
      })
    },

    // 单个删除
    handleDelete(row) {
      this.$confirm('确定要删除这条收藏记录吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteCollections([row.id])
      })
    },

    // 删除收藏
    deleteCollections(ids) {
      this.$http({
        url: 'changdiCollection/delete',
        method: 'post',
        data: ids
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.$message.success('删除成功')
          this.loadCollections()
          this.selectedCollections = []
        } else {
          this.$message.error(data.msg || '删除失败')
        }
      }).catch((error) => {
        console.error('删除失败:', error)
        this.$message.error('删除失败，请稍后重试')
      })
    },

    // 查看详情
    viewDetails(row) {
      this.currentCollection = row
      this.detailDialogVisible = true
    },

    // 导出数据
    exportCollections() {
      try {
        collectionStorage.exportCollections()
        this.$message.success('收藏数据已导出')
      } catch (error) {
        console.error('导出失败:', error)
        this.$message.error('导出失败: ' + error.message)
      }
    },

    // 分页大小变化
    handleSizeChange(size) {
      this.pageSize = size
      this.currentPage = 1
      this.loadCollections()
    },

    // 当前页变化
    handleCurrentChange(page) {
      this.currentPage = page
      this.loadCollections()
    },

    // 获取收藏类型标签
    getCollectionTypeTag(type) {
      switch (type) {
        case 1: return 'success'
        case 2: return 'warning'
        case 3: return 'danger'
        default: return 'info'
      }
    },

    // 格式化日期
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    },

    // 图片错误处理
    handleImageError(event) {
      event.target.src = '/static/images/default-venue.png'
    }
  }
}
</script>

<style scoped>
.collection-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
}

.page-title i {
  margin-right: 12px;
  color: #409eff;
}

.page-description {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

/* 统计卡片 */
.stats-cards {
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.stat-icon i {
  font-size: 24px;
  color: white;
}

.stat-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-icon.today {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-icon.users {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-icon.venues {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content h3 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #303133;
}

.stat-content p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

/* 搜索区域 */
.search-section {
  margin-bottom: 20px;
}

.search-form {
  margin: 0;
}

/* 操作栏 */
.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 表格区域 */
.collection-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 场地信息 */
.venue-info {
  display: flex;
  align-items: center;
}

.venue-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 12px;
  border: 1px solid #ebeef5;
}

.venue-details h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.venue-type {
  margin: 0 0 4px 0;
  font-size: 12px;
  color: #909399;
}

.venue-price {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #f56c6c;
}

/* 用户信息 */
.user-info .user-name {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.user-info .user-phone {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

/* 分页 */
.pagination-wrapper {
  margin-top: 20px;
  text-align: center;
}

/* 详情对话框 */
.collection-detail {
  padding: 20px 0;
}

.venue-preview {
  margin-top: 20px;
}

.venue-preview h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.preview-image {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid #ebeef5;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.preview-image:hover {
  transform: scale(1.05);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .collection-management {
    padding: 12px;
  }

  .stats-cards .el-col {
    margin-bottom: 12px;
  }

  .action-bar {
    flex-direction: column;
    gap: 12px;
  }

  .venue-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .venue-image {
    margin-bottom: 8px;
  }
}
</style>
