<template>
  <el-dialog
    title="场地详情"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    class="details-dialog">
    
    <div class="details-content">
      <!-- 场地基本信息 -->
      <div class="venue-header">
        <div class="venue-image">
          <img :src="venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'" :alt="venue.changdiName">
        </div>
        <div class="venue-basic-info">
          <h2>{{ venue.changdiName }}</h2>
          <div class="venue-code">场地编号：{{ venue.changdiUuidNumber }}</div>
          <div class="venue-status">
            <el-tag :type="venue.shangxiaTypes === 1 ? 'success' : 'danger'">
              {{ venue.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}
            </el-tag>
          </div>
          <div class="venue-price">
            <span class="current-price">¥{{ venue.changdiNewMoney }}</span>
            <span v-if="venue.changdiOldMoney !== venue.changdiNewMoney" class="original-price">
              ¥{{ venue.changdiOldMoney }}
            </span>
            <span class="price-unit">/时段</span>
          </div>
        </div>
      </div>

      <!-- 详细信息 -->
      <div class="venue-details">
        <el-row :gutter="30">
          <el-col :span="12">
            <div class="detail-section">
              <h3><i class="el-icon-info"></i> 基本信息</h3>
              <div class="detail-list">
                <div class="detail-item">
                  <span class="label">场地类型：</span>
                  <span class="value">{{ venue.changdiValue }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">半全场：</span>
                  <span class="value">{{ venue.banquanValue }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">开放时间：</span>
                  <span class="value">{{ venue.shijianduan || '08:00-22:00' }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">点击次数：</span>
                  <span class="value">{{ venue.changdiClicknum }}次</span>
                </div>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="detail-section">
              <h3><i class="el-icon-star-on"></i> 推荐信息</h3>
              <div class="recommendation">
                <p>{{ venue.tuijian || '暂无推荐信息' }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 场地描述 -->
      <div class="venue-description" v-if="venue.changdiContent">
        <h3><i class="el-icon-document"></i> 场地介绍</h3>
        <div class="description-content" v-html="venue.changdiContent"></div>
      </div>

      <!-- 可预约时间段 -->
      <div class="available-slots">
        <h3><i class="el-icon-time"></i> 可预约时间段</h3>
        <div class="time-slots">
          <div v-for="slot in timeSlots" :key="slot.value" class="time-slot" :class="{ disabled: slot.disabled }">
            <div class="slot-time">{{ slot.label }}</div>
            <div class="slot-status">
              <el-tag :type="slot.disabled ? 'danger' : 'success'" size="mini">
                {{ slot.disabled ? '已预约' : '可预约' }}
              </el-tag>
            </div>
          </div>
        </div>
      </div>

      <!-- 预约须知 -->
      <div class="booking-notice">
        <h3><i class="el-icon-warning"></i> 预约须知</h3>
        <ul class="notice-list">
          <li>请提前至少1小时预约，当天预约需要电话确认</li>
          <li>预约成功后请按时到场，迟到超过15分钟将自动取消</li>
          <li>如需取消预约，请提前2小时联系客服</li>
          <li>场地内禁止吸烟，请爱护场地设施</li>
          <li>运动时请注意安全，建议穿着运动服装和运动鞋</li>
        </ul>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="bookVenue" :disabled="venue.shangxiaTypes !== 1">
        <i class="el-icon-date"></i>
        立即预约
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'VenueDetailsDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    venue: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      dialogVisible: false,
      
      timeSlots: [
        { label: '08:00-10:00', value: '08:00-10:00', disabled: false },
        { label: '10:00-12:00', value: '10:00-12:00', disabled: true },
        { label: '12:00-14:00', value: '12:00-14:00', disabled: false },
        { label: '14:00-16:00', value: '14:00-16:00', disabled: false },
        { label: '16:00-18:00', value: '16:00-18:00', disabled: true },
        { label: '18:00-20:00', value: '18:00-20:00', disabled: false },
        { label: '20:00-22:00', value: '20:00-22:00', disabled: false }
      ]
    }
  },
  
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.loadVenueDetails()
      }
    },
    
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  
  methods: {
    // 加载场地详情
    loadVenueDetails() {
      // 这里可以加载更详细的场地信息
      // 包括已预约的时间段等
    },
    
    // 预约场地
    bookVenue() {
      const role = this.$storage.get('role')
      if (!role || role !== '用户') {
        this.$message.warning('请先登录用户账户')
        this.$router.push('/login')
        return
      }

      this.$emit('book-venue', this.venue)
      this.handleClose()
    },
    
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .details-dialog {
  .el-dialog__header {
    background: linear-gradient(45deg, #00c292, #00a085);
    color: white;
    padding: 20px 30px;
    
    .el-dialog__title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__close {
      color: white;
      font-size: 20px;
    }
  }
  
  .el-dialog__body {
    padding: 30px;
    max-height: 70vh;
    overflow-y: auto;
  }
}

.details-content {
  .venue-header {
    display: flex;
    gap: 25px;
    margin-bottom: 30px;
    padding: 25px;
    background: #f8fffe;
    border-radius: 12px;
    border: 1px solid rgba(0, 194, 146, 0.1);
    
    .venue-image {
      flex: 0 0 200px;
      
      img {
        width: 200px;
        height: 150px;
        object-fit: cover;
        border-radius: 8px;
      }
    }
    
    .venue-basic-info {
      flex: 1;
      
      h2 {
        font-size: 24px;
        color: #2c3e50;
        margin: 0 0 15px 0;
      }
      
      .venue-code {
        color: #666;
        margin-bottom: 10px;
        font-size: 14px;
      }
      
      .venue-status {
        margin-bottom: 15px;
      }
      
      .venue-price {
        .current-price {
          font-size: 28px;
          font-weight: 700;
          color: #00c292;
        }
        
        .original-price {
          font-size: 18px;
          color: #999;
          text-decoration: line-through;
          margin-left: 10px;
        }
        
        .price-unit {
          font-size: 14px;
          color: #666;
          margin-left: 5px;
        }
      }
    }
  }
  
  .venue-details {
    margin-bottom: 30px;
    
    .detail-section {
      h3 {
        font-size: 18px;
        color: #2c3e50;
        margin-bottom: 20px;
        
        i {
          color: #00c292;
          margin-right: 8px;
        }
      }
      
      .detail-list {
        .detail-item {
          display: flex;
          margin-bottom: 12px;
          
          .label {
            flex: 0 0 80px;
            color: #666;
            font-size: 14px;
          }
          
          .value {
            color: #2c3e50;
            font-size: 14px;
          }
        }
      }
      
      .recommendation {
        p {
          color: #666;
          line-height: 1.6;
          margin: 0;
        }
      }
    }
  }
  
  .venue-description {
    margin-bottom: 30px;
    
    h3 {
      font-size: 18px;
      color: #2c3e50;
      margin-bottom: 15px;
      
      i {
        color: #00c292;
        margin-right: 8px;
      }
    }
    
    .description-content {
      color: #666;
      line-height: 1.6;
      padding: 20px;
      background: #f8f9fa;
      border-radius: 8px;
    }
  }
  
  .available-slots {
    margin-bottom: 30px;
    
    h3 {
      font-size: 18px;
      color: #2c3e50;
      margin-bottom: 20px;
      
      i {
        color: #00c292;
        margin-right: 8px;
      }
    }
    
    .time-slots {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 15px;
      
      .time-slot {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px;
        background: white;
        border: 2px solid #e8f4f8;
        border-radius: 8px;
        transition: all 0.3s ease;
        
        &:not(.disabled):hover {
          border-color: #00c292;
          background: #f8fffe;
        }
        
        &.disabled {
          background: #f5f5f5;
          border-color: #ddd;
        }
        
        .slot-time {
          font-weight: 600;
          color: #2c3e50;
        }
      }
    }
  }
  
  .booking-notice {
    h3 {
      font-size: 18px;
      color: #2c3e50;
      margin-bottom: 15px;
      
      i {
        color: #f56c6c;
        margin-right: 8px;
      }
    }
    
    .notice-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        color: #666;
        line-height: 1.6;
        margin-bottom: 8px;
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    border-radius: 8px;
    font-weight: 600;
    
    &.el-button--primary {
      background: linear-gradient(45deg, #00c292, #00a085);
      border: none;
      
      &:hover:not(:disabled) {
        background: linear-gradient(45deg, #00a085, #008f75);
      }
      
      &:disabled {
        background: #ddd;
        color: #999;
      }
    }
  }
}
</style>
