{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\login.vue", "mtime": 1750594624583}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["menu", "data", "loading", "showCaptcha", "rulesForm", "username", "password", "role", "code", "menus", "tableName", "codes", "num", "color", "rotate", "size", "mounted", "list", "created", "setInputColor", "getRandCode", "methods", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "style", "backgroundColor", "height", "lineHeight", "borderRadius", "setTimeout", "register", "$storage", "set", "$router", "push", "path", "login", "_this", "i", "$message", "error", "toLowerCase", "length", "<PERSON><PERSON><PERSON>", "$http", "url", "concat", "method", "then", "_ref", "token", "message", "type", "duration", "onClose", "replace", "msg", "catch", "len", "arguments", "undefined", "randomString", "chars", "colors", "sizes", "output", "key", "Math", "floor", "random", "j", "plus"], "sources": ["src/views/login.vue"], "sourcesContent": ["<template>\r\n    <div class=\"login-container\">\r\n        <!-- 背景装饰 -->\r\n        <div class=\"background-decoration\">\r\n            <div class=\"decoration-circle circle-1\"></div>\r\n            <div class=\"decoration-circle circle-2\"></div>\r\n            <div class=\"decoration-circle circle-3\"></div>\r\n        </div>\r\n\r\n        <!-- 主要内容区域 -->\r\n        <div class=\"main-content\">\r\n            <!-- 左侧信息区域 -->\r\n            <div class=\"info-section\">\r\n                <div class=\"brand-info\">\r\n                    <div class=\"logo-container\">\r\n                        <i class=\"el-icon-trophy-1\"></i>\r\n                        <h1>体育馆管理系统</h1>\r\n                    </div>\r\n                    <p class=\"brand-description\">\r\n                        现代化的体育场馆预约管理平台<br>\r\n                        为您提供便捷、高效的场地预约服务\r\n                    </p>\r\n                    <div class=\"features\">\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-time\"></i>\r\n                            <span>24小时在线预约</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-location\"></i>\r\n                            <span>多场地智能管理</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>用户友好界面</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 右侧登录表单 -->\r\n            <div class=\"login-section\">\r\n                <div class=\"login-card\">\r\n                    <div class=\"card-header\">\r\n                        <h2>欢迎登录</h2>\r\n                        <p>请输入您的账户信息</p>\r\n                    </div>\r\n\r\n                    <el-form class=\"login-form\" :model=\"rulesForm\" @submit.native.prevent=\"login\">\r\n                        <el-form-item class=\"form-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-user input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.username\"\r\n                                    placeholder=\"请输入用户名\"\r\n                                    size=\"large\"\r\n                                    clearable>\r\n                                </el-input>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item class=\"form-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-lock input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.password\"\r\n                                    type=\"password\"\r\n                                    placeholder=\"请输入密码\"\r\n                                    size=\"large\"\r\n                                    show-password\r\n                                    clearable>\r\n                                </el-input>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item v-if=\"showCaptcha\" class=\"form-item captcha-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-key input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.code\"\r\n                                    placeholder=\"请输入验证码\"\r\n                                    size=\"large\"\r\n                                    clearable>\r\n                                </el-input>\r\n                                <div class=\"captcha-code\" @click=\"getRandCode(4)\">\r\n                                    <span v-for=\"(item, index) in codes\" :key=\"index\"\r\n                                          :style=\"{color:item.color,transform:item.rotate,fontSize:item.size}\">\r\n                                        {{ item.num }}\r\n                                    </span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item class=\"form-item role-item\">\r\n                            <label class=\"role-label\">选择角色</label>\r\n                            <div class=\"role-options\">\r\n                                <el-radio-group v-model=\"rulesForm.role\" class=\"role-group\">\r\n                                    <el-radio\r\n                                        v-for=\"item in menus\"\r\n                                        v-if=\"item.hasBackLogin=='是'\"\r\n                                        :key=\"item.roleName\"\r\n                                        :label=\"item.roleName\"\r\n                                        class=\"role-radio\">\r\n                                        {{ item.roleName }}\r\n                                    </el-radio>\r\n                                </el-radio-group>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            @click=\"login()\"\r\n                            class=\"login-button\"\r\n                            size=\"large\"\r\n                            :loading=\"loading\">\r\n                            <i class=\"el-icon-right\"></i>\r\n                            立即登录\r\n                        </el-button>\r\n\r\n                        <div class=\"form-footer\">\r\n                            <div class=\"register-link\" @click=\"register('yonghu')\">\r\n                                <i class=\"el-icon-user-solid\"></i>\r\n                                还没有账户？立即注册\r\n                            </div>\r\n                        </div>\r\n                    </el-form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import menu from \"@/utils/menu\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                showCaptcha: false, // 是否显示验证码\r\n                rulesForm: {\r\n                    username: \"\",\r\n                    password: \"\",\r\n                    role: \"\",\r\n                    code: '',\r\n                },\r\n                menus: [],\r\n                tableName: \"\",\r\n                codes: [{\r\n                    num: 1,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 2,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 3,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 4,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                }],\r\n            };\r\n        },\r\n        mounted() {\r\n            let menus = menu.list();\r\n            this.menus = menus;\r\n        },\r\n        created() {\r\n            this.setInputColor()\r\n            this.getRandCode()\r\n        },\r\n        methods: {\r\n            setInputColor(){\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{\r\n                        el.style.backgroundColor = \"rgba(255, 255, 255, 1)\"\r\n                        el.style.color = \"rgba(51, 51, 51, 1)\"\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                        el.style.borderRadius = \"30px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{\r\n                        el.style.color = \"rgba(59, 160, 215, 1)\"\r\n                    })\r\n                    setTimeout(()=>{\r\n                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{\r\n                            el.style.color = \"#fff\"\r\n                        })\r\n                    },350)\r\n                })\r\n\r\n            },\r\n            register(tableName){\r\n                this.$storage.set(\"loginTable\", tableName);\r\n                this.$router.push({path:'/register'})\r\n            },\r\n            // 登陆\r\n            login() {\r\n                // 验证码检查\r\n                if (this.showCaptcha) {\r\n                    let code = ''\r\n                    for(let i in this.codes) {\r\n                        code += this.codes[i].num\r\n                    }\r\n                    if (!this.rulesForm.code) {\r\n                        this.$message.error(\"请输入验证码\");\r\n                        return;\r\n                    }\r\n                    if (this.rulesForm.code.toLowerCase() != code.toLowerCase()) {\r\n                        this.$message.error(\"验证码输入有误\");\r\n                        this.getRandCode()\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // 表单验证\r\n                if (!this.rulesForm.username) {\r\n                    this.$message.error(\"请输入用户名\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.password) {\r\n                    this.$message.error(\"请输入密码\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.role) {\r\n                    this.$message.error(\"请选择角色\");\r\n                    return;\r\n                }\r\n\r\n                // 获取表名\r\n                let menus = this.menus;\r\n                for (let i = 0; i < menus.length; i++) {\r\n                    if (menus[i].roleName == this.rulesForm.role) {\r\n                        this.tableName = menus[i].tableName;\r\n                    }\r\n                }\r\n\r\n                // 开始登录\r\n                this.loading = true;\r\n                this.$http({\r\n                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n                    method: \"post\"\r\n                }).then(({ data }) => {\r\n                    this.loading = false;\r\n                    if (data && data.code === 0) {\r\n                        this.$storage.set(\"Token\", data.token);\r\n                        this.$storage.set(\"role\", this.rulesForm.role);\r\n                        this.$storage.set(\"sessionTable\", this.tableName);\r\n                        this.$storage.set(\"adminName\", this.rulesForm.username);\r\n\r\n                        this.$message({\r\n                            message: \"登录成功！\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                this.$router.replace({ path: \"/index/\" });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        this.$message.error(data.msg || \"登录失败，请检查用户名和密码\");\r\n                    }\r\n                }).catch(() => {\r\n                    this.loading = false;\r\n                    this.$message.error(\"网络错误，请稍后重试\");\r\n                });\r\n            },\r\n            getRandCode(len = 4){\r\n                this.randomString(len)\r\n            },\r\n            randomString(len = 4) {\r\n                let chars = [\r\n                    \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\",\r\n                    \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\",\r\n                    \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\",\r\n                    \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\",\r\n                    \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"0\", \"1\", \"2\",\r\n                    \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"\r\n                ]\r\n                let colors = [\"0\", \"1\", \"2\",\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\"]\r\n                let sizes = ['14', '15', '16', '17', '18']\r\n\r\n                let output = [];\r\n                for (let i = 0; i < len; i++) {\r\n                    // 随机验证码\r\n                    let key = Math.floor(Math.random()*chars.length)\r\n                    this.codes[i].num = chars[key]\r\n                    // 随机验证码颜色\r\n                    let code = '#'\r\n                    for (let j = 0; j < 6; j++) {\r\n                        let key = Math.floor(Math.random()*colors.length)\r\n                        code += colors[key]\r\n                    }\r\n                    this.codes[i].color = code\r\n                    // 随机验证码方向\r\n                    let rotate = Math.floor(Math.random()*60)\r\n                    let plus = Math.floor(Math.random()*2)\r\n                    if(plus == 1) rotate = '-'+rotate\r\n                    this.codes[i].rotate = 'rotate('+rotate+'deg)'\r\n                    // 随机验证码字体大小\r\n                    let size = Math.floor(Math.random()*sizes.length)\r\n                    this.codes[i].size = sizes[size]+'px'\r\n                }\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  // 背景装饰\r\n  .background-decoration {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    pointer-events: none;\r\n\r\n    .decoration-circle {\r\n      position: absolute;\r\n      border-radius: 50%;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      animation: float 6s ease-in-out infinite;\r\n\r\n      &.circle-1 {\r\n        width: 200px;\r\n        height: 200px;\r\n        top: 10%;\r\n        left: 10%;\r\n        animation-delay: 0s;\r\n      }\r\n\r\n      &.circle-2 {\r\n        width: 150px;\r\n        height: 150px;\r\n        top: 60%;\r\n        right: 15%;\r\n        animation-delay: 2s;\r\n      }\r\n\r\n      &.circle-3 {\r\n        width: 100px;\r\n        height: 100px;\r\n        bottom: 20%;\r\n        left: 20%;\r\n        animation-delay: 4s;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域\r\n  .main-content {\r\n    display: flex;\r\n    width: 100%;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 40px;\r\n    gap: 60px;\r\n    align-items: center;\r\n\r\n    // 左侧信息区域\r\n    .info-section {\r\n      flex: 1;\r\n      color: white;\r\n\r\n      .brand-info {\r\n        .logo-container {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 30px;\r\n\r\n          i {\r\n            font-size: 48px;\r\n            color: #00c292;\r\n            margin-right: 20px;\r\n          }\r\n\r\n          h1 {\r\n            font-size: 36px;\r\n            font-weight: 700;\r\n            margin: 0;\r\n            background: linear-gradient(45deg, #00c292, #00e5ff);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            background-clip: text;\r\n          }\r\n        }\r\n\r\n        .brand-description {\r\n          font-size: 18px;\r\n          line-height: 1.6;\r\n          margin-bottom: 40px;\r\n          opacity: 0.9;\r\n        }\r\n\r\n        .features {\r\n          .feature-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n\r\n            i {\r\n              font-size: 20px;\r\n              color: #00c292;\r\n              margin-right: 15px;\r\n              width: 24px;\r\n            }\r\n\r\n            span {\r\n              font-size: 16px;\r\n              opacity: 0.9;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 右侧登录表单\r\n    .login-section {\r\n      flex: 0 0 450px;\r\n\r\n      .login-card {\r\n        background: rgba(255, 255, 255, 0.95);\r\n        backdrop-filter: blur(20px);\r\n        border-radius: 20px;\r\n        padding: 40px;\r\n        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid rgba(255, 255, 255, 0.2);\r\n\r\n        .card-header {\r\n          text-align: center;\r\n          margin-bottom: 40px;\r\n\r\n          h2 {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #2c3e50;\r\n            margin: 0 0 10px 0;\r\n          }\r\n\r\n          p {\r\n            color: #666;\r\n            font-size: 16px;\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        .login-form {\r\n          .form-item {\r\n            margin-bottom: 25px;\r\n\r\n            .input-wrapper {\r\n              position: relative;\r\n\r\n              .input-icon {\r\n                position: absolute;\r\n                left: 15px;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                color: #00c292;\r\n                font-size: 18px;\r\n                z-index: 2;\r\n              }\r\n\r\n              ::v-deep .el-input__inner {\r\n                height: 50px;\r\n                padding-left: 50px;\r\n                border: 2px solid #e8f4f8;\r\n                border-radius: 12px;\r\n                font-size: 16px;\r\n                transition: all 0.3s ease;\r\n                background: #f8fffe !important;\r\n                color: #333 !important;\r\n\r\n                &:focus {\r\n                  border-color: #00c292;\r\n                  box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);\r\n                  background: white !important;\r\n                  color: #333 !important;\r\n                }\r\n\r\n                &::placeholder {\r\n                  color: #999 !important;\r\n                }\r\n              }\r\n            }\r\n\r\n            // 验证码特殊样式\r\n            &.captcha-item {\r\n              .input-wrapper {\r\n                display: flex;\r\n                gap: 10px;\r\n\r\n                .el-input {\r\n                  flex: 1;\r\n                }\r\n\r\n                .captcha-code {\r\n                  width: 120px;\r\n                  height: 50px;\r\n                  background: linear-gradient(45deg, #00c292, #00a085);\r\n                  border-radius: 12px;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  cursor: pointer;\r\n                  transition: all 0.3s ease;\r\n\r\n                  &:hover {\r\n                    transform: scale(1.05);\r\n                  }\r\n\r\n                  span {\r\n                    font-weight: 600;\r\n                    color: white;\r\n                    margin: 0 2px;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            // 角色选择样式\r\n            &.role-item {\r\n              .role-label {\r\n                display: block;\r\n                margin-bottom: 15px;\r\n                font-weight: 600;\r\n                color: #2c3e50 !important;\r\n                font-size: 16px;\r\n              }\r\n\r\n              .role-options {\r\n                .role-group {\r\n                  display: flex;\r\n                  flex-wrap: wrap;\r\n                  gap: 15px;\r\n\r\n                  .role-radio {\r\n                    margin: 0;\r\n\r\n                    ::v-deep .el-radio__input.is-checked .el-radio__inner {\r\n                      background-color: #00c292;\r\n                      border-color: #00c292;\r\n                    }\r\n\r\n                    ::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n                      color: #00c292 !important;\r\n                      font-weight: 600;\r\n                    }\r\n\r\n                    ::v-deep .el-radio__label {\r\n                      font-size: 16px;\r\n                      color: #666 !important;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .login-button {\r\n            width: 100%;\r\n            height: 50px;\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            border-radius: 12px;\r\n            background: linear-gradient(45deg, #00c292, #00a085);\r\n            border: none;\r\n            margin-top: 20px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover {\r\n              transform: translateY(-2px);\r\n              box-shadow: 0 8px 25px rgba(0, 194, 146, 0.3);\r\n            }\r\n\r\n            &:active {\r\n              transform: translateY(0);\r\n            }\r\n\r\n            i {\r\n              margin-right: 8px;\r\n            }\r\n          }\r\n\r\n          .form-footer {\r\n            text-align: center;\r\n            margin-top: 30px;\r\n\r\n            .register-link {\r\n              color: #00c292;\r\n              cursor: pointer;\r\n              font-size: 16px;\r\n              transition: all 0.3s ease;\r\n              display: inline-flex;\r\n              align-items: center;\r\n\r\n              &:hover {\r\n                color: #00a085;\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n                font-size: 18px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1024px) {\r\n  .login-container .main-content {\r\n    flex-direction: column;\r\n    gap: 40px;\r\n    padding: 20px;\r\n\r\n    .info-section {\r\n      text-align: center;\r\n\r\n      .brand-info .logo-container {\r\n        justify-content: center;\r\n\r\n        h1 {\r\n          font-size: 28px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .login-section {\r\n      flex: none;\r\n      width: 100%;\r\n      max-width: 450px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .login-container .main-content {\r\n    padding: 15px;\r\n\r\n    .login-section .login-card {\r\n      padding: 30px 20px;\r\n\r\n      .card-header h2 {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n\r\n    .info-section .brand-info .logo-container h1 {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;AAmIA,OAAAA,IAAA;AACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,WAAA;MAAA;MACAC,SAAA;QACAC,QAAA;QACAC,QAAA;QACAC,IAAA;QACAC,IAAA;MACA;MACAC,KAAA;MACAC,SAAA;MACAC,KAAA;QACAC,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;QACAH,GAAA;QACAC,KAAA;QACAC,MAAA;QACAC,IAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAP,KAAA,GAAAT,IAAA,CAAAiB,IAAA;IACA,KAAAR,KAAA,GAAAA,KAAA;EACA;EACAS,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACAF,aAAA,WAAAA,cAAA;MACA,KAAAG,SAAA;QACAC,QAAA,CAAAC,gBAAA,8BAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAC,eAAA;UACAF,EAAA,CAAAC,KAAA,CAAAd,KAAA;UACAa,EAAA,CAAAC,KAAA,CAAAE,MAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,UAAA;UACAJ,EAAA,CAAAC,KAAA,CAAAI,YAAA;QACA;QACAR,QAAA,CAAAC,gBAAA,0CAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAE,MAAA;UACAH,EAAA,CAAAC,KAAA,CAAAG,UAAA;QACA;QACAP,QAAA,CAAAC,gBAAA,kCAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,KAAA,CAAAd,KAAA;QACA;QACAmB,UAAA;UACAT,QAAA,CAAAC,gBAAA,oCAAAC,OAAA,WAAAC,EAAA;YACAA,EAAA,CAAAC,KAAA,CAAAd,KAAA;UACA;QACA;MACA;IAEA;IACAoB,QAAA,WAAAA,SAAAvB,SAAA;MACA,KAAAwB,QAAA,CAAAC,GAAA,eAAAzB,SAAA;MACA,KAAA0B,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IACA;IACAC,KAAA,WAAAA,MAAA;MAAA,IAAAC,KAAA;MACA;MACA,SAAArC,WAAA;QACA,IAAAK,IAAA;QACA,SAAAiC,CAAA,SAAA9B,KAAA;UACAH,IAAA,SAAAG,KAAA,CAAA8B,CAAA,EAAA7B,GAAA;QACA;QACA,UAAAR,SAAA,CAAAI,IAAA;UACA,KAAAkC,QAAA,CAAAC,KAAA;UACA;QACA;QACA,SAAAvC,SAAA,CAAAI,IAAA,CAAAoC,WAAA,MAAApC,IAAA,CAAAoC,WAAA;UACA,KAAAF,QAAA,CAAAC,KAAA;UACA,KAAAvB,WAAA;UACA;QACA;MACA;;MAEA;MACA,UAAAhB,SAAA,CAAAC,QAAA;QACA,KAAAqC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAvC,SAAA,CAAAE,QAAA;QACA,KAAAoC,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAvC,SAAA,CAAAG,IAAA;QACA,KAAAmC,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,IAAAlC,KAAA,QAAAA,KAAA;MACA,SAAAgC,EAAA,MAAAA,EAAA,GAAAhC,KAAA,CAAAoC,MAAA,EAAAJ,EAAA;QACA,IAAAhC,KAAA,CAAAgC,EAAA,EAAAK,QAAA,SAAA1C,SAAA,CAAAG,IAAA;UACA,KAAAG,SAAA,GAAAD,KAAA,CAAAgC,EAAA,EAAA/B,SAAA;QACA;MACA;;MAEA;MACA,KAAAR,OAAA;MACA,KAAA6C,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAAvC,SAAA,sBAAAuC,MAAA,MAAA7C,SAAA,CAAAC,QAAA,gBAAA4C,MAAA,MAAA7C,SAAA,CAAAE,QAAA;QACA4C,MAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAnD,IAAA,GAAAmD,IAAA,CAAAnD,IAAA;QACAuC,KAAA,CAAAtC,OAAA;QACA,IAAAD,IAAA,IAAAA,IAAA,CAAAO,IAAA;UACAgC,KAAA,CAAAN,QAAA,CAAAC,GAAA,UAAAlC,IAAA,CAAAoD,KAAA;UACAb,KAAA,CAAAN,QAAA,CAAAC,GAAA,SAAAK,KAAA,CAAApC,SAAA,CAAAG,IAAA;UACAiC,KAAA,CAAAN,QAAA,CAAAC,GAAA,iBAAAK,KAAA,CAAA9B,SAAA;UACA8B,KAAA,CAAAN,QAAA,CAAAC,GAAA,cAAAK,KAAA,CAAApC,SAAA,CAAAC,QAAA;UAEAmC,KAAA,CAAAE,QAAA;YACAY,OAAA;YACAC,IAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAjB,KAAA,CAAAJ,OAAA,CAAAsB,OAAA;gBAAApB,IAAA;cAAA;YACA;UACA;QACA;UACAE,KAAA,CAAAE,QAAA,CAAAC,KAAA,CAAA1C,IAAA,CAAA0D,GAAA;QACA;MACA,GAAAC,KAAA;QACApB,KAAA,CAAAtC,OAAA;QACAsC,KAAA,CAAAE,QAAA,CAAAC,KAAA;MACA;IACA;IACAvB,WAAA,WAAAA,YAAA;MAAA,IAAAyC,GAAA,GAAAC,SAAA,CAAAjB,MAAA,QAAAiB,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,KAAAE,YAAA,CAAAH,GAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MAAA,IAAAH,GAAA,GAAAC,SAAA,CAAAjB,MAAA,QAAAiB,SAAA,QAAAC,SAAA,GAAAD,SAAA;MACA,IAAAG,KAAA,IACA,uDACA,uDACA,uDACA,uDACA,uDACA,kCACA;MACA,IAAAC,MAAA;MACA,IAAAC,KAAA;MAEA,IAAAC,MAAA;MACA,SAAA3B,CAAA,MAAAA,CAAA,GAAAoB,GAAA,EAAApB,CAAA;QACA;QACA,IAAA4B,GAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAP,KAAA,CAAApB,MAAA;QACA,KAAAlC,KAAA,CAAA8B,CAAA,EAAA7B,GAAA,GAAAqD,KAAA,CAAAI,GAAA;QACA;QACA,IAAA7D,IAAA;QACA,SAAAiE,CAAA,MAAAA,CAAA,MAAAA,CAAA;UACA,IAAAJ,IAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAN,MAAA,CAAArB,MAAA;UACArC,IAAA,IAAA0D,MAAA,CAAAG,IAAA;QACA;QACA,KAAA1D,KAAA,CAAA8B,CAAA,EAAA5B,KAAA,GAAAL,IAAA;QACA;QACA,IAAAM,MAAA,GAAAwD,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA,IAAAE,IAAA,GAAAJ,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA;QACA,IAAAE,IAAA,OAAA5D,MAAA,SAAAA,MAAA;QACA,KAAAH,KAAA,CAAA8B,CAAA,EAAA3B,MAAA,eAAAA,MAAA;QACA;QACA,IAAAC,IAAA,GAAAuD,IAAA,CAAAC,KAAA,CAAAD,IAAA,CAAAE,MAAA,KAAAL,KAAA,CAAAtB,MAAA;QACA,KAAAlC,KAAA,CAAA8B,CAAA,EAAA1B,IAAA,GAAAoD,KAAA,CAAApD,IAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}