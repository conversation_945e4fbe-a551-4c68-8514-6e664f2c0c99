{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\gonggao\\add-or-update.vue?vue&type=style&index=0&id=73555b5a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\gonggao\\add-or-update.vue", "mtime": 1750592212497}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAseA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/gonggao", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"gonggao-form-container\">\r\n        <div class=\"form-header\">\r\n            <h3 v-if=\"type === 'info'\">公告详情</h3>\r\n            <h3 v-else-if=\"!ruleForm.id\">新增公告</h3>\r\n            <h3 v-else>编辑公告</h3>\r\n            <p>管理系统公告和轮播图信息</p>\r\n        </div>\r\n\r\n        <el-card class=\"form-card\">\r\n            <el-form\r\n                class=\"gonggao-form\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\">\r\n\r\n                <el-row :gutter=\"20\">\r\n                    <input id=\"updateId\" name=\"id\" type=\"hidden\">\r\n\r\n                    <!-- 基本信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">基本信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"公告名称\" prop=\"gonggaoName\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.gonggaoName\"\r\n                                placeholder=\"请输入公告名称\"\r\n                                clearable\r\n                                :readonly=\"ro.gonggaoName || type === 'info'\"\r\n                                prefix-icon=\"el-icon-edit-outline\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"公告类型\" prop=\"gonggaoTypes\">\r\n                            <el-select\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.gonggaoTypes\"\r\n                                placeholder=\"请选择公告类型\"\r\n                                style=\"width: 100%\">\r\n                                <el-option\r\n                                    v-for=\"(item,index) in gonggaoTypesOptions\"\r\n                                    v-bind:key=\"item.codeIndex\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                </el-option>\r\n                            </el-select>\r\n                            <el-input\r\n                                v-else\r\n                                v-model=\"ruleForm.gonggaoValue\"\r\n                                placeholder=\"公告类型\"\r\n                                readonly\r\n                                prefix-icon=\"el-icon-menu\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                    <!-- 图片信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">图片信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"24\">\r\n                        <el-form-item label=\"公告图片\" prop=\"gonggaoPhoto\">\r\n                            <file-upload\r\n                                v-if=\"type !== 'info' && !ro.gonggaoPhoto\"\r\n                                tip=\"点击上传公告图片，建议尺寸：1920x600px\"\r\n                                action=\"file/upload\"\r\n                                :limit=\"5\"\r\n                                :multiple=\"true\"\r\n                                :fileUrls=\"ruleForm.gonggaoPhoto?ruleForm.gonggaoPhoto:''\"\r\n                                @change=\"gonggaoPhotoUploadChange\"\r\n                            ></file-upload>\r\n                            <div v-else-if=\"ruleForm.gonggaoPhoto\" class=\"photo-preview\">\r\n                                <img\r\n                                    v-for=\"(item,index) in (ruleForm.gonggaoPhoto || '').split(',')\"\r\n                                    :key=\"index\"\r\n                                    :src=\"item\"\r\n                                    class=\"preview-image\"\r\n                                    @click=\"previewImage(item)\">\r\n                            </div>\r\n                            <div v-else class=\"no-image\">暂无图片</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <!-- 详细信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">详细信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"24\">\r\n                        <el-form-item label=\"公告详情\" prop=\"gonggaoContent\">\r\n                            <editor\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.gonggaoContent\"\r\n                                class=\"editor\"\r\n                                action=\"file/upload\"\r\n                                placeholder=\"请输入公告的详细内容...\">\r\n                            </editor>\r\n                            <div v-else-if=\"ruleForm.gonggaoContent\" class=\"content-preview\">\r\n                                <div v-html=\"ruleForm.gonggaoContent\"></div>\r\n                            </div>\r\n                            <div v-else class=\"no-content\">暂无详情</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                </el-row>\r\n\r\n                <!-- 操作按钮 -->\r\n                <div class=\"form-actions\">\r\n                    <el-button\r\n                        v-if=\"type !== 'info'\"\r\n                        type=\"primary\"\r\n                        @click=\"onSubmit\"\r\n                        :loading=\"loading\">\r\n                        <i class=\"el-icon-check\"></i>\r\n                        {{ !ruleForm.id ? '新增公告' : '保存修改' }}\r\n                    </el-button>\r\n                    <el-button @click=\"back()\">\r\n                        <i class=\"el-icon-back\"></i>\r\n                        {{ type === 'info' ? '返回' : '取消' }}\r\n                    </el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-card>\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                sessionTable : \"\",//登录账户所在表名\r\n                role : \"\",//权限\r\n                loading: false,\r\n                ro:{\r\n                    gonggaoName: false,\r\n                    gonggaoPhoto: false,\r\n                    gonggaoTypes: false,\r\n                    insertTime: false,\r\n                    gonggaoContent: false,\r\n                },\r\n                ruleForm: {\r\n                    gonggaoName: '',\r\n                    gonggaoPhoto: '',\r\n                    gonggaoTypes: '',\r\n                    insertTime: '',\r\n                    gonggaoContent: '',\r\n                },\r\n                gonggaoTypesOptions : [],\r\n                rules: {\r\n                   gonggaoName: [\r\n                              { required: true, message: '公告名称不能为空', trigger: 'blur' },\r\n                          ],\r\n                   gonggaoPhoto: [\r\n                              { required: true, message: '公告图片不能为空', trigger: 'blur' },\r\n                          ],\r\n                   gonggaoTypes: [\r\n                              { required: true, message: '公告类型不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   insertTime: [\r\n                              { required: true, message: '公告发布时间不能为空', trigger: 'blur' },\r\n                          ],\r\n                   gonggaoContent: [\r\n                              { required: true, message: '公告详情不能为空', trigger: 'blur' },\r\n                          ],\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n            if (this.role != \"管理员\"){\r\n            }\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n            //获取下拉框信息\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=gonggao_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.gonggaoTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n\r\n\r\n        },\r\n        mounted() {\r\n        },\r\n        methods: {\r\n            // 下载\r\n            download(file){\r\n                window.open(`${file}`)\r\n            },\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else if(this.type=='cross'){\r\n                    var obj = this.$storage.getObj('crossObj');\r\n                    for (var o in obj){\r\n\r\n                      if(o=='gonggaoName'){\r\n                          this.ruleForm.gonggaoName = obj[o];\r\n                          this.ro.gonggaoName = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='gonggaoPhoto'){\r\n                          this.ruleForm.gonggaoPhoto = obj[o];\r\n                          this.ro.gonggaoPhoto = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='gonggaoTypes'){\r\n                          this.ruleForm.gonggaoTypes = obj[o];\r\n                          this.ro.gonggaoTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='insertTime'){\r\n                          this.ruleForm.insertTime = obj[o];\r\n                          this.ro.insertTime = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='gonggaoContent'){\r\n                          this.ruleForm.gonggaoContent = obj[o];\r\n                          this.ro.gonggaoContent = true;\r\n                          continue;\r\n                      }\r\n                    }\r\n                }\r\n                // 获取用户信息\r\n                this.$http({\r\n                    url:`${this.$storage.get(\"sessionTable\")}/session`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        var json = data.data;\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `gonggao/info/${id}`,\r\n                    method: 'get'\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.ruleForm = data.data;\r\n                        //解决前台上传图片后台不显示的问题\r\n                        let reg=new RegExp('../../../upload','g')//g代表全部\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 图片预览\r\n            previewImage(url) {\r\n                this.$alert(`<img src=\"${url}\" style=\"width: 100%; max-width: 500px;\">`, '图片预览', {\r\n                    dangerouslyUseHTMLString: true,\r\n                    showConfirmButton: false,\r\n                    showCancelButton: true,\r\n                    cancelButtonText: '关闭'\r\n                });\r\n            },\r\n\r\n            // 提交\r\n            onSubmit() {\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        this.loading = true;\r\n                        this.$http({\r\n                            url:`gonggao/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: this.ruleForm\r\n                        }).then(({ data }) => {\r\n                            this.loading = false;\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.gonggaoCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        }).catch(() => {\r\n                            this.loading = false;\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.gonggaoCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            //图片\r\n            gonggaoPhotoUploadChange(fileUrls){\r\n                this.ruleForm.gonggaoPhoto = fileUrls;\r\n                this.addEditUploadStyleChange()\r\n            },\r\n\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputFontColor\r\n                        el.style.fontSize = this.addEditForm.inputFontSize\r\n                        el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                        el.style.borderColor = this.addEditForm.inputBorderColor\r\n                        el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.inputBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputLableColor\r\n                        el.style.fontSize = this.addEditForm.inputLableFontSize\r\n                    })\r\n                    // select\r\n                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectFontColor\r\n                        el.style.fontSize = this.addEditForm.selectFontSize\r\n                        el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                        el.style.borderColor = this.addEditForm.selectBorderColor\r\n                        el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.selectBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectLableColor\r\n                        el.style.fontSize = this.addEditForm.selectLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                        el.style.color = this.addEditForm.selectIconFontColor\r\n                        el.style.fontSize = this.addEditForm.selectIconFontSize\r\n                    })\r\n                    // date\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateFontColor\r\n                        el.style.fontSize = this.addEditForm.dateFontSize\r\n                        el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                        el.style.borderColor = this.addEditForm.dateBorderColor\r\n                        el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.dateBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateLableColor\r\n                        el.style.fontSize = this.addEditForm.dateLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                        el.style.color = this.addEditForm.dateIconFontColor\r\n                        el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                    })\r\n                    // upload\r\n                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.uploadHeight\r\n                        el.style.color = this.addEditForm.uploadLableColor\r\n                        el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                        el.style.color = this.addEditForm.uploadIconFontColor\r\n                        el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                        el.style.lineHeight = iconLineHeight\r\n                        el.style.display = 'block'\r\n                    })\r\n                    // 多文本输入框\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaFontColor\r\n                        el.style.fontSize = this.addEditForm.textareaFontSize\r\n                        el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                        el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                        el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                        // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaLableColor\r\n                        el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n                    })\r\n                    // 保存\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnSaveWidth\r\n                        el.style.height = this.addEditForm.btnSaveHeight\r\n                        el.style.color = this.addEditForm.btnSaveFontColor\r\n                        el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n                    })\r\n                    // 返回\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnCancelWidth\r\n                        el.style.height = this.addEditForm.btnCancelHeight\r\n                        el.style.color = this.addEditForm.btnCancelFontColor\r\n                        el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n                    })\r\n                })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.gonggao-form-container {\r\n  padding: 20px;\r\n\r\n  .form-header {\r\n    text-align: center;\r\n    margin-bottom: 30px;\r\n\r\n    h3 {\r\n      font-size: 24px;\r\n      color: #2c3e50;\r\n      margin: 0 0 10px 0;\r\n    }\r\n\r\n    p {\r\n      font-size: 14px;\r\n      color: #909399;\r\n      margin: 0;\r\n    }\r\n  }\r\n\r\n  .form-card {\r\n    ::v-deep .el-card__body {\r\n      padding: 30px;\r\n    }\r\n\r\n    .gonggao-form {\r\n      .section-title {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #2c3e50;\r\n        margin-bottom: 20px;\r\n        padding-bottom: 10px;\r\n        border-bottom: 2px solid #00c292;\r\n        position: relative;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          bottom: -2px;\r\n          left: 0;\r\n          width: 50px;\r\n          height: 2px;\r\n          background: #00c292;\r\n        }\r\n      }\r\n\r\n      .el-form-item {\r\n        margin-bottom: 25px;\r\n\r\n        ::v-deep .el-form-item__label {\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n        }\r\n\r\n        ::v-deep .el-input__inner {\r\n          border-radius: 6px;\r\n          border: 1px solid #dcdfe6;\r\n          transition: all 0.3s ease;\r\n\r\n          &:focus {\r\n            border-color: #00c292;\r\n            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n          }\r\n        }\r\n\r\n        ::v-deep .el-select {\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .photo-preview {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .preview-image {\r\n          width: 120px;\r\n          height: 120px;\r\n          object-fit: cover;\r\n          border-radius: 8px;\r\n          cursor: pointer;\r\n          border: 2px solid #dcdfe6;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            border-color: #00c292;\r\n            transform: scale(1.05);\r\n          }\r\n        }\r\n      }\r\n\r\n      .no-image, .no-content {\r\n        color: #909399;\r\n        font-style: italic;\r\n        text-align: center;\r\n        padding: 20px;\r\n        background: #f8f9fa;\r\n        border-radius: 6px;\r\n        border: 1px dashed #dcdfe6;\r\n      }\r\n\r\n      .content-preview {\r\n        padding: 15px;\r\n        background: #f8f9fa;\r\n        border-radius: 6px;\r\n        border: 1px solid #dcdfe6;\r\n        min-height: 100px;\r\n\r\n        ::v-deep img {\r\n          max-width: 100%;\r\n          height: auto;\r\n        }\r\n      }\r\n    }\r\n\r\n    .form-actions {\r\n      text-align: center;\r\n      margin-top: 30px;\r\n      padding-top: 20px;\r\n      border-top: 1px solid #ebeef5;\r\n\r\n      .el-button {\r\n        border-radius: 6px;\r\n        padding: 12px 30px;\r\n        font-weight: 600;\r\n        margin: 0 10px;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.editor {\r\n  height: 400px;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n\r\n  ::v-deep .ql-container {\r\n    height: 310px;\r\n  }\r\n\r\n  ::v-deep .ql-toolbar {\r\n    border-bottom: 1px solid #dcdfe6;\r\n  }\r\n}\r\n\r\n::v-deep .el-upload {\r\n  .el-upload-dragger {\r\n    border-radius: 8px;\r\n    border: 2px dashed #d9d9d9;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      border-color: #00c292;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n\r\n\r\n"]}]}