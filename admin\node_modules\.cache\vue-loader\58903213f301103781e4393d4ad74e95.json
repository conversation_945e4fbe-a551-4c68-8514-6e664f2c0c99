{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue?vue&type=style&index=0&id=90178fb0&scoped=true&lang=css", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue", "mtime": 1750602879994}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["collection-management.vue"], "names": [], "mappings": ";AAuiBA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "collection-management.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"collection-management\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">\n        <i class=\"el-icon-star-on\"></i>\n        收藏管理\n      </h1>\n      <p class=\"page-description\">管理用户的场地收藏记录，查看收藏统计和趋势</p>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon total\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ totalCollections }}</h3>\n              <p>总收藏数</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon today\">\n              <i class=\"el-icon-date\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ todayCollections }}</h3>\n              <p>今日新增</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon users\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ activeUsers }}</h3>\n              <p>活跃用户</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon venues\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ popularVenues }}</h3>\n              <p>热门场地</p>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"search-section\">\n      <el-card>\n        <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n          <el-form-item label=\"场地名称\">\n            <el-input\n              v-model=\"searchForm.changdiName\"\n              placeholder=\"请输入场地名称\"\n              clearable\n              @keyup.enter.native=\"handleSearch\">\n            </el-input>\n          </el-form-item>\n          <el-form-item label=\"用户姓名\">\n            <el-input\n              v-model=\"searchForm.yonghuName\"\n              placeholder=\"请输入用户姓名\"\n              clearable\n              @keyup.enter.native=\"handleSearch\">\n            </el-input>\n          </el-form-item>\n          <el-form-item label=\"收藏时间\">\n            <el-date-picker\n              v-model=\"searchForm.dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              format=\"yyyy-MM-dd\"\n              value-format=\"yyyy-MM-dd\">\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleSearch\" icon=\"el-icon-search\">搜索</el-button>\n            <el-button @click=\"handleReset\" icon=\"el-icon-refresh\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-card>\n    </div>\n\n    <!-- 操作栏 -->\n    <div class=\"action-bar\">\n      <div class=\"action-left\">\n        <el-button\n          type=\"danger\"\n          :disabled=\"selectedCollections.length === 0\"\n          @click=\"handleBatchDelete\"\n          icon=\"el-icon-delete\">\n          批量删除 ({{ selectedCollections.length }})\n        </el-button>\n        <el-button\n          type=\"success\"\n          @click=\"exportCollections\"\n          icon=\"el-icon-download\">\n          导出数据\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"syncToServer\"\n          :loading=\"syncing\"\n          icon=\"el-icon-upload2\">\n          同步到服务器\n        </el-button>\n        <el-button\n          type=\"info\"\n          @click=\"pullFromServer\"\n          :loading=\"pulling\"\n          icon=\"el-icon-download\">\n          从服务器拉取\n        </el-button>\n      </div>\n      <div class=\"action-right\">\n        <el-tag v-if=\"syncStatus.status !== 'never'\" :type=\"getSyncStatusType()\">\n          {{ getSyncStatusText() }}\n        </el-tag>\n        <el-button @click=\"loadCollections\" icon=\"el-icon-refresh\">刷新</el-button>\n      </div>\n    </div>\n\n    <!-- 收藏列表 -->\n    <div class=\"collection-table\">\n      <el-table\n        v-loading=\"loading\"\n        :data=\"collectionList\"\n        @selection-change=\"handleSelectionChange\"\n        stripe\n        style=\"width: 100%\">\n        \n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        \n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\"></el-table-column>\n        \n        <el-table-column label=\"场地信息\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"venue-info\">\n              <img \n                v-if=\"scope.row.changdiPhoto\" \n                :src=\"scope.row.changdiPhoto.split(',')[0]\" \n                class=\"venue-image\"\n                @error=\"handleImageError\">\n              <div class=\"venue-details\">\n                <h4>{{ scope.row.changdiName }}</h4>\n                <p class=\"venue-type\">{{ scope.row.changdiValue }}</p>\n                <p class=\"venue-price\">¥{{ scope.row.changdiNewMoney }}</p>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"用户信息\" min-width=\"150\">\n          <template slot-scope=\"scope\">\n            <div class=\"user-info\">\n              <p class=\"user-name\">{{ scope.row.yonghuName }}</p>\n              <p class=\"user-phone\">{{ scope.row.yonghuPhone }}</p>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"changdiCollectionValue\" label=\"收藏类型\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getCollectionTypeTag(scope.row.changdiCollectionTypes)\">\n              {{ scope.row.changdiCollectionValue }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"insertTime\" label=\"收藏时间\" width=\"180\">\n          <template slot-scope=\"scope\">\n            {{ formatDate(scope.row.insertTime) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"viewDetails(scope.row)\"\n              icon=\"el-icon-view\">\n              查看\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"danger\"\n              @click=\"handleDelete(scope.row)\"\n              icon=\"el-icon-delete\">\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-wrapper\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50, 100]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 详情对话框 -->\n    <el-dialog\n      title=\"收藏详情\"\n      :visible.sync=\"detailDialogVisible\"\n      width=\"600px\">\n      <div v-if=\"currentCollection\" class=\"collection-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"收藏ID\">{{ currentCollection.id }}</el-descriptions-item>\n          <el-descriptions-item label=\"场地名称\">{{ currentCollection.changdiName }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户姓名\">{{ currentCollection.yonghuName }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户电话\">{{ currentCollection.yonghuPhone }}</el-descriptions-item>\n          <el-descriptions-item label=\"收藏类型\">{{ currentCollection.changdiCollectionValue }}</el-descriptions-item>\n          <el-descriptions-item label=\"收藏时间\">{{ formatDate(currentCollection.insertTime) }}</el-descriptions-item>\n        </el-descriptions>\n        \n        <div class=\"venue-preview\" v-if=\"currentCollection.changdiPhoto\">\n          <h4>场地图片</h4>\n          <div class=\"image-gallery\">\n            <img \n              v-for=\"(image, index) in currentCollection.changdiPhoto.split(',')\" \n              :key=\"index\"\n              :src=\"image\" \n              class=\"preview-image\"\n              @error=\"handleImageError\">\n          </div>\n        </div>\n      </div>\n      \n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport collectionStorage from '@/utils/collection-storage'\n\nexport default {\n  name: 'CollectionManagement',\n  data() {\n    return {\n      loading: false,\n      collectionList: [],\n      selectedCollections: [],\n      currentPage: 1,\n      pageSize: 20,\n      totalCount: 0,\n      \n      // 统计数据\n      totalCollections: 0,\n      todayCollections: 0,\n      activeUsers: 0,\n      popularVenues: 0,\n      \n      // 搜索表单\n      searchForm: {\n        changdiName: '',\n        yonghuName: '',\n        dateRange: []\n      },\n      \n      // 详情对话框\n      detailDialogVisible: false,\n      currentCollection: null,\n\n      // 同步状态\n      syncing: false,\n      pulling: false,\n      syncStatus: { status: 'never', lastSync: null }\n    }\n  },\n  \n  mounted() {\n    this.loadCollections()\n    this.loadStats()\n    this.loadLocalCollections()\n    this.syncStatus = collectionStorage.getSyncStatus()\n  },\n  \n  methods: {\n    // 加载收藏列表\n    loadCollections() {\n      this.loading = true\n      \n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize\n      }\n      \n      // 添加搜索条件\n      if (this.searchForm.changdiName) {\n        params.changdiName = this.searchForm.changdiName\n      }\n      if (this.searchForm.yonghuName) {\n        params.yonghuName = this.searchForm.yonghuName\n      }\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.insertTimeStart = this.searchForm.dateRange[0]\n        params.insertTimeEnd = this.searchForm.dateRange[1]\n      }\n      \n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        this.loading = false\n        if (data && data.code === 0) {\n          this.collectionList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        } else {\n          this.$message.error(data.msg || '获取收藏列表失败')\n        }\n      }).catch((error) => {\n        this.loading = false\n        console.error('加载收藏列表失败:', error)\n        // 加载本地收藏数据作为备用\n        this.loadLocalCollections()\n      })\n    },\n\n    // 加载本地收藏数据\n    loadLocalCollections() {\n      try {\n        const localCollections = collectionStorage.getAllCollections()\n        if (localCollections.length > 0 && this.collectionList.length === 0) {\n          this.collectionList = localCollections\n          this.totalCount = localCollections.length\n          this.$message.info(`已加载本地收藏数据 (${localCollections.length} 条)`)\n        }\n      } catch (error) {\n        console.error('加载本地收藏数据失败:', error)\n      }\n    },\n\n    // 同步到服务器\n    async syncToServer() {\n      this.syncing = true\n      try {\n        const result = await collectionStorage.syncToServer(this.$http)\n        if (result.success) {\n          this.$message.success(result.message)\n          this.loadCollections() // 重新加载数据\n        } else {\n          this.$message.error(result.message)\n        }\n        this.syncStatus = collectionStorage.getSyncStatus()\n      } catch (error) {\n        console.error('同步失败:', error)\n        this.$message.error('同步失败: ' + error.message)\n      } finally {\n        this.syncing = false\n      }\n    },\n\n    // 从服务器拉取数据\n    async pullFromServer() {\n      this.pulling = true\n      try {\n        const result = await collectionStorage.pullFromServer(this.$http)\n        if (result.success) {\n          this.$message.success(result.message)\n          this.loadLocalCollections() // 重新加载本地数据\n        } else {\n          this.$message.error(result.message)\n        }\n        this.syncStatus = collectionStorage.getSyncStatus()\n      } catch (error) {\n        console.error('拉取数据失败:', error)\n        this.$message.error('拉取数据失败: ' + error.message)\n      } finally {\n        this.pulling = false\n      }\n    },\n\n    // 获取同步状态类型\n    getSyncStatusType() {\n      switch (this.syncStatus.status) {\n        case 'pending': return 'warning'\n        case 'completed': return 'success'\n        case 'failed': return 'danger'\n        default: return 'info'\n      }\n    },\n\n    // 获取同步状态文本\n    getSyncStatusText() {\n      switch (this.syncStatus.status) {\n        case 'pending': return '待同步'\n        case 'completed': return '已同步'\n        case 'failed': return '同步失败'\n        case 'never': return '从未同步'\n        default: return '未知状态'\n      }\n    },\n\n    // 加载统计数据\n    loadStats() {\n      // 模拟统计数据，实际应该从后端获取\n      this.totalCollections = 156\n      this.todayCollections = 12\n      this.activeUsers = 89\n      this.popularVenues = 23\n    },\n\n    // 搜索\n    handleSearch() {\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        changdiName: '',\n        yonghuName: '',\n        dateRange: []\n      }\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 选择变化\n    handleSelectionChange(selection) {\n      this.selectedCollections = selection\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedCollections.length === 0) {\n        this.$message.warning('请选择要删除的收藏记录')\n        return\n      }\n\n      this.$confirm(`确定要删除选中的 ${this.selectedCollections.length} 条收藏记录吗？`, '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const ids = this.selectedCollections.map(item => item.id)\n        this.deleteCollections(ids)\n      })\n    },\n\n    // 单个删除\n    handleDelete(row) {\n      this.$confirm('确定要删除这条收藏记录吗？', '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.deleteCollections([row.id])\n      })\n    },\n\n    // 删除收藏\n    deleteCollections(ids) {\n      this.$http({\n        url: 'changdiCollection/delete',\n        method: 'post',\n        data: ids\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.$message.success('删除成功')\n          this.loadCollections()\n          this.selectedCollections = []\n        } else {\n          this.$message.error(data.msg || '删除失败')\n        }\n      }).catch((error) => {\n        console.error('删除失败:', error)\n        this.$message.error('删除失败，请稍后重试')\n      })\n    },\n\n    // 查看详情\n    viewDetails(row) {\n      this.currentCollection = row\n      this.detailDialogVisible = true\n    },\n\n    // 导出数据\n    exportCollections() {\n      this.$message.info('导出功能开发中...')\n    },\n\n    // 分页大小变化\n    handleSizeChange(size) {\n      this.pageSize = size\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 当前页变化\n    handleCurrentChange(page) {\n      this.currentPage = page\n      this.loadCollections()\n    },\n\n    // 获取收藏类型标签\n    getCollectionTypeTag(type) {\n      switch (type) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 3: return 'danger'\n        default: return 'info'\n      }\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '-'\n      return new Date(date).toLocaleString('zh-CN')\n    },\n\n    // 图片错误处理\n    handleImageError(event) {\n      event.target.src = '/static/images/default-venue.png'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collection-management {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-title {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n}\n\n.page-title i {\n  margin-right: 12px;\n  color: #409eff;\n}\n\n.page-description {\n  color: #909399;\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  margin-bottom: 24px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  transition: transform 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.stat-icon i {\n  font-size: 24px;\n  color: white;\n}\n\n.stat-icon.total {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.stat-icon.today {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.stat-icon.users {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.stat-icon.venues {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.stat-content h3 {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.stat-content p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 搜索区域 */\n.search-section {\n  margin-bottom: 20px;\n}\n\n.search-form {\n  margin: 0;\n}\n\n/* 操作栏 */\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 16px 20px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* 表格区域 */\n.collection-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* 场地信息 */\n.venue-info {\n  display: flex;\n  align-items: center;\n}\n\n.venue-image {\n  width: 60px;\n  height: 60px;\n  border-radius: 8px;\n  object-fit: cover;\n  margin-right: 12px;\n  border: 1px solid #ebeef5;\n}\n\n.venue-details h4 {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.venue-type {\n  margin: 0 0 4px 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n.venue-price {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #f56c6c;\n}\n\n/* 用户信息 */\n.user-info .user-name {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.user-info .user-phone {\n  margin: 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 分页 */\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: center;\n}\n\n/* 详情对话框 */\n.collection-detail {\n  padding: 20px 0;\n}\n\n.venue-preview {\n  margin-top: 20px;\n}\n\n.venue-preview h4 {\n  margin: 0 0 12px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.image-gallery {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.preview-image {\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  object-fit: cover;\n  border: 1px solid #ebeef5;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.preview-image:hover {\n  transform: scale(1.05);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .collection-management {\n    padding: 12px;\n  }\n\n  .stats-cards .el-col {\n    margin-bottom: 12px;\n  }\n\n  .action-bar {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .venue-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .venue-image {\n    margin-bottom: 8px;\n  }\n}\n</style>\n"]}]}