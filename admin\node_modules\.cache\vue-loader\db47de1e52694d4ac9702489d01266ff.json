{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\components\\VenueDetailsDialog.vue?vue&type=style&index=0&id=53869e0a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\components\\VenueDetailsDialog.vue", "mtime": 1750596240454}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["VenueDetailsDialog.vue"], "names": [], "mappings": ";AA0LA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "VenueDetailsDialog.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"场地详情\"\n    :visible.sync=\"dialogVisible\"\n    width=\"800px\"\n    :before-close=\"handleClose\"\n    class=\"details-dialog\">\n    \n    <div class=\"details-content\">\n      <!-- 场地基本信息 -->\n      <div class=\"venue-header\">\n        <div class=\"venue-image\">\n          <img :src=\"venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" :alt=\"venue.changdiName\">\n        </div>\n        <div class=\"venue-basic-info\">\n          <h2>{{ venue.changdiName }}</h2>\n          <div class=\"venue-code\">场地编号：{{ venue.changdiUuidNumber }}</div>\n          <div class=\"venue-status\">\n            <el-tag :type=\"venue.shangxiaTypes === 1 ? 'success' : 'danger'\">\n              {{ venue.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}\n            </el-tag>\n          </div>\n          <div class=\"venue-price\">\n            <span class=\"current-price\">¥{{ venue.changdiNewMoney }}</span>\n            <span v-if=\"venue.changdiOldMoney !== venue.changdiNewMoney\" class=\"original-price\">\n              ¥{{ venue.changdiOldMoney }}\n            </span>\n            <span class=\"price-unit\">/时段</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 详细信息 -->\n      <div class=\"venue-details\">\n        <el-row :gutter=\"30\">\n          <el-col :span=\"12\">\n            <div class=\"detail-section\">\n              <h3><i class=\"el-icon-info\"></i> 基本信息</h3>\n              <div class=\"detail-list\">\n                <div class=\"detail-item\">\n                  <span class=\"label\">场地类型：</span>\n                  <span class=\"value\">{{ venue.changdiValue }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <span class=\"label\">半全场：</span>\n                  <span class=\"value\">{{ venue.banquanValue }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <span class=\"label\">开放时间：</span>\n                  <span class=\"value\">{{ venue.shijianduan || '08:00-22:00' }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <span class=\"label\">点击次数：</span>\n                  <span class=\"value\">{{ venue.changdiClicknum }}次</span>\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"12\">\n            <div class=\"detail-section\">\n              <h3><i class=\"el-icon-star-on\"></i> 推荐信息</h3>\n              <div class=\"recommendation\">\n                <p>{{ venue.tuijian || '暂无推荐信息' }}</p>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 场地描述 -->\n      <div class=\"venue-description\" v-if=\"venue.changdiContent\">\n        <h3><i class=\"el-icon-document\"></i> 场地介绍</h3>\n        <div class=\"description-content\" v-html=\"venue.changdiContent\"></div>\n      </div>\n\n      <!-- 可预约时间段 -->\n      <div class=\"available-slots\">\n        <h3><i class=\"el-icon-time\"></i> 可预约时间段</h3>\n        <div class=\"time-slots\">\n          <div v-for=\"slot in timeSlots\" :key=\"slot.value\" class=\"time-slot\" :class=\"{ disabled: slot.disabled }\">\n            <div class=\"slot-time\">{{ slot.label }}</div>\n            <div class=\"slot-status\">\n              <el-tag :type=\"slot.disabled ? 'danger' : 'success'\" size=\"mini\">\n                {{ slot.disabled ? '已预约' : '可预约' }}\n              </el-tag>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 预约须知 -->\n      <div class=\"booking-notice\">\n        <h3><i class=\"el-icon-warning\"></i> 预约须知</h3>\n        <ul class=\"notice-list\">\n          <li>请提前至少1小时预约，当天预约需要电话确认</li>\n          <li>预约成功后请按时到场，迟到超过15分钟将自动取消</li>\n          <li>如需取消预约，请提前2小时联系客服</li>\n          <li>场地内禁止吸烟，请爱护场地设施</li>\n          <li>运动时请注意安全，建议穿着运动服装和运动鞋</li>\n        </ul>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">关闭</el-button>\n      <el-button type=\"primary\" @click=\"bookVenue\" :disabled=\"venue.shangxiaTypes !== 1\">\n        <i class=\"el-icon-date\"></i>\n        立即预约\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nexport default {\n  name: 'VenueDetailsDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    venue: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  data() {\n    return {\n      dialogVisible: false,\n      \n      timeSlots: [\n        { label: '08:00-10:00', value: '08:00-10:00', disabled: false },\n        { label: '10:00-12:00', value: '10:00-12:00', disabled: true },\n        { label: '12:00-14:00', value: '12:00-14:00', disabled: false },\n        { label: '14:00-16:00', value: '14:00-16:00', disabled: false },\n        { label: '16:00-18:00', value: '16:00-18:00', disabled: true },\n        { label: '18:00-20:00', value: '18:00-20:00', disabled: false },\n        { label: '20:00-22:00', value: '20:00-22:00', disabled: false }\n      ]\n    }\n  },\n  \n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.loadVenueDetails()\n      }\n    },\n    \n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  \n  methods: {\n    // 加载场地详情\n    loadVenueDetails() {\n      // 这里可以加载更详细的场地信息\n      // 包括已预约的时间段等\n    },\n    \n    // 预约场地\n    bookVenue() {\n      const role = this.$storage.get('role')\n      if (!role || role !== '用户') {\n        this.$message.warning('请先登录用户账户')\n        this.$router.push('/login')\n        return\n      }\n\n      this.$emit('book-venue', this.venue)\n      this.handleClose()\n    },\n    \n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .details-dialog {\n  .el-dialog__header {\n    background: linear-gradient(45deg, #00c292, #00a085);\n    color: white;\n    padding: 20px 30px;\n    \n    .el-dialog__title {\n      color: white;\n      font-size: 18px;\n      font-weight: 600;\n    }\n    \n    .el-dialog__close {\n      color: white;\n      font-size: 20px;\n    }\n  }\n  \n  .el-dialog__body {\n    padding: 30px;\n    max-height: 70vh;\n    overflow-y: auto;\n  }\n}\n\n.details-content {\n  .venue-header {\n    display: flex;\n    gap: 25px;\n    margin-bottom: 30px;\n    padding: 25px;\n    background: #f8fffe;\n    border-radius: 12px;\n    border: 1px solid rgba(0, 194, 146, 0.1);\n    \n    .venue-image {\n      flex: 0 0 200px;\n      \n      img {\n        width: 200px;\n        height: 150px;\n        object-fit: cover;\n        border-radius: 8px;\n      }\n    }\n    \n    .venue-basic-info {\n      flex: 1;\n      \n      h2 {\n        font-size: 24px;\n        color: #2c3e50;\n        margin: 0 0 15px 0;\n      }\n      \n      .venue-code {\n        color: #666;\n        margin-bottom: 10px;\n        font-size: 14px;\n      }\n      \n      .venue-status {\n        margin-bottom: 15px;\n      }\n      \n      .venue-price {\n        .current-price {\n          font-size: 28px;\n          font-weight: 700;\n          color: #00c292;\n        }\n        \n        .original-price {\n          font-size: 18px;\n          color: #999;\n          text-decoration: line-through;\n          margin-left: 10px;\n        }\n        \n        .price-unit {\n          font-size: 14px;\n          color: #666;\n          margin-left: 5px;\n        }\n      }\n    }\n  }\n  \n  .venue-details {\n    margin-bottom: 30px;\n    \n    .detail-section {\n      h3 {\n        font-size: 18px;\n        color: #2c3e50;\n        margin-bottom: 20px;\n        \n        i {\n          color: #00c292;\n          margin-right: 8px;\n        }\n      }\n      \n      .detail-list {\n        .detail-item {\n          display: flex;\n          margin-bottom: 12px;\n          \n          .label {\n            flex: 0 0 80px;\n            color: #666;\n            font-size: 14px;\n          }\n          \n          .value {\n            color: #2c3e50;\n            font-size: 14px;\n          }\n        }\n      }\n      \n      .recommendation {\n        p {\n          color: #666;\n          line-height: 1.6;\n          margin: 0;\n        }\n      }\n    }\n  }\n  \n  .venue-description {\n    margin-bottom: 30px;\n    \n    h3 {\n      font-size: 18px;\n      color: #2c3e50;\n      margin-bottom: 15px;\n      \n      i {\n        color: #00c292;\n        margin-right: 8px;\n      }\n    }\n    \n    .description-content {\n      color: #666;\n      line-height: 1.6;\n      padding: 20px;\n      background: #f8f9fa;\n      border-radius: 8px;\n    }\n  }\n  \n  .available-slots {\n    margin-bottom: 30px;\n    \n    h3 {\n      font-size: 18px;\n      color: #2c3e50;\n      margin-bottom: 20px;\n      \n      i {\n        color: #00c292;\n        margin-right: 8px;\n      }\n    }\n    \n    .time-slots {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n      gap: 15px;\n      \n      .time-slot {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 15px;\n        background: white;\n        border: 2px solid #e8f4f8;\n        border-radius: 8px;\n        transition: all 0.3s ease;\n        \n        &:not(.disabled):hover {\n          border-color: #00c292;\n          background: #f8fffe;\n        }\n        \n        &.disabled {\n          background: #f5f5f5;\n          border-color: #ddd;\n        }\n        \n        .slot-time {\n          font-weight: 600;\n          color: #2c3e50;\n        }\n      }\n    }\n  }\n  \n  .booking-notice {\n    h3 {\n      font-size: 18px;\n      color: #2c3e50;\n      margin-bottom: 15px;\n      \n      i {\n        color: #f56c6c;\n        margin-right: 8px;\n      }\n    }\n    \n    .notice-list {\n      margin: 0;\n      padding-left: 20px;\n      \n      li {\n        color: #666;\n        line-height: 1.6;\n        margin-bottom: 8px;\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  \n  .el-button {\n    border-radius: 8px;\n    font-weight: 600;\n    \n    &.el-button--primary {\n      background: linear-gradient(45deg, #00c292, #00a085);\n      border: none;\n      \n      &:hover:not(:disabled) {\n        background: linear-gradient(45deg, #00a085, #008f75);\n      }\n      \n      &:disabled {\n        background: #ddd;\n        color: #999;\n      }\n    }\n  }\n}\n</style>\n"]}]}