{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexMain.vue?vue&type=style&index=0&id=16fdb8a4&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexMain.vue", "mtime": 1750588959110}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmEgewoJdGV4dC1kZWNvcmF0aW9uOiBub25lOwoJY29sb3I6ICM1NTU7Cn0KCmE6aG92ZXIgewoJYmFja2dyb3VuZDogIzAwYzI5MjsKfQoKLm5hdi1saXN0IHsKCXdpZHRoOiAxMDAlOwoJbWFyZ2luOiAwIGF1dG87Cgl0ZXh0LWFsaWduOiBsZWZ0OwoJbWFyZ2luLXRvcDogMjBweDsKCgkubmF2LXRpdGxlIHsKCQlkaXNwbGF5OiBpbmxpbmUtYmxvY2s7CgkJZm9udC1zaXplOiAxNXB4OwoJCWNvbG9yOiAjMzMzOwoJCXBhZGRpbmc6IDE1cHggMjVweDsKCQlib3JkZXI6IG5vbmU7Cgl9CgoJLm5hdi10aXRsZS5hY3RpdmUgewoJCWNvbG9yOiAjNTU1OwoJCWN1cnNvcjogZGVmYXVsdDsKCQliYWNrZ3JvdW5kLWNvbG9yOiAjZmZmOwoJfQp9CgoubmF2LWl0ZW0gewoJbWFyZ2luLXRvcDogMjBweDsKCWJhY2tncm91bmQ6ICNGRkZGRkY7CglwYWRkaW5nOiAxNXB4IDA7CgoJLm1lbnUgewoJCXBhZGRpbmc6IDE1cHggMjVweDsKCX0KfQoKLmVsLW1haW4gewoJYmFja2dyb3VuZC1jb2xvcjogI2Y4ZjlmYTsKCXBhZGRpbmc6IDIwcHggMjRweDsKCW1pbi1oZWlnaHQ6IGNhbGMoMTAwdmggLSA2MHB4KTsKCS8vIHBhZGRpbmctdG9wOiA2MHB4Owp9Cgoucm91dGVyLXZpZXcgewoJcGFkZGluZzogMjRweDsKCW1hcmdpbi10b3A6IDE2cHg7CgliYWNrZ3JvdW5kOiAjRkZGRkZGOwoJYm94LXNpemluZzogYm9yZGVyLWJveDsKCWJvcmRlci1yYWRpdXM6IDhweDsKCWJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7CgltaW4taGVpZ2h0OiA0MDBweDsKfQoKLmJyZWFkLWNydW1icyB7Cgl3aWR0aDogMTAwJTsKCS8vIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTllZWYzOwoJLy8gYm9yZGVyLXRvcDogMXB4IHNvbGlkICNlOWVlZjM7CgltYXJnaW4tdG9wOiAwOwoJbWFyZ2luLWJvdHRvbTogMTZweDsKCXBhZGRpbmc6IDEycHggMTZweDsKCWJhY2tncm91bmQ6ICNmZmZmZmY7Cglib3JkZXItcmFkaXVzOiA4cHg7Cglib3gtc2hhZG93OiAwIDFweCA0cHggcmdiYSgwLCAwLCAwLCAwLjA1KTsKCWJveC1zaXppbmc6IGJvcmRlci1ib3g7Cn0K"}, {"version": 3, "sources": ["IndexMain.vue"], "names": [], "mappings": ";AAuIA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexMain.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n\t<el-main>\r\n\t\t<bread-crumbs :title=\"title\" class=\"bread-crumbs\"></bread-crumbs>\r\n\t\t<router-view class=\"router-view\"></router-view>\r\n\t</el-main>\r\n</template>\r\n<script>\r\n\timport menu from \"@/utils/menu\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmenuList: [],\r\n\t\t\t\trole: \"\",\r\n\t\t\t\tcurrentIndex: -2,\r\n\t\t\t\titemMenu: [],\r\n\t\t\t\ttitle: ''\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tlet menus = menu.list();\r\n\t\t\tthis.menuList = menus;\r\n\t\t\tthis.role = this.$storage.get(\"role\");\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tmenuHandler(menu) {\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: menu.tableName\r\n\t\t\t\t});\r\n\t\t\t\tthis.title = menu.menu;\r\n\t\t\t},\r\n\t\t\ttitleChange(index, menus) {\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.itemMenu = menus;\r\n\t\t\t\tconsole.log(menus);\r\n\t\t\t},\r\n\t\t\thomeChange(index) {\r\n\t\t\t\tthis.itemMenu = [];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tcenterChange(index) {\r\n\t\t\t\tthis.itemMenu = [{\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"修改密码\",\r\n\t\t\t\t\t\"tableName\": \"updatePassword\"\r\n\t\t\t\t}, {\r\n\t\t\t\t\t\"buttons\": [\"新增\", \"查看\", \"修改\", \"删除\"],\r\n\t\t\t\t\t\"menu\": \"个人信息\",\r\n\t\t\t\t\t\"tableName\": \"center\"\r\n\t\t\t\t}];\r\n\t\t\t\tthis.title = \"\"\r\n\t\t\t\tthis.currentIndex = index\r\n\t\t\t\tthis.$router.push({\r\n\t\t\t\t\tname: 'home'\r\n\t\t\t\t});\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\ta {\r\n\t\ttext-decoration: none;\r\n\t\tcolor: #555;\r\n\t}\r\n\r\n\ta:hover {\r\n\t\tbackground: #00c292;\r\n\t}\r\n\r\n\t.nav-list {\r\n\t\twidth: 100%;\r\n\t\tmargin: 0 auto;\r\n\t\ttext-align: left;\r\n\t\tmargin-top: 20px;\r\n\r\n\t\t.nav-title {\r\n\t\t\tdisplay: inline-block;\r\n\t\t\tfont-size: 15px;\r\n\t\t\tcolor: #333;\r\n\t\t\tpadding: 15px 25px;\r\n\t\t\tborder: none;\r\n\t\t}\r\n\r\n\t\t.nav-title.active {\r\n\t\t\tcolor: #555;\r\n\t\t\tcursor: default;\r\n\t\t\tbackground-color: #fff;\r\n\t\t}\r\n\t}\r\n\r\n\t.nav-item {\r\n\t\tmargin-top: 20px;\r\n\t\tbackground: #FFFFFF;\r\n\t\tpadding: 15px 0;\r\n\r\n\t\t.menu {\r\n\t\t\tpadding: 15px 25px;\r\n\t\t}\r\n\t}\r\n\r\n\t.el-main {\r\n\t\tbackground-color: #f8f9fa;\r\n\t\tpadding: 20px 24px;\r\n\t\tmin-height: calc(100vh - 60px);\r\n\t\t// padding-top: 60px;\r\n\t}\r\n\r\n\t.router-view {\r\n\t\tpadding: 24px;\r\n\t\tmargin-top: 16px;\r\n\t\tbackground: #FFFFFF;\r\n\t\tbox-sizing: border-box;\r\n\t\tborder-radius: 8px;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t\tmin-height: 400px;\r\n\t}\r\n\r\n\t.bread-crumbs {\r\n\t\twidth: 100%;\r\n\t\t// border-bottom: 1px solid #e9eef3;\r\n\t\t// border-top: 1px solid #e9eef3;\r\n\t\tmargin-top: 0;\r\n\t\tmargin-bottom: 16px;\r\n\t\tpadding: 12px 16px;\r\n\t\tbackground: #ffffff;\r\n\t\tborder-radius: 8px;\r\n\t\tbox-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n</style>\r\n\r\n"]}]}