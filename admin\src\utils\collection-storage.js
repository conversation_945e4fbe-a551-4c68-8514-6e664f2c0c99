/**
 * 收藏本地存储管理工具
 * 提供收藏数据的本地存储、同步和管理功能
 */

const STORAGE_KEYS = {
  COLLECTIONS: 'local_collections',
  SYNC_STATUS: 'collection_sync_status',
  LAST_SYNC: 'collection_last_sync'
}

class CollectionStorage {
  constructor() {
    this.collections = this.loadCollections()
    this.syncStatus = this.loadSyncStatus()
  }

  /**
   * 加载本地收藏数据
   */
  loadCollections() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.COLLECTIONS)
      return data ? JSON.parse(data) : []
    } catch (error) {
      console.error('加载本地收藏数据失败:', error)
      return []
    }
  }

  /**
   * 保存收藏数据到本地存储
   */
  saveCollections() {
    try {
      localStorage.setItem(STORAGE_KEYS.COLLECTIONS, JSON.stringify(this.collections))
      this.updateSyncStatus('pending')
      return true
    } catch (error) {
      console.error('保存收藏数据失败:', error)
      return false
    }
  }

  /**
   * 添加收藏
   */
  addCollection(venueData) {
    const collection = {
      id: Date.now() + Math.random(), // 临时ID
      changdiId: venueData.id,
      changdiName: venueData.changdiName || venueData.name,
      changdiPhoto: venueData.changdiPhoto || venueData.photo,
      changdiNewMoney: venueData.changdiNewMoney || venueData.price,
      changdiValue: venueData.changdiValue || venueData.type,
      yonghuId: this.getCurrentUserId(),
      changdiCollectionTypes: 1,
      insertTime: new Date().toISOString(),
      createTime: new Date().toISOString(),
      isLocal: true, // 标记为本地数据
      syncStatus: 'pending' // 同步状态：pending, synced, failed
    }

    // 检查是否已存在
    const exists = this.collections.find(item => 
      item.changdiId === venueData.id && 
      item.yonghuId === this.getCurrentUserId()
    )

    if (exists) {
      return { success: false, message: '已经收藏过了' }
    }

    this.collections.unshift(collection)
    this.saveCollections()

    return { success: true, data: collection }
  }

  /**
   * 移除收藏
   */
  removeCollection(venueId) {
    const userId = this.getCurrentUserId()
    const index = this.collections.findIndex(item => 
      item.changdiId === venueId && 
      item.yonghuId === userId
    )

    if (index === -1) {
      return { success: false, message: '收藏不存在' }
    }

    const removed = this.collections.splice(index, 1)[0]
    this.saveCollections()

    return { success: true, data: removed }
  }

  /**
   * 检查是否已收藏
   */
  isCollected(venueId) {
    const userId = this.getCurrentUserId()
    return this.collections.some(item => 
      item.changdiId === venueId && 
      item.yonghuId === userId
    )
  }

  /**
   * 获取用户的收藏列表
   */
  getUserCollections(userId = null) {
    const targetUserId = userId || this.getCurrentUserId()
    return this.collections.filter(item => item.yonghuId === targetUserId)
  }

  /**
   * 获取所有收藏数据
   */
  getAllCollections() {
    return this.collections
  }

  /**
   * 清空收藏数据
   */
  clearCollections() {
    this.collections = []
    this.saveCollections()
  }

  /**
   * 获取收藏统计
   */
  getCollectionStats() {
    const userId = this.getCurrentUserId()
    const userCollections = this.getUserCollections(userId)
    
    const today = new Date().toISOString().split('T')[0]
    const todayCollections = userCollections.filter(item => 
      item.insertTime && item.insertTime.startsWith(today)
    )

    // 按场地类型分组
    const typeStats = {}
    userCollections.forEach(item => {
      const type = item.changdiValue || '未知'
      typeStats[type] = (typeStats[type] || 0) + 1
    })

    return {
      total: userCollections.length,
      today: todayCollections.length,
      typeDistribution: typeStats,
      lastCollectionTime: userCollections.length > 0 ? 
        Math.max(...userCollections.map(item => new Date(item.insertTime).getTime())) : null
    }
  }

  /**
   * 同步到服务器
   */
  async syncToServer(httpClient) {
    const pendingCollections = this.collections.filter(item => 
      item.isLocal && item.syncStatus === 'pending'
    )

    if (pendingCollections.length === 0) {
      return { success: true, message: '没有需要同步的数据' }
    }

    const results = {
      success: 0,
      failed: 0,
      errors: []
    }

    for (const collection of pendingCollections) {
      try {
        const response = await httpClient({
          url: 'changdiCollection/add',
          method: 'post',
          data: {
            yonghuId: collection.yonghuId,
            changdiId: collection.changdiId,
            changdiCollectionTypes: collection.changdiCollectionTypes
          }
        })

        if (response.data && response.data.code === 0) {
          // 同步成功，更新状态
          collection.syncStatus = 'synced'
          collection.isLocal = false
          results.success++
        } else {
          collection.syncStatus = 'failed'
          results.failed++
          results.errors.push(`${collection.changdiName}: ${response.data.msg || '同步失败'}`)
        }
      } catch (error) {
        collection.syncStatus = 'failed'
        results.failed++
        results.errors.push(`${collection.changdiName}: ${error.message}`)
      }
    }

    this.saveCollections()
    this.updateSyncStatus('completed', new Date().toISOString())

    return {
      success: results.failed === 0,
      message: `同步完成：成功 ${results.success} 个，失败 ${results.failed} 个`,
      details: results
    }
  }

  /**
   * 从服务器拉取收藏数据
   */
  async pullFromServer(httpClient) {
    try {
      const userId = this.getCurrentUserId()
      if (!userId) {
        return { success: false, message: '用户未登录' }
      }

      const response = await httpClient({
        url: 'changdiCollection/list',
        method: 'get',
        params: {
          page: 1,
          limit: 1000,
          yonghuId: userId
        }
      })

      if (response.data && response.data.code === 0) {
        const serverCollections = response.data.data.list || []
        
        // 合并服务器数据和本地数据
        this.mergeCollections(serverCollections)
        
        return { 
          success: true, 
          message: `从服务器拉取了 ${serverCollections.length} 条收藏记录`,
          data: serverCollections
        }
      } else {
        return { 
          success: false, 
          message: response.data.msg || '拉取数据失败' 
        }
      }
    } catch (error) {
      console.error('从服务器拉取收藏数据失败:', error)
      return { 
        success: false, 
        message: '网络错误，无法拉取数据' 
      }
    }
  }

  /**
   * 合并服务器数据和本地数据
   */
  mergeCollections(serverCollections) {
    const userId = this.getCurrentUserId()
    
    // 移除已同步的本地数据
    this.collections = this.collections.filter(item => 
      item.yonghuId !== userId || item.syncStatus === 'pending'
    )

    // 添加服务器数据
    serverCollections.forEach(serverItem => {
      const exists = this.collections.find(localItem => 
        localItem.changdiId === serverItem.changdiId && 
        localItem.yonghuId === serverItem.yonghuId
      )

      if (!exists) {
        this.collections.push({
          ...serverItem,
          isLocal: false,
          syncStatus: 'synced'
        })
      }
    })

    this.saveCollections()
  }

  /**
   * 获取当前用户ID
   */
  getCurrentUserId() {
    return localStorage.getItem('userid') || localStorage.getItem('userId')
  }

  /**
   * 加载同步状态
   */
  loadSyncStatus() {
    try {
      const data = localStorage.getItem(STORAGE_KEYS.SYNC_STATUS)
      return data ? JSON.parse(data) : { status: 'never', lastSync: null }
    } catch (error) {
      return { status: 'never', lastSync: null }
    }
  }

  /**
   * 更新同步状态
   */
  updateSyncStatus(status, timestamp = null) {
    this.syncStatus = {
      status,
      lastSync: timestamp || this.syncStatus.lastSync,
      updatedAt: new Date().toISOString()
    }

    try {
      localStorage.setItem(STORAGE_KEYS.SYNC_STATUS, JSON.stringify(this.syncStatus))
    } catch (error) {
      console.error('更新同步状态失败:', error)
    }
  }

  /**
   * 获取同步状态
   */
  getSyncStatus() {
    return this.syncStatus
  }

  /**
   * 导出收藏数据
   */
  exportCollections() {
    const data = {
      collections: this.collections,
      syncStatus: this.syncStatus,
      exportTime: new Date().toISOString(),
      version: '1.0'
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { 
      type: 'application/json' 
    })
    
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `collections_backup_${new Date().toISOString().split('T')[0]}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  /**
   * 导入收藏数据
   */
  importCollections(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result)
          
          if (data.collections && Array.isArray(data.collections)) {
            this.collections = data.collections
            this.saveCollections()
            
            if (data.syncStatus) {
              this.syncStatus = data.syncStatus
              this.updateSyncStatus(data.syncStatus.status, data.syncStatus.lastSync)
            }
            
            resolve({
              success: true,
              message: `成功导入 ${data.collections.length} 条收藏记录`
            })
          } else {
            reject(new Error('无效的备份文件格式'))
          }
        } catch (error) {
          reject(new Error('解析备份文件失败: ' + error.message))
        }
      }
      
      reader.onerror = () => {
        reject(new Error('读取文件失败'))
      }
      
      reader.readAsText(file)
    })
  }
}

// 创建单例实例
const collectionStorage = new CollectionStorage()

export default collectionStorage
