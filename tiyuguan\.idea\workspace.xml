<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="15288608-bafb-4042-b306-1f212eb01c41" name="更改" comment="">
      <change afterPath="$PROJECT_DIR$/../.idea/encodings.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../.idea/jarRepositories.xml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/.cache/babel-loader/224782a59c9d37b7b7baca300a95af1f.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/.cache/babel-loader/9085b96ce42bd59378621ec10001df3b.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/.cache/babel-loader/c64a136ca484f29cbf8e338a4df81b5d.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/.cache/vue-loader/dc96bcc23176c0edd58e3327f8043c9d.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/compat-data/corejs3-shipped-proposals.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/helpers/lib/helpers/regeneratorRuntime.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/plugin-syntax-numeric-separator/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/plugin-transform-modules-commonjs/lib/dynamic-import.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/runtime/helpers/setFunctionName.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/index.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@jridgewell/trace-mapping/dist/types/sourcemap-segment.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@types/node/http.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/component-compiler-utils/node_modules/postcss/lib/container.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@webassemblyjs/ast/esm/signatures.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/@xtuc/long/dist/long.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/ansi-colors/types/index.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/ansi-escapes/node_modules/type-fest/source/opaque.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/async-validator/es/rule/pattern.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/async-validator/es/validator/date.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/aws-sign2/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/babel-runtime/node_modules/core-js/fn/function/name.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/babel-runtime/node_modules/core-js/fn/function/virtual/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/babel-runtime/node_modules/core-js/fn/regexp/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/babel-runtime/node_modules/core-js/fn/symbol/for.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/babel-runtime/node_modules/core-js/library/modules/_set-species.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/babel-runtime/node_modules/core-js/modules/_date-to-iso-string.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/babel-runtime/node_modules/core-js/modules/library/_add-to-unscopables.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/bfj/test/unit/match.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/bluebird/js/release/some.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/cacache/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/caniuse-lite/data/features/css-text-orientation.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/caniuse-lite/data/regions/AM.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/caniuse-lite/data/regions/PN.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/caniuse-lite/data/regions/SI.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/canvas/build/Release/zlib1.dll" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/canvas/src/JPEGStream.h" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/actual/suppressed-error.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/features/math/rad-per-deg.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/full/regexp/escape.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/full/string/virtual/replace-all.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/full/typed-array/includes.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/internals/to-property-key.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/proposals/decorators.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/proposals/json-parse-with-source.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/core-js/stable/typed-array/from.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/css-tree/docs/Tokenizer.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/css-tree/lib/syntax/create.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/css-tree/lib/syntax/node/Identifier.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/cssnano-util-get-arguments/CHANGELOG.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/defaults/node_modules/clone/clone.iml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/detect-node/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/lib/container.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/lib/theme-chalk/input-number.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/lib/theme-chalk/result.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/lib/umd/locale/en.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/packages/dropdown-item/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/packages/theme-chalk/src/common/var.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/types/input-number.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/element-ui/types/tree.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/enhanced-resolve/lib/UnsafeCachePlugin.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2018/ArraySpeciesCreate.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2019/CanonicalNumericIndexString.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2019/FlattenIntoArray.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2019/thisNumberValue.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2021/Day.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2021/ToPropertyDescriptor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2021/YearFromTime.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2022/CloneArrayBuffer.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/es-abstract/2023/IsCompatiblePropertyDescriptor.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/eslint/lib/formatters/visualstudio.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/eslint/node_modules/debug/src/browser.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/eslint/node_modules/ms/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/extglob/node_modules/is-descriptor/.eslintrc" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/faye-websocket/lib/faye/websocket/api/event.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/fs.realpath/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/http-proxy/lib/http-proxy/passes/web-outgoing.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/infer-owner/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/inquirer/node_modules/restore-cursor/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/is-descriptor/LICENSE" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/lodash/_getValue.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/lodash/isEmpty.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/memory-fs/node_modules/readable-stream/writable-browser.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/node-sass/src/libsass/src/paths.hpp" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/opener/LICENSE.txt" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/os-browserify/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/pnp-webpack-plugin/fixtures/file.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/postcss-loader/node_modules/picocolors/picocolors.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/postcss-modules-local-by-default/node_modules/postcss/lib/tokenize.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/postcss-modules-values/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/postcss-normalize-unicode/node_modules/postcss/lib/container.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/q/CHANGES.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/quill/blots/text.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/quill/formats/script.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/regenerate-unicode-properties/Binary_Property/Changes_When_Lowercased.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/regenerator-runtime/runtime.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/renderkid/node_modules/entities/lib/decode.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/ripemd160/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/_esm2015/internal/operators/concatMapTo.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/_esm2015/internal/operators/sequenceEqual.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/_esm2015/internal/operators/timeoutWith.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/_esm5/testing/index.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/add/observable/range.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/observable/PairsObservable.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/scheduler/animationFrame.js.map" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/src/observable/FromObservable.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/rxjs/src/operators/takeUntil.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/sass-loader/dist/getDefaultSassImplementation.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/simple-swizzle/node_modules/is-arrayish/yarn-error.log" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/strip-final-newline/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/svgo/plugins/removeUnknownsAndDefaults.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/typed-array-byte-length/index.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/unidecode/data/x54.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/vue-eslint-parser/index.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/vue-loader-v16/node_modules/color-name/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/vue-loader-v16/node_modules/has-flag/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/vue-style-loader/node_modules/json5/lib/parse.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/vue/src/compiler/helpers.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/vue/src/core/vdom/helpers/is-async-placeholder.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/webpack-dev-server/node_modules/debug/src/browser.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/webpack-dev-server/node_modules/readable-stream/passthrough.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/webpack/node_modules/loader-utils/lib/getHashDigest.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/which-typed-array/.eslintrc" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/../admin/node_modules/worker-farm/examples/basic/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/.bin/nanoid" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/.bin/nanoid" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/.bin/nanoid.cmd" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/.bin/nanoid.cmd" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/.bin/nanoid.ps1" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/.bin/nanoid.ps1" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/.bin/parser" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/.bin/parser" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/.bin/parser.cmd" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/.bin/parser.cmd" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/.bin/parser.ps1" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/.bin/parser.ps1" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-string-parser/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-string-parser/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-validator-identifier/lib/identifier.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-validator-identifier/lib/identifier.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-validator-identifier/lib/identifier.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-validator-identifier/lib/identifier.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-validator-identifier/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-validator-identifier/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/helper-validator-identifier/scripts/generate-identifier-regex.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/parser/bin/babel-parser.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/parser/bin/babel-parser.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/parser/index.cjs" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/parser/lib/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/parser/lib/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/parser/typings/babel-parser.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/parser/typings/babel-parser.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/asserts/generated/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/asserts/generated/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/asserts/generated/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/asserts/generated/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/ast-types/generated/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/ast-types/generated/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/generated/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/generated/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/generated/uppercase.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/generated/uppercase.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/generated/uppercase.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/generated/uppercase.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/validateNode.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/validateNode.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/validateNode.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/builders/validateNode.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/constants/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/constants/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/constants/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/constants/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/gatherSequenceExpressions.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/gatherSequenceExpressions.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/toExpression.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/toExpression.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/toExpression.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/toExpression.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/toSequenceExpression.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/toSequenceExpression.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/valueToNode.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/valueToNode.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/valueToNode.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/converters/valueToNode.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/core.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/core.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/core.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/core.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/experimental.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/experimental.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/experimental.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/experimental.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/flow.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/flow.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/flow.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/flow.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/jsx.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/jsx.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/jsx.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/jsx.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/misc.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/misc.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/misc.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/misc.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/typescript.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/typescript.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/typescript.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/typescript.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/utils.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/utils.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/utils.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/definitions/utils.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/index-legacy.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/index-legacy.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/index.js.flow" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/index.js.flow" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/modifications/typescript/removeTypeDuplicates.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/modifications/typescript/removeTypeDuplicates.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/retrievers/getBindingIdentifiers.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/retrievers/getBindingIdentifiers.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/traverse/traverseFast.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/traverse/traverseFast.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/traverse/traverseFast.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/traverse/traverseFast.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/utils/deprecationWarning.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/utils/deprecationWarning.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/utils/deprecationWarning.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/utils/deprecationWarning.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/generated/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/generated/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/generated/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/generated/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/is.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/is.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/is.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/is.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isLet.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isLet.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isLet.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isLet.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isPlaceholderType.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isPlaceholderType.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isPlaceholderType.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isPlaceholderType.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isType.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isType.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isType.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isType.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isVar.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isVar.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isVar.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/isVar.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/validate.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/validate.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/validate.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/lib/validators/validate.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@babel/types/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@babel/types/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/babel-plugin-resolve-type/node_modules/@vue/compiler-sfc/LICENSE" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/babel-plugin-resolve-type/node_modules/@vue/compiler-sfc/README.md" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/babel-plugin-resolve-type/node_modules/@vue/compiler-sfc/dist/compiler-sfc.cjs.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/babel-plugin-resolve-type/node_modules/@vue/compiler-sfc/dist/compiler-sfc.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.cjs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.cjs.prod.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.cjs.prod.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.esm-bundler.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/dist/compiler-core.esm-bundler.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-core/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.prod.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.cjs.prod.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-browser.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-browser.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-browser.prod.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-browser.prod.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-bundler.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.esm-bundler.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.global.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.global.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.global.prod.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/dist/compiler-dom.global.prod.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-dom/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-sfc/LICENSE" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-sfc/LICENSE" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-sfc/dist/compiler-sfc.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-sfc/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-sfc/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-ssr/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-ssr/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-ssr/dist/compiler-ssr.cjs.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-ssr/dist/compiler-ssr.cjs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-ssr/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/compiler-ssr/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/dist/shared.cjs.prod.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/dist/shared.cjs.prod.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/dist/shared.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/dist/shared.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/dist/shared.esm-bundler.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/dist/shared.esm-bundler.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/@vue/shared/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode-codepoint.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode-codepoint.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode-codepoint.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode-codepoint.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/decode.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/encode.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/encode.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/encode.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/encode.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/escape.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/escape.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/escape.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/escape.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/decode-data-html.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/decode-data-html.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/decode-data-html.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/decode-data-html.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/decode-data-xml.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/decode-data-xml.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/decode-data-xml.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/encode-html.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/generated/encode-html.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/index.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/index.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/commonjs/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode-codepoint.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode-codepoint.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode-codepoint.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode-codepoint.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/decode.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/encode.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/encode.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/encode.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/escape.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/escape.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/escape.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/escape.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-html.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-html.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-html.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-html.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-xml.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-xml.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-xml.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/decode-data-xml.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/encode-html.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/encode-html.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/encode-html.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/generated/encode-html.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/index.d.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/index.d.ts.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/index.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/dist/esm/package.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/entities/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/decode-codepoint.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/decode.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/encode.spec.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/encode.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/escape.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/generated/.eslintrc.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/generated/decode-data-html.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/generated/decode-data-xml.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/generated/encode-html.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/index.spec.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/entities/src/index.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/magic-string/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/magic-string/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.cjs.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.cjs.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.cjs.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.cjs.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.es.d.mts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.es.d.mts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.es.mjs.map" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.es.mjs.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.umd.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/magic-string/dist/magic-string.umd.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/magic-string/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/magic-string/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.browser.cjs" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.browser.cjs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.browser.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.browser.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.cjs" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.cjs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.native.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/async/index.native.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.browser.cjs" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.browser.cjs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.browser.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.browser.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.cjs" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.cjs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/non-secure/index.cjs" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/non-secure/index.cjs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/non-secure/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/non-secure/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/nanoid/url-alphabet/index.cjs" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/nanoid/url-alphabet/index.cjs" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/picocolors/LICENSE" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/picocolors/LICENSE" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/picocolors/picocolors.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/picocolors/picocolors.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/picocolors/types.ts" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/README.md" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/README.md" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/at-rule.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/at-rule.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/comment.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/comment.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/container.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/container.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/container.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/container.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/css-syntax-error.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/css-syntax-error.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/declaration.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/declaration.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/declaration.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/declaration.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/document.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/document.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/fromJSON.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/fromJSON.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/input.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/input.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/lazy-result.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/lazy-result.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/lazy-result.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/lazy-result.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/list.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/list.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/map-generator.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/map-generator.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/no-work-result.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/no-work-result.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/no-work-result.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/no-work-result.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/node.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/node.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/node.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/node.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/parse.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/parse.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/parser.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/parser.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/postcss.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/postcss.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/previous-map.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/previous-map.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/processor.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/processor.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/processor.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/processor.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/result.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/result.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/result.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/result.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/root.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/root.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/rule.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/rule.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/rule.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/rule.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/stringifier.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/stringifier.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/tokenize.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/lib/tokenize.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/postcss/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/postcss/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/source-map-js/lib/source-map-consumer.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/source-map-js/lib/source-map-consumer.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/source-map-js/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/source-map-js/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/node_modules/source-map-js/source-map.d.ts" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/node_modules/source-map-js/source-map.d.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/src/utils/menu.js" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/src/utils/menu.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/src/views/modules/dictionaryForum/add-or-update.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/src/views/modules/dictionaryForum/add-or-update.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/src/views/modules/dictionaryForum/list.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/src/views/modules/dictionaryForum/list.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../admin/src/views/register.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../admin/src/views/register.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/jarRepositories.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/jarRepositories.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/misc.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/misc.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/modules.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/tiyuguan.iml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/vcs.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/vcs.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/controller/ForumController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/controller/GonggaoController.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/dao/ForumDao.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/dao/GonggaoDao.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/ForumEntity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/GonggaoEntity.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/model/ChangdiCollectionModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/model/ChangdiModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/model/ChangdiOrderModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/model/DictionaryModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/model/ForumModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/model/GonggaoModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/model/YonghuModel.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/view/ForumView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/view/GonggaoView.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/vo/ForumVO.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/entity/vo/GonggaoVO.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/service/ForumService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/service/GonggaoService.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/service/impl/CommonServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/java/com/service/impl/CommonServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/service/impl/ForumServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/java/com/service/impl/GonggaoServiceImpl.java" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/dist/css/app.3d693ba9.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/dist/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/admin/admin/dist/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/dist/js/app.eec93dbf.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/dist/js/app.eec93dbf.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/dist/js/chunk-vendors.5a6aab60.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/dist/js/chunk-vendors.5a6aab60.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/src/assets/img/bg.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/admin/admin/src/utils/menu.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/admin/admin/src/utils/menu.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/front/front/js/config.js" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/front/front/js/config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/front/front/pages/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/front/front/pages/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/img/img/back-img-bg.jpg" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/img/img/back-img-bg.jpg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/img/img/front-img-bg.jpg" beforeDir="false" afterPath="$PROJECT_DIR$/src/main/resources/img/img/front-img-bg.jpg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/ForumDao.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/mapper/GonggaoDao.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/main/resources/static/upload/1642410512593.jpeg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/dist/css/app.3d693ba9.css" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/dist/index.html" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/admin/admin/dist/index.html" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/dist/js/app.eec93dbf.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/dist/js/app.eec93dbf.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/dist/js/chunk-vendors.5a6aab60.js" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/dist/js/chunk-vendors.5a6aab60.js.map" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/src/assets/img/bg.jpg" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/admin/admin/src/utils/menu.js" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/admin/admin/src/utils/menu.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/ServletContextListener/DictionaryServletContextListener.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/ServletContextListener/DictionaryServletContextListener.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/annotation/APPLoginUser.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/annotation/APPLoginUser.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/annotation/IgnoreAuth.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/annotation/IgnoreAuth.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/annotation/LoginUser.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/annotation/LoginUser.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/config/InterceptorConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/config/InterceptorConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/config/MyMetaObjectHandler.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/config/MyMetaObjectHandler.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/config/MybatisPlusConfig.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/config/MybatisPlusConfig.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/ChangdiCollectionController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/ChangdiCollectionController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/ChangdiController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/ChangdiController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/ChangdiOrderController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/ChangdiOrderController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/CommonController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/CommonController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/ConfigController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/ConfigController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/DictionaryController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/DictionaryController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/FileController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/FileController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/ForumController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/GonggaoController.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/UsersController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/UsersController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/controller/YonghuController.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/controller/YonghuController.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/ChangdiCollectionDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/ChangdiCollectionDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/ChangdiDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/ChangdiDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/ChangdiOrderDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/ChangdiOrderDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/CommonDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/CommonDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/ConfigDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/ConfigDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/DictionaryDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/DictionaryDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/ForumDao.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/GonggaoDao.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/TokenDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/TokenDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/UsersDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/UsersDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/dao/YonghuDao.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/dao/YonghuDao.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/ChangdiCollectionEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/ChangdiCollectionEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/ChangdiEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/ChangdiEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/ChangdiOrderEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/ChangdiOrderEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/ConfigEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/ConfigEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/DictionaryEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/DictionaryEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/EIException.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/EIException.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/ForumEntity.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/GonggaoEntity.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/TokenEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/TokenEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/UsersEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/UsersEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/YonghuEntity.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/YonghuEntity.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/model/ChangdiCollectionModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/model/ChangdiModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/model/ChangdiOrderModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/model/DictionaryModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/model/ForumModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/model/GonggaoModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/model/YonghuModel.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/view/ChangdiCollectionView.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/view/ChangdiCollectionView.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/view/ChangdiOrderView.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/view/ChangdiOrderView.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/view/ChangdiView.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/view/ChangdiView.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/view/DictionaryView.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/view/DictionaryView.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/view/ForumView.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/view/GonggaoView.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/view/YonghuView.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/view/YonghuView.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/vo/ChangdiCollectionVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/vo/ChangdiCollectionVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/vo/ChangdiOrderVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/vo/ChangdiOrderVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/vo/ChangdiVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/vo/ChangdiVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/vo/DictionaryVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/vo/DictionaryVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/vo/ForumVO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/vo/GonggaoVO.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/entity/vo/YonghuVO.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/entity/vo/YonghuVO.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/interceptor/AuthorizationInterceptor.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/interceptor/AuthorizationInterceptor.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/model/enums/TypeEnum.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/model/enums/TypeEnum.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/ChangdiCollectionService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/ChangdiCollectionService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/ChangdiOrderService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/ChangdiOrderService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/ChangdiService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/ChangdiService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/CommonService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/CommonService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/ConfigService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/ConfigService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/DictionaryService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/DictionaryService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/ForumService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/GonggaoService.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/TokenService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/TokenService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/UsersService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/UsersService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/YonghuService.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/YonghuService.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/ChangdiCollectionServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/ChangdiCollectionServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/ChangdiOrderServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/ChangdiOrderServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/ChangdiServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/ChangdiServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/CommonServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/CommonServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/ConfigServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/ConfigServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/DictionaryServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/DictionaryServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/ForumServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/GonggaoServiceImpl.class" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/TokenServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/TokenServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/UsersServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/UsersServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/service/impl/YonghuServiceImpl.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/service/impl/YonghuServiceImpl.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/thread/MyThreadMethod.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/thread/MyThreadMethod.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/tiyuguanApplication.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/tiyuguanApplication.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/BaiduUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/BaiduUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/CommonUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/CommonUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/FileUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/FileUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/HttpClientUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/HttpClientUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/JQPageInfo.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/JQPageInfo.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/MPUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/MPUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/PageUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/PageUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/PoiUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/PoiUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/Query.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/Query.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/R.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/R.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/SQLFilter.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/SQLFilter.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/SpringContextUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/SpringContextUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/StringUtil.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/StringUtil.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/com/utils/ValidatorUtils.class" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/com/utils/ValidatorUtils.class" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/front/front/js/config.js" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/front/front/js/config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/front/front/pages/home/<USER>" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/front/front/pages/home/<USER>" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/img/img/back-img-bg.jpg" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/img/img/back-img-bg.jpg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/img/img/front-img-bg.jpg" beforeDir="false" afterPath="$PROJECT_DIR$/target/classes/img/img/front-img-bg.jpg" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/mapper/ForumDao.xml" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/target/classes/mapper/GonggaoDao.xml" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="C:/Program Files/Apache/apache-maven-3.8.5" />
        <option name="showDialogWithAdvancedSettings" value="true" />
        <option name="userSettingsFile" value="C:\Program Files\Apache\apache-maven-3.8.5\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/src/main/resources/admin/admin/package.json" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2kO0dDtCmK5mCZmmkj8ubcvO7CW" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_ADD_EXTERNAL_FILES": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "ignore.virus.scanning.warn.message": "true",
    "last_opened_file_path": "D:/1/tiyuguan/tiyuguan",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "settings.editor.selected.configurable": "MavenSettings",
    "spring.configuration.checksum": "a6c15d2d5a23da62c08aab3b0879df9c",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "derby",
      "mysql"
    ]
  }
}]]></component>
  <component name="RunManager">
    <configuration name="tiyuguanApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="tiyuguan" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tiyuguanApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="15288608-bafb-4042-b306-1f212eb01c41" name="更改" comment="" />
      <created>1723139400664</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1723139400664</updated>
      <workItem from="1723139401671" duration="1591000" />
      <workItem from="1731232944367" duration="1716000" />
      <workItem from="1731293928857" duration="259000" />
      <workItem from="1731390647657" duration="167000" />
      <workItem from="1733126146633" duration="2657000" />
      <workItem from="1733536752670" duration="7714000" />
      <workItem from="1735225385978" duration="1756000" />
      <workItem from="1735738590206" duration="239000" />
      <workItem from="1750580426988" duration="1791000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>