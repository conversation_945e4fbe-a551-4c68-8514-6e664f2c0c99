{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue", "mtime": 1750602713137}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["collection-analytics.vue"], "names": [], "mappings": ";AAiOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "collection-analytics.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"collection-analytics\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">\n        <i class=\"el-icon-data-analysis\"></i>\n        收藏数据分析\n      </h1>\n      <p class=\"page-description\">分析用户收藏行为，了解热门场地和收藏趋势</p>\n    </div>\n\n    <!-- 核心指标卡片 -->\n    <div class=\"metrics-section\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"metric-card primary\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ totalCollections }}</h3>\n              <p>总收藏数</p>\n              <span class=\"metric-trend up\">\n                <i class=\"el-icon-top\"></i>\n                +{{ todayIncrease }}\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card success\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ popularVenues }}</h3>\n              <p>热门场地数</p>\n              <span class=\"metric-trend\">\n                收藏>10次\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card warning\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ activeCollectors }}</h3>\n              <p>活跃收藏用户</p>\n              <span class=\"metric-trend\">\n                本月活跃\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card info\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-pie-chart\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ avgCollectionsPerUser.toFixed(1) }}</h3>\n              <p>人均收藏数</p>\n              <span class=\"metric-trend\">\n                平均值\n              </span>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 图表区域 -->\n    <div class=\"charts-section\">\n      <el-row :gutter=\"20\">\n        <!-- 收藏趋势图 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>收藏趋势</span>\n              <el-select v-model=\"trendPeriod\" size=\"small\" @change=\"loadTrendData\">\n                <el-option label=\"最近7天\" value=\"7days\"></el-option>\n                <el-option label=\"最近30天\" value=\"30days\"></el-option>\n                <el-option label=\"最近3个月\" value=\"3months\"></el-option>\n              </el-select>\n            </div>\n            <div id=\"trendChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n        \n        <!-- 场地类型分布 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>场地类型收藏分布</span>\n            </div>\n            <div id=\"typeChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n        <!-- 热门场地排行 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>热门场地排行</span>\n            </div>\n            <div class=\"ranking-list\">\n              <div \n                v-for=\"(venue, index) in topVenues\" \n                :key=\"venue.id\"\n                class=\"ranking-item\"\n                :class=\"{ 'top-three': index < 3 }\">\n                <div class=\"ranking-number\" :class=\"getRankingClass(index)\">\n                  {{ index + 1 }}\n                </div>\n                <div class=\"venue-info\">\n                  <img \n                    v-if=\"venue.photo\" \n                    :src=\"venue.photo.split(',')[0]\" \n                    class=\"venue-thumb\"\n                    @error=\"handleImageError\">\n                  <div class=\"venue-details\">\n                    <h4>{{ venue.name }}</h4>\n                    <p>{{ venue.type }}</p>\n                  </div>\n                </div>\n                <div class=\"collection-count\">\n                  <span class=\"count\">{{ venue.collections }}</span>\n                  <span class=\"label\">收藏</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <!-- 用户收藏行为分析 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>用户收藏行为</span>\n            </div>\n            <div class=\"behavior-stats\">\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏高峰时段</div>\n                <div class=\"behavior-value\">14:00 - 16:00</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">平均收藏间隔</div>\n                <div class=\"behavior-value\">2.3天</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">取消收藏率</div>\n                <div class=\"behavior-value\">12.5%</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏转预约率</div>\n                <div class=\"behavior-value\">35.8%</div>\n              </div>\n            </div>\n            \n            <div class=\"time-distribution\">\n              <h4>收藏时间分布</h4>\n              <div class=\"time-bars\">\n                <div \n                  v-for=\"hour in timeDistribution\" \n                  :key=\"hour.time\"\n                  class=\"time-bar\">\n                  <div class=\"bar\" :style=\"{ height: hour.percentage + '%' }\"></div>\n                  <span class=\"time-label\">{{ hour.time }}</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 详细数据表格 -->\n    <div class=\"data-table-section\">\n      <el-card>\n        <div slot=\"header\" class=\"table-header\">\n          <span>收藏详细数据</span>\n          <div class=\"table-actions\">\n            <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\n              导出数据\n            </el-button>\n            <el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n        \n        <el-table :data=\"detailData\" stripe style=\"width: 100%\">\n          <el-table-column prop=\"venueName\" label=\"场地名称\" min-width=\"150\"></el-table-column>\n          <el-table-column prop=\"venueType\" label=\"场地类型\" width=\"120\"></el-table-column>\n          <el-table-column prop=\"totalCollections\" label=\"总收藏数\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"todayCollections\" label=\"今日新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"weekCollections\" label=\"本周新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"conversionRate\" label=\"转预约率\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              {{ scope.row.conversionRate }}%\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"avgRating\" label=\"平均评分\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              <el-rate \n                v-model=\"scope.row.avgRating\" \n                disabled \n                show-score \n                text-color=\"#ff9900\"\n                score-template=\"{value}\">\n              </el-rate>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CollectionAnalytics',\n  data() {\n    return {\n      // 核心指标\n      totalCollections: 1256,\n      todayIncrease: 23,\n      popularVenues: 15,\n      activeCollectors: 89,\n      avgCollectionsPerUser: 4.2,\n      \n      // 图表配置\n      trendPeriod: '7days',\n      \n      // 热门场地数据\n      topVenues: [\n        {\n          id: 1,\n          name: '中央体育馆篮球场',\n          type: '篮球场',\n          photo: '/static/images/basketball.jpg',\n          collections: 156\n        },\n        {\n          id: 2,\n          name: '游泳馆标准池',\n          type: '游泳池',\n          photo: '/static/images/swimming.jpg',\n          collections: 134\n        },\n        {\n          id: 3,\n          name: '网球场A区',\n          type: '网球场',\n          photo: '/static/images/tennis.jpg',\n          collections: 98\n        },\n        {\n          id: 4,\n          name: '羽毛球馆1号场',\n          type: '羽毛球场',\n          photo: '/static/images/badminton.jpg',\n          collections: 87\n        },\n        {\n          id: 5,\n          name: '足球场草坪',\n          type: '足球场',\n          photo: '/static/images/football.jpg',\n          collections: 76\n        }\n      ],\n      \n      // 时间分布数据\n      timeDistribution: [\n        { time: '6:00', percentage: 5 },\n        { time: '8:00', percentage: 15 },\n        { time: '10:00', percentage: 25 },\n        { time: '12:00', percentage: 35 },\n        { time: '14:00', percentage: 85 },\n        { time: '16:00', percentage: 90 },\n        { time: '18:00', percentage: 70 },\n        { time: '20:00', percentage: 45 },\n        { time: '22:00', percentage: 20 }\n      ],\n      \n      // 详细数据\n      detailData: [\n        {\n          venueName: '中央体育馆篮球场',\n          venueType: '篮球场',\n          totalCollections: 156,\n          todayCollections: 8,\n          weekCollections: 23,\n          conversionRate: 42.3,\n          avgRating: 4.5\n        },\n        {\n          venueName: '游泳馆标准池',\n          venueType: '游泳池',\n          totalCollections: 134,\n          todayCollections: 5,\n          weekCollections: 18,\n          conversionRate: 38.7,\n          avgRating: 4.3\n        },\n        {\n          venueName: '网球场A区',\n          venueType: '网球场',\n          totalCollections: 98,\n          todayCollections: 3,\n          weekCollections: 12,\n          conversionRate: 35.2,\n          avgRating: 4.2\n        }\n      ]\n    }\n  },\n  \n  mounted() {\n    this.initCharts()\n    this.loadAnalyticsData()\n  },\n  \n  methods: {\n    // 初始化图表\n    initCharts() {\n      this.$nextTick(() => {\n        this.initTrendChart()\n        this.initTypeChart()\n      })\n    },\n    \n    // 初始化趋势图\n    initTrendChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化收藏趋势图')\n    },\n    \n    // 初始化类型分布图\n    initTypeChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化场地类型分布图')\n    },\n    \n    // 加载趋势数据\n    loadTrendData() {\n      console.log('加载趋势数据:', this.trendPeriod)\n    },\n    \n    // 加载分析数据\n    loadAnalyticsData() {\n      // 模拟数据加载\n      console.log('加载收藏分析数据')\n    },\n    \n    // 获取排名样式\n    getRankingClass(index) {\n      if (index === 0) return 'gold'\n      if (index === 1) return 'silver'\n      if (index === 2) return 'bronze'\n      return 'normal'\n    },\n    \n    // 导出数据\n    exportData() {\n      this.$message.info('导出功能开发中...')\n    },\n    \n    // 刷新数据\n    refreshData() {\n      this.loadAnalyticsData()\n      this.$message.success('数据已刷新')\n    },\n    \n    // 图片错误处理\n    handleImageError(event) {\n      event.target.src = '/static/images/default-venue.png'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collection-analytics {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-title {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n}\n\n.page-title i {\n  margin-right: 12px;\n  color: #409eff;\n}\n\n.page-description {\n  color: #909399;\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 核心指标卡片 */\n.metrics-section {\n  margin-bottom: 24px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.metric-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.metric-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n}\n\n.metric-card.primary::before {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success::before {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning::before {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info::before {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.metric-card.primary .metric-icon {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success .metric-icon {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning .metric-icon {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info .metric-icon {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon i {\n  font-size: 24px;\n  color: white;\n}\n\n.metric-content h3 {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.metric-content p {\n  margin: 0 0 8px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.metric-trend {\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 12px;\n  background: #f0f9ff;\n  color: #0369a1;\n}\n\n.metric-trend.up {\n  background: #f0fdf4;\n  color: #166534;\n}\n\n.metric-trend i {\n  margin-right: 2px;\n}\n\n/* 图表区域 */\n.charts-section {\n  margin-bottom: 24px;\n}\n\n.chart-card {\n  height: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border-radius: 12px;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n.chart-container {\n  height: 320px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 排行榜 */\n.ranking-list {\n  max-height: 320px;\n  overflow-y: auto;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n  transition: background-color 0.3s ease;\n}\n\n.ranking-item:hover {\n  background-color: #f8f9fa;\n}\n\n.ranking-item.top-three {\n  background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);\n  border-radius: 8px;\n  margin-bottom: 8px;\n  padding: 16px 12px;\n}\n\n.ranking-number {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  margin-right: 12px;\n  color: white;\n  font-size: 14px;\n}\n\n.ranking-number.gold {\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\n}\n\n.ranking-number.silver {\n  background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);\n}\n\n.ranking-number.bronze {\n  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);\n}\n\n.ranking-number.normal {\n  background: #e5e7eb;\n  color: #6b7280;\n}\n\n.venue-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.venue-thumb {\n  width: 48px;\n  height: 48px;\n  border-radius: 8px;\n  object-fit: cover;\n  margin-right: 12px;\n  border: 1px solid #ebeef5;\n}\n\n.venue-details h4 {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.venue-details p {\n  margin: 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n.collection-count {\n  text-align: center;\n}\n\n.collection-count .count {\n  display: block;\n  font-size: 18px;\n  font-weight: 700;\n  color: #f56c6c;\n}\n\n.collection-count .label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 用户行为分析 */\n.behavior-stats {\n  margin-bottom: 24px;\n}\n\n.behavior-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.behavior-label {\n  color: #606266;\n  font-size: 14px;\n}\n\n.behavior-value {\n  font-weight: 600;\n  color: #303133;\n  font-size: 16px;\n}\n\n.time-distribution h4 {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.time-bars {\n  display: flex;\n  align-items: end;\n  justify-content: space-between;\n  height: 120px;\n  padding: 0 8px;\n}\n\n.time-bar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.bar {\n  width: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2px 2px 0 0;\n  margin-bottom: 8px;\n  min-height: 4px;\n  transition: all 0.3s ease;\n}\n\n.time-bar:hover .bar {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.time-label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 数据表格 */\n.data-table-section {\n  margin-bottom: 24px;\n}\n\n.table-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .collection-analytics {\n    padding: 12px;\n  }\n\n  .metrics-section .el-col {\n    margin-bottom: 12px;\n  }\n\n  .charts-section .el-col {\n    margin-bottom: 20px;\n  }\n\n  .metric-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .metric-icon {\n    margin-right: 0;\n    margin-bottom: 12px;\n  }\n\n  .venue-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .venue-thumb {\n    margin-bottom: 8px;\n  }\n}\n</style>\n"]}]}