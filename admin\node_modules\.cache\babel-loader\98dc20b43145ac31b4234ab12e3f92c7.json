{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue", "mtime": 1750603092620}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "dialogVisible", "ruleForm", "user", "heads", "created", "setHeaderStyle", "mounted", "_this", "sessionTable", "$storage", "get", "$http", "url", "method", "then", "_ref", "code", "message", "$message", "error", "msg", "methods", "onLogout", "storage", "router", "$router", "remove", "replace", "name", "onIndexTap", "window", "location", "href", "concat", "$base", "indexUrl", "goToHome", "push", "goToForum", "goToBooking", "_this2", "$nextTick", "document", "querySelectorAll", "for<PERSON>ach", "el", "addEventListener", "e", "stopPropagation", "style", "backgroundColor", "headLogoutFontHoverBgColor", "color", "headLogoutFontHoverColor", "headLogoutFontColor"], "sources": ["src/components/index/IndexHeader.vue"], "sourcesContent": ["<template>\r\n    <!-- <el-header>\r\n        <el-menu background-color=\"#00c292\" text-color=\"#FFFFFF\" active-text-color=\"#FFFFFF\" mode=\"horizontal\">\r\n            <div class=\"fl title\">{{this.$project.projectName}}</div>\r\n            <div class=\"fr logout\" style=\"display:flex;\">\r\n                <el-menu-item index=\"3\">\r\n                    <div>{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n                </el-menu-item>\r\n                <el-menu-item @click=\"onLogout\" index=\"2\">\r\n                    <div>退出登�?/div>\r\n                </el-menu-item>\r\n            </div>\r\n        </el-menu>\r\n    </el-header> -->\r\n    <div class=\"navbar\" :style=\"{backgroundColor:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}\">\r\n        <div class=\"title-menu\" :style=\"{justifyContent:heads.headTitleStyle=='1'?'flex-start':'center'}\">\r\n            <el-image v-if=\"heads.headTitleImg\" class=\"title-img\" :style=\"{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}\" :src=\"heads.headTitleImgUrl\" fit=\"cover\"></el-image>\r\n            <div class=\"title-name\" :style=\"{color:heads.headFontColor,fontSize:heads.headFontSize}\">{{this.$project.projectName}}</div>\r\n\r\n            <!-- 导航菜单 -->\r\n            <div class=\"nav-menu\">\r\n                <div class=\"nav-item\" @click=\"goToHome\">\r\n                    <i class=\"el-icon-house\"></i>\r\n                    <span>首页</span>\r\n                </div>\r\n                <div class=\"nav-item\" @click=\"goToForum\">\r\n                    <i class=\"el-icon-chat-dot-round\"></i>\r\n                    <span>论坛</span>\r\n                </div>\r\n                <div class=\"nav-item\" @click=\"goToBooking\">\r\n                    <i class=\"el-icon-date\"></i>\r\n                    <span>预约</span>\r\n                </div>\r\n                <div class=\"nav-item dropdown\" @mouseenter=\"showCollectionMenu = true\" @mouseleave=\"showCollectionMenu = false\">\r\n                    <i class=\"el-icon-star-on\"></i>\r\n                    <span>收藏</span>\r\n                    <div v-show=\"showCollectionMenu\" class=\"dropdown-menu\">\r\n                        <div class=\"dropdown-item\" @click=\"goToCollectionManagement\">\r\n                            <i class=\"el-icon-s-data\"></i>\r\n                            <span>收藏管理</span>\r\n                        </div>\r\n                        <div class=\"dropdown-item\" @click=\"goToCollectionAnalytics\">\r\n                            <i class=\"el-icon-data-analysis\"></i>\r\n                            <span>收藏分析</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"right-menu\">\r\n            <div class=\"user-info\" :style=\"{color:heads.headUserInfoFontColor,fontSize:heads.headUserInfoFontSize}\">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n\t\t\t<div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onIndexTap\">退出到前台</div>\r\n            <div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onLogout\">退出登录</div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                dialogVisible: false,\r\n                ruleForm: {},\r\n                user: {},\r\n                heads: {\"headLogoutFontHoverColor\":\"#fff\",\"headFontSize\":\"20px\",\"headUserInfoFontColor\":\"#fff\",\"headBoxShadow\":\"0 2px 8px rgba(0,0,0,0.1)\",\"headTitleImgHeight\":\"44px\",\"headLogoutFontHoverBgColor\":\"#00c292\",\"headFontColor\":\"#fff\",\"headTitleImg\":false,\"headHeight\":\"60px\",\"headTitleImgBorderRadius\":\"22px\",\"headTitleImgUrl\":\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\",\"headBgColor\":\"#34495e\",\"headTitleImgBoxShadow\":\"0 1px 6px #444\",\"headLogoutFontColor\":\"#fff\",\"headUserInfoFontSize\":\"16px\",\"headTitleImgWidth\":\"44px\",\"headTitleStyle\":\"1\",\"headLogoutFontSize\":\"16px\"},\r\n            };\r\n        },\r\n        created() {\r\n            this.setHeaderStyle()\r\n        },\r\n        mounted() {\r\n            let sessionTable = this.$storage.get(\"sessionTable\")\r\n            this.$http({\r\n                url: sessionTable + '/session',\r\n                method: \"get\"\r\n            }).then(({\r\n                         data\r\n                     }) => {\r\n                if (data && data.code === 0) {\r\n                    this.user = data.data;\r\n                } else {\r\n                    let message = this.$message\r\n                    message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n        methods: {\r\n            onLogout() {\r\n                let storage = this.$storage\r\n                let router = this.$router\r\n                storage.remove(\"Token\");\r\n                router.replace({\r\n                    name: \"login\"\r\n                });\r\n            },\r\n            onIndexTap(){\r\n                window.location.href = `${this.$base.indexUrl}`\r\n            },\r\n            // 导航方法\r\n            goToHome() {\r\n                this.$router.push('/')\r\n            },\r\n            goToForum() {\r\n                this.$router.push('/forum-new')\r\n            },\r\n            goToBooking() {\r\n                this.$router.push('/changdi-booking')\r\n            },\r\n            setHeaderStyle() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{\r\n                        el.addEventListener(\"mouseenter\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor\r\n                            el.style.color = this.heads.headLogoutFontHoverColor\r\n                        })\r\n                        el.addEventListener(\"mouseleave\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = \"transparent\"\r\n                            el.style.color = this.heads.headLogoutFontColor\r\n                        })\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n    .navbar {\r\n        height: 60px;\r\n        line-height: 60px;\r\n        width: 100%;\r\n        padding: 0 34px;\r\n        box-sizing: border-box;\r\n        background-color: #ff00ff;\r\n        position: relative;\r\n        z-index: 111;\r\n\r\n    .right-menu {\r\n        position: absolute;\r\n        right: 34px;\r\n        top: 0;\r\n        height: 100%;\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        align-items: center;\r\n        z-index: 111;\r\n\r\n    .user-info {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n    }\r\n\r\n    .logout {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n        cursor: pointer;\r\n    }\r\n\r\n    }\r\n\r\n    .title-menu {\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        align-items: center;\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n    .title-img {\r\n        width: 44px;\r\n        height: 44px;\r\n        border-radius: 22px;\r\n        box-shadow: 0 1px 6px #444;\r\n        margin-right: 16px;\r\n    }\r\n\r\n    .title-name {\r\n        font-size: 24px;\r\n        color: #fff;\r\n        font-weight: 700;\r\n        margin-right: 40px;\r\n    }\r\n\r\n    .nav-menu {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 20px;\r\n\r\n        .nav-item {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px;\r\n            padding: 8px 16px;\r\n            border-radius: 6px;\r\n            cursor: pointer;\r\n            transition: all 0.3s ease;\r\n            color: rgba(255, 255, 255, 0.8);\r\n            font-size: 14px;\r\n\r\n            &:hover {\r\n                background-color: rgba(255, 255, 255, 0.1);\r\n                color: #fff;\r\n                transform: translateY(-1px);\r\n            }\r\n\r\n            i {\r\n                font-size: 16px;\r\n            }\r\n        }\r\n    }\r\n    }\r\n    }\r\n    // .el-header .fr {\r\n       // \tfloat: right;\r\n       // }\r\n\r\n    // .el-header .fl {\r\n       // \tfloat: left;\r\n       // }\r\n\r\n    // .el-header {\r\n       // \twidth: 100%;\r\n       // \tcolor: #333;\r\n       // \ttext-align: center;\r\n       // \tline-height: 60px;\r\n       // \tpadding: 0;\r\n       // \tz-index: 99;\r\n       // }\r\n\r\n    // .logo {\r\n       // \twidth: 60px;\r\n       // \theight: 60px;\r\n       // \tmargin-left: 70px;\r\n       // }\r\n\r\n    // .avator {\r\n       // \twidth: 40px;\r\n       // \theight: 40px;\r\n       // \tbackground: #ffffff;\r\n       // \tborder-radius: 50%;\r\n       // }\r\n\r\n    // .title {\r\n       // \tcolor: #ffffff;\r\n       // \tfont-size: 20px;\r\n       // \tfont-weight: bold;\r\n       // \tmargin-left: 20px;\r\n       // }\r\n</style>\r\n\r\n"], "mappings": ";;;;;;AA0DA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,QAAA;MACAC,IAAA;MACAC,KAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;QAAA;MAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,YAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAC,KAAA;MACAC,GAAA,EAAAJ,YAAA;MACAK,MAAA;IACA,GAAAC,IAAA,WAAAC,IAAA,EAEA;MAAA,IADAhB,IAAA,GAAAgB,IAAA,CAAAhB,IAAA;MAEA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;QACAT,KAAA,CAAAL,IAAA,GAAAH,IAAA,CAAAA,IAAA;MACA;QACA,IAAAkB,OAAA,GAAAV,KAAA,CAAAW,QAAA;QACAD,OAAA,CAAAE,KAAA,CAAApB,IAAA,CAAAqB,GAAA;MACA;IACA;EACA;EACAC,OAAA;IACAC,QAAA,WAAAA,SAAA;MACA,IAAAC,OAAA,QAAAd,QAAA;MACA,IAAAe,MAAA,QAAAC,OAAA;MACAF,OAAA,CAAAG,MAAA;MACAF,MAAA,CAAAG,OAAA;QACAC,IAAA;MACA;IACA;IACAC,UAAA,WAAAA,WAAA;MACAC,MAAA,CAAAC,QAAA,CAAAC,IAAA,MAAAC,MAAA,MAAAC,KAAA,CAAAC,QAAA;IACA;IACA;IACAC,QAAA,WAAAA,SAAA;MACA,KAAAX,OAAA,CAAAY,IAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MACA,KAAAb,OAAA,CAAAY,IAAA;IACA;IACAE,WAAA,WAAAA,YAAA;MACA,KAAAd,OAAA,CAAAY,IAAA;IACA;IACAhC,cAAA,WAAAA,eAAA;MAAA,IAAAmC,MAAA;MACA,KAAAC,SAAA;QACAC,QAAA,CAAAC,gBAAA,gCAAAC,OAAA,WAAAC,EAAA;UACAA,EAAA,CAAAC,gBAAA,yBAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAI,KAAA,CAAAC,eAAA,GAAAV,MAAA,CAAArC,KAAA,CAAAgD,0BAAA;YACAN,EAAA,CAAAI,KAAA,CAAAG,KAAA,GAAAZ,MAAA,CAAArC,KAAA,CAAAkD,wBAAA;UACA;UACAR,EAAA,CAAAC,gBAAA,yBAAAC,CAAA;YACAA,CAAA,CAAAC,eAAA;YACAH,EAAA,CAAAI,KAAA,CAAAC,eAAA;YACAL,EAAA,CAAAI,KAAA,CAAAG,KAAA,GAAAZ,MAAA,CAAArC,KAAA,CAAAmD,mBAAA;UACA;QACA;MACA;IACA;EACA;AACA", "ignoreList": []}]}