{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\src\\utils\\collection-storage.js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\utils\\collection-storage.js", "mtime": 1750602797047}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["STORAGE_KEYS", "COLLECTIONS", "SYNC_STATUS", "LAST_SYNC", "CollectionStorage", "_classCallCheck", "collections", "loadCollections", "syncStatus", "loadSyncStatus", "_createClass", "key", "value", "data", "localStorage", "getItem", "JSON", "parse", "error", "console", "saveCollections", "setItem", "stringify", "updateSyncStatus", "addCollection", "venueData", "_this", "collection", "id", "Date", "now", "Math", "random", "changdiId", "changdiName", "name", "changdiPhoto", "photo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "price", "changdiValue", "type", "yonghuId", "getCurrentUserId", "changdiCollectionTypes", "insertTime", "toISOString", "createTime", "isLocal", "exists", "find", "item", "success", "message", "unshift", "removeCollection", "venueId", "userId", "index", "findIndex", "removed", "splice", "isCollected", "some", "getUserCollections", "arguments", "length", "undefined", "targetUserId", "filter", "getAllCollections", "clearCollections", "getCollectionStats", "userCollections", "today", "split", "todayCollections", "startsWith", "typeStats", "for<PERSON>ach", "total", "typeDistribution", "lastCollectionTime", "max", "apply", "_toConsumableArray", "map", "getTime", "_syncToServer", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "httpClient", "pendingCollections", "results", "_iterator", "_step", "response", "wrap", "_callee$", "_context", "prev", "next", "abrupt", "failed", "errors", "_createForOfIteratorHelper", "s", "n", "done", "url", "method", "sent", "code", "push", "concat", "msg", "t0", "t1", "e", "f", "finish", "details", "stop", "syncToServer", "_x", "_pullFromServer", "_callee2", "serverCollections", "_callee2$", "_context2", "params", "page", "limit", "list", "mergeCollections", "pullFromServer", "_x2", "_this2", "serverItem", "localItem", "_objectSpread", "status", "lastSync", "timestamp", "updatedAt", "getSyncStatus", "exportCollections", "exportTime", "version", "blob", "Blob", "URL", "createObjectURL", "a", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "importCollections", "file", "_this3", "Promise", "resolve", "reject", "reader", "FileReader", "onload", "target", "result", "Array", "isArray", "Error", "onerror", "readAsText", "collectionStorage"], "sources": ["D:/1/tiyuguan/admin/src/utils/collection-storage.js"], "sourcesContent": ["/**\n * 收藏本地存储管理工具\n * 提供收藏数据的本地存储、同步和管理功能\n */\n\nconst STORAGE_KEYS = {\n  COLLECTIONS: 'local_collections',\n  SYNC_STATUS: 'collection_sync_status',\n  LAST_SYNC: 'collection_last_sync'\n}\n\nclass CollectionStorage {\n  constructor() {\n    this.collections = this.loadCollections()\n    this.syncStatus = this.loadSyncStatus()\n  }\n\n  /**\n   * 加载本地收藏数据\n   */\n  loadCollections() {\n    try {\n      const data = localStorage.getItem(STORAGE_KEYS.COLLECTIONS)\n      return data ? JSON.parse(data) : []\n    } catch (error) {\n      console.error('加载本地收藏数据失败:', error)\n      return []\n    }\n  }\n\n  /**\n   * 保存收藏数据到本地存储\n   */\n  saveCollections() {\n    try {\n      localStorage.setItem(STORAGE_KEYS.COLLECTIONS, JSON.stringify(this.collections))\n      this.updateSyncStatus('pending')\n      return true\n    } catch (error) {\n      console.error('保存收藏数据失败:', error)\n      return false\n    }\n  }\n\n  /**\n   * 添加收藏\n   */\n  addCollection(venueData) {\n    const collection = {\n      id: Date.now() + Math.random(), // 临时ID\n      changdiId: venueData.id,\n      changdiName: venueData.changdiName || venueData.name,\n      changdiPhoto: venueData.changdiPhoto || venueData.photo,\n      changdiNewMoney: venueData.changdiNewMoney || venueData.price,\n      changdiValue: venueData.changdiValue || venueData.type,\n      yonghuId: this.getCurrentUserId(),\n      changdiCollectionTypes: 1,\n      insertTime: new Date().toISOString(),\n      createTime: new Date().toISOString(),\n      isLocal: true, // 标记为本地数据\n      syncStatus: 'pending' // 同步状态：pending, synced, failed\n    }\n\n    // 检查是否已存在\n    const exists = this.collections.find(item => \n      item.changdiId === venueData.id && \n      item.yonghuId === this.getCurrentUserId()\n    )\n\n    if (exists) {\n      return { success: false, message: '已经收藏过了' }\n    }\n\n    this.collections.unshift(collection)\n    this.saveCollections()\n\n    return { success: true, data: collection }\n  }\n\n  /**\n   * 移除收藏\n   */\n  removeCollection(venueId) {\n    const userId = this.getCurrentUserId()\n    const index = this.collections.findIndex(item => \n      item.changdiId === venueId && \n      item.yonghuId === userId\n    )\n\n    if (index === -1) {\n      return { success: false, message: '收藏不存在' }\n    }\n\n    const removed = this.collections.splice(index, 1)[0]\n    this.saveCollections()\n\n    return { success: true, data: removed }\n  }\n\n  /**\n   * 检查是否已收藏\n   */\n  isCollected(venueId) {\n    const userId = this.getCurrentUserId()\n    return this.collections.some(item => \n      item.changdiId === venueId && \n      item.yonghuId === userId\n    )\n  }\n\n  /**\n   * 获取用户的收藏列表\n   */\n  getUserCollections(userId = null) {\n    const targetUserId = userId || this.getCurrentUserId()\n    return this.collections.filter(item => item.yonghuId === targetUserId)\n  }\n\n  /**\n   * 获取所有收藏数据\n   */\n  getAllCollections() {\n    return this.collections\n  }\n\n  /**\n   * 清空收藏数据\n   */\n  clearCollections() {\n    this.collections = []\n    this.saveCollections()\n  }\n\n  /**\n   * 获取收藏统计\n   */\n  getCollectionStats() {\n    const userId = this.getCurrentUserId()\n    const userCollections = this.getUserCollections(userId)\n    \n    const today = new Date().toISOString().split('T')[0]\n    const todayCollections = userCollections.filter(item => \n      item.insertTime && item.insertTime.startsWith(today)\n    )\n\n    // 按场地类型分组\n    const typeStats = {}\n    userCollections.forEach(item => {\n      const type = item.changdiValue || '未知'\n      typeStats[type] = (typeStats[type] || 0) + 1\n    })\n\n    return {\n      total: userCollections.length,\n      today: todayCollections.length,\n      typeDistribution: typeStats,\n      lastCollectionTime: userCollections.length > 0 ? \n        Math.max(...userCollections.map(item => new Date(item.insertTime).getTime())) : null\n    }\n  }\n\n  /**\n   * 同步到服务器\n   */\n  async syncToServer(httpClient) {\n    const pendingCollections = this.collections.filter(item => \n      item.isLocal && item.syncStatus === 'pending'\n    )\n\n    if (pendingCollections.length === 0) {\n      return { success: true, message: '没有需要同步的数据' }\n    }\n\n    const results = {\n      success: 0,\n      failed: 0,\n      errors: []\n    }\n\n    for (const collection of pendingCollections) {\n      try {\n        const response = await httpClient({\n          url: 'changdiCollection/add',\n          method: 'post',\n          data: {\n            yonghuId: collection.yonghuId,\n            changdiId: collection.changdiId,\n            changdiCollectionTypes: collection.changdiCollectionTypes\n          }\n        })\n\n        if (response.data && response.data.code === 0) {\n          // 同步成功，更新状态\n          collection.syncStatus = 'synced'\n          collection.isLocal = false\n          results.success++\n        } else {\n          collection.syncStatus = 'failed'\n          results.failed++\n          results.errors.push(`${collection.changdiName}: ${response.data.msg || '同步失败'}`)\n        }\n      } catch (error) {\n        collection.syncStatus = 'failed'\n        results.failed++\n        results.errors.push(`${collection.changdiName}: ${error.message}`)\n      }\n    }\n\n    this.saveCollections()\n    this.updateSyncStatus('completed', new Date().toISOString())\n\n    return {\n      success: results.failed === 0,\n      message: `同步完成：成功 ${results.success} 个，失败 ${results.failed} 个`,\n      details: results\n    }\n  }\n\n  /**\n   * 从服务器拉取收藏数据\n   */\n  async pullFromServer(httpClient) {\n    try {\n      const userId = this.getCurrentUserId()\n      if (!userId) {\n        return { success: false, message: '用户未登录' }\n      }\n\n      const response = await httpClient({\n        url: 'changdiCollection/list',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 1000,\n          yonghuId: userId\n        }\n      })\n\n      if (response.data && response.data.code === 0) {\n        const serverCollections = response.data.data.list || []\n        \n        // 合并服务器数据和本地数据\n        this.mergeCollections(serverCollections)\n        \n        return { \n          success: true, \n          message: `从服务器拉取了 ${serverCollections.length} 条收藏记录`,\n          data: serverCollections\n        }\n      } else {\n        return { \n          success: false, \n          message: response.data.msg || '拉取数据失败' \n        }\n      }\n    } catch (error) {\n      console.error('从服务器拉取收藏数据失败:', error)\n      return { \n        success: false, \n        message: '网络错误，无法拉取数据' \n      }\n    }\n  }\n\n  /**\n   * 合并服务器数据和本地数据\n   */\n  mergeCollections(serverCollections) {\n    const userId = this.getCurrentUserId()\n    \n    // 移除已同步的本地数据\n    this.collections = this.collections.filter(item => \n      item.yonghuId !== userId || item.syncStatus === 'pending'\n    )\n\n    // 添加服务器数据\n    serverCollections.forEach(serverItem => {\n      const exists = this.collections.find(localItem => \n        localItem.changdiId === serverItem.changdiId && \n        localItem.yonghuId === serverItem.yonghuId\n      )\n\n      if (!exists) {\n        this.collections.push({\n          ...serverItem,\n          isLocal: false,\n          syncStatus: 'synced'\n        })\n      }\n    })\n\n    this.saveCollections()\n  }\n\n  /**\n   * 获取当前用户ID\n   */\n  getCurrentUserId() {\n    return localStorage.getItem('userid') || localStorage.getItem('userId')\n  }\n\n  /**\n   * 加载同步状态\n   */\n  loadSyncStatus() {\n    try {\n      const data = localStorage.getItem(STORAGE_KEYS.SYNC_STATUS)\n      return data ? JSON.parse(data) : { status: 'never', lastSync: null }\n    } catch (error) {\n      return { status: 'never', lastSync: null }\n    }\n  }\n\n  /**\n   * 更新同步状态\n   */\n  updateSyncStatus(status, timestamp = null) {\n    this.syncStatus = {\n      status,\n      lastSync: timestamp || this.syncStatus.lastSync,\n      updatedAt: new Date().toISOString()\n    }\n\n    try {\n      localStorage.setItem(STORAGE_KEYS.SYNC_STATUS, JSON.stringify(this.syncStatus))\n    } catch (error) {\n      console.error('更新同步状态失败:', error)\n    }\n  }\n\n  /**\n   * 获取同步状态\n   */\n  getSyncStatus() {\n    return this.syncStatus\n  }\n\n  /**\n   * 导出收藏数据\n   */\n  exportCollections() {\n    const data = {\n      collections: this.collections,\n      syncStatus: this.syncStatus,\n      exportTime: new Date().toISOString(),\n      version: '1.0'\n    }\n\n    const blob = new Blob([JSON.stringify(data, null, 2)], { \n      type: 'application/json' \n    })\n    \n    const url = URL.createObjectURL(blob)\n    const a = document.createElement('a')\n    a.href = url\n    a.download = `collections_backup_${new Date().toISOString().split('T')[0]}.json`\n    document.body.appendChild(a)\n    a.click()\n    document.body.removeChild(a)\n    URL.revokeObjectURL(url)\n  }\n\n  /**\n   * 导入收藏数据\n   */\n  importCollections(file) {\n    return new Promise((resolve, reject) => {\n      const reader = new FileReader()\n      \n      reader.onload = (e) => {\n        try {\n          const data = JSON.parse(e.target.result)\n          \n          if (data.collections && Array.isArray(data.collections)) {\n            this.collections = data.collections\n            this.saveCollections()\n            \n            if (data.syncStatus) {\n              this.syncStatus = data.syncStatus\n              this.updateSyncStatus(data.syncStatus.status, data.syncStatus.lastSync)\n            }\n            \n            resolve({\n              success: true,\n              message: `成功导入 ${data.collections.length} 条收藏记录`\n            })\n          } else {\n            reject(new Error('无效的备份文件格式'))\n          }\n        } catch (error) {\n          reject(new Error('解析备份文件失败: ' + error.message))\n        }\n      }\n      \n      reader.onerror = () => {\n        reject(new Error('读取文件失败'))\n      }\n      \n      reader.readAsText(file)\n    })\n  }\n}\n\n// 创建单例实例\nconst collectionStorage = new CollectionStorage()\n\nexport default collectionStorage\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;;AAEA,IAAMA,YAAY,GAAG;EACnBC,WAAW,EAAE,mBAAmB;EAChCC,WAAW,EAAE,wBAAwB;EACrCC,SAAS,EAAE;AACb,CAAC;AAAA,IAEKC,iBAAiB;EACrB,SAAAA,kBAAA,EAAc;IAAAC,eAAA,OAAAD,iBAAA;IACZ,IAAI,CAACE,WAAW,GAAG,IAAI,CAACC,eAAe,CAAC,CAAC;IACzC,IAAI,CAACC,UAAU,GAAG,IAAI,CAACC,cAAc,CAAC,CAAC;EACzC;;EAEA;AACF;AACA;EAFE,OAAAC,YAAA,CAAAN,iBAAA;IAAAO,GAAA;IAAAC,KAAA,EAGA,SAAAL,eAAeA,CAAA,EAAG;MAChB,IAAI;QACF,IAAMM,IAAI,GAAGC,YAAY,CAACC,OAAO,CAACf,YAAY,CAACC,WAAW,CAAC;QAC3D,OAAOY,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,GAAG,EAAE;MACrC,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;QACnC,OAAO,EAAE;MACX;IACF;;IAEA;AACF;AACA;EAFE;IAAAP,GAAA;IAAAC,KAAA,EAGA,SAAAQ,eAAeA,CAAA,EAAG;MAChB,IAAI;QACFN,YAAY,CAACO,OAAO,CAACrB,YAAY,CAACC,WAAW,EAAEe,IAAI,CAACM,SAAS,CAAC,IAAI,CAAChB,WAAW,CAAC,CAAC;QAChF,IAAI,CAACiB,gBAAgB,CAAC,SAAS,CAAC;QAChC,OAAO,IAAI;MACb,CAAC,CAAC,OAAOL,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;QACjC,OAAO,KAAK;MACd;IACF;;IAEA;AACF;AACA;EAFE;IAAAP,GAAA;IAAAC,KAAA,EAGA,SAAAY,aAAaA,CAACC,SAAS,EAAE;MAAA,IAAAC,KAAA;MACvB,IAAMC,UAAU,GAAG;QACjBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC;QAAE;QAChCC,SAAS,EAAER,SAAS,CAACG,EAAE;QACvBM,WAAW,EAAET,SAAS,CAACS,WAAW,IAAIT,SAAS,CAACU,IAAI;QACpDC,YAAY,EAAEX,SAAS,CAACW,YAAY,IAAIX,SAAS,CAACY,KAAK;QACvDC,eAAe,EAAEb,SAAS,CAACa,eAAe,IAAIb,SAAS,CAACc,KAAK;QAC7DC,YAAY,EAAEf,SAAS,CAACe,YAAY,IAAIf,SAAS,CAACgB,IAAI;QACtDC,QAAQ,EAAE,IAAI,CAACC,gBAAgB,CAAC,CAAC;QACjCC,sBAAsB,EAAE,CAAC;QACzBC,UAAU,EAAE,IAAIhB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;QACpCC,UAAU,EAAE,IAAIlB,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;QACpCE,OAAO,EAAE,IAAI;QAAE;QACfxC,UAAU,EAAE,SAAS,CAAC;MACxB,CAAC;;MAED;MACA,IAAMyC,MAAM,GAAG,IAAI,CAAC3C,WAAW,CAAC4C,IAAI,CAAC,UAAAC,IAAI;QAAA,OACvCA,IAAI,CAAClB,SAAS,KAAKR,SAAS,CAACG,EAAE,IAC/BuB,IAAI,CAACT,QAAQ,KAAKhB,KAAI,CAACiB,gBAAgB,CAAC,CAAC;MAAA,CAC3C,CAAC;MAED,IAAIM,MAAM,EAAE;QACV,OAAO;UAAEG,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAS,CAAC;MAC9C;MAEA,IAAI,CAAC/C,WAAW,CAACgD,OAAO,CAAC3B,UAAU,CAAC;MACpC,IAAI,CAACP,eAAe,CAAC,CAAC;MAEtB,OAAO;QAAEgC,OAAO,EAAE,IAAI;QAAEvC,IAAI,EAAEc;MAAW,CAAC;IAC5C;;IAEA;AACF;AACA;EAFE;IAAAhB,GAAA;IAAAC,KAAA,EAGA,SAAA2C,gBAAgBA,CAACC,OAAO,EAAE;MACxB,IAAMC,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;MACtC,IAAMe,KAAK,GAAG,IAAI,CAACpD,WAAW,CAACqD,SAAS,CAAC,UAAAR,IAAI;QAAA,OAC3CA,IAAI,CAAClB,SAAS,KAAKuB,OAAO,IAC1BL,IAAI,CAACT,QAAQ,KAAKe,MAAM;MAAA,CAC1B,CAAC;MAED,IAAIC,KAAK,KAAK,CAAC,CAAC,EAAE;QAChB,OAAO;UAAEN,OAAO,EAAE,KAAK;UAAEC,OAAO,EAAE;QAAQ,CAAC;MAC7C;MAEA,IAAMO,OAAO,GAAG,IAAI,CAACtD,WAAW,CAACuD,MAAM,CAACH,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACpD,IAAI,CAACtC,eAAe,CAAC,CAAC;MAEtB,OAAO;QAAEgC,OAAO,EAAE,IAAI;QAAEvC,IAAI,EAAE+C;MAAQ,CAAC;IACzC;;IAEA;AACF;AACA;EAFE;IAAAjD,GAAA;IAAAC,KAAA,EAGA,SAAAkD,WAAWA,CAACN,OAAO,EAAE;MACnB,IAAMC,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;MACtC,OAAO,IAAI,CAACrC,WAAW,CAACyD,IAAI,CAAC,UAAAZ,IAAI;QAAA,OAC/BA,IAAI,CAAClB,SAAS,KAAKuB,OAAO,IAC1BL,IAAI,CAACT,QAAQ,KAAKe,MAAM;MAAA,CAC1B,CAAC;IACH;;IAEA;AACF;AACA;EAFE;IAAA9C,GAAA;IAAAC,KAAA,EAGA,SAAAoD,kBAAkBA,CAAA,EAAgB;MAAA,IAAfP,MAAM,GAAAQ,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MAC9B,IAAMG,YAAY,GAAGX,MAAM,IAAI,IAAI,CAACd,gBAAgB,CAAC,CAAC;MACtD,OAAO,IAAI,CAACrC,WAAW,CAAC+D,MAAM,CAAC,UAAAlB,IAAI;QAAA,OAAIA,IAAI,CAACT,QAAQ,KAAK0B,YAAY;MAAA,EAAC;IACxE;;IAEA;AACF;AACA;EAFE;IAAAzD,GAAA;IAAAC,KAAA,EAGA,SAAA0D,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAAChE,WAAW;IACzB;;IAEA;AACF;AACA;EAFE;IAAAK,GAAA;IAAAC,KAAA,EAGA,SAAA2D,gBAAgBA,CAAA,EAAG;MACjB,IAAI,CAACjE,WAAW,GAAG,EAAE;MACrB,IAAI,CAACc,eAAe,CAAC,CAAC;IACxB;;IAEA;AACF;AACA;EAFE;IAAAT,GAAA;IAAAC,KAAA,EAGA,SAAA4D,kBAAkBA,CAAA,EAAG;MACnB,IAAMf,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;MACtC,IAAM8B,eAAe,GAAG,IAAI,CAACT,kBAAkB,CAACP,MAAM,CAAC;MAEvD,IAAMiB,KAAK,GAAG,IAAI7C,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACpD,IAAMC,gBAAgB,GAAGH,eAAe,CAACJ,MAAM,CAAC,UAAAlB,IAAI;QAAA,OAClDA,IAAI,CAACN,UAAU,IAAIM,IAAI,CAACN,UAAU,CAACgC,UAAU,CAACH,KAAK,CAAC;MAAA,CACtD,CAAC;;MAED;MACA,IAAMI,SAAS,GAAG,CAAC,CAAC;MACpBL,eAAe,CAACM,OAAO,CAAC,UAAA5B,IAAI,EAAI;QAC9B,IAAMV,IAAI,GAAGU,IAAI,CAACX,YAAY,IAAI,IAAI;QACtCsC,SAAS,CAACrC,IAAI,CAAC,GAAG,CAACqC,SAAS,CAACrC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;MAC9C,CAAC,CAAC;MAEF,OAAO;QACLuC,KAAK,EAAEP,eAAe,CAACP,MAAM;QAC7BQ,KAAK,EAAEE,gBAAgB,CAACV,MAAM;QAC9Be,gBAAgB,EAAEH,SAAS;QAC3BI,kBAAkB,EAAET,eAAe,CAACP,MAAM,GAAG,CAAC,GAC5CnC,IAAI,CAACoD,GAAG,CAAAC,KAAA,CAARrD,IAAI,EAAAsD,kBAAA,CAAQZ,eAAe,CAACa,GAAG,CAAC,UAAAnC,IAAI;UAAA,OAAI,IAAItB,IAAI,CAACsB,IAAI,CAACN,UAAU,CAAC,CAAC0C,OAAO,CAAC,CAAC;QAAA,EAAC,EAAC,GAAG;MACpF,CAAC;IACH;;IAEA;AACF;AACA;EAFE;IAAA5E,GAAA;IAAAC,KAAA;MAAA,IAAA4E,aAAA,GAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAGA,SAAAC,QAAmBC,UAAU;QAAA,IAAAC,kBAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,KAAA,EAAAtE,UAAA,EAAAuE,QAAA;QAAA,OAAAR,mBAAA,GAAAS,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACrBT,kBAAkB,GAAG,IAAI,CAACxF,WAAW,CAAC+D,MAAM,CAAC,UAAAlB,IAAI;gBAAA,OACrDA,IAAI,CAACH,OAAO,IAAIG,IAAI,CAAC3C,UAAU,KAAK,SAAS;cAAA,CAC/C,CAAC;cAAA,MAEGsF,kBAAkB,CAAC5B,MAAM,KAAK,CAAC;gBAAAmC,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAA,OAAAF,QAAA,CAAAG,MAAA,WAC1B;gBAAEpD,OAAO,EAAE,IAAI;gBAAEC,OAAO,EAAE;cAAY,CAAC;YAAA;cAG1C0C,OAAO,GAAG;gBACd3C,OAAO,EAAE,CAAC;gBACVqD,MAAM,EAAE,CAAC;gBACTC,MAAM,EAAE;cACV,CAAC;cAAAV,SAAA,GAAAW,0BAAA,CAEwBb,kBAAkB;cAAAO,QAAA,CAAAC,IAAA;cAAAN,SAAA,CAAAY,CAAA;YAAA;cAAA,KAAAX,KAAA,GAAAD,SAAA,CAAAa,CAAA,IAAAC,IAAA;gBAAAT,QAAA,CAAAE,IAAA;gBAAA;cAAA;cAAhC5E,UAAU,GAAAsE,KAAA,CAAArF,KAAA;cAAAyF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEMV,UAAU,CAAC;gBAChCkB,GAAG,EAAE,uBAAuB;gBAC5BC,MAAM,EAAE,MAAM;gBACdnG,IAAI,EAAE;kBACJ6B,QAAQ,EAAEf,UAAU,CAACe,QAAQ;kBAC7BT,SAAS,EAAEN,UAAU,CAACM,SAAS;kBAC/BW,sBAAsB,EAAEjB,UAAU,CAACiB;gBACrC;cACF,CAAC,CAAC;YAAA;cARIsD,QAAQ,GAAAG,QAAA,CAAAY,IAAA;cAUd,IAAIf,QAAQ,CAACrF,IAAI,IAAIqF,QAAQ,CAACrF,IAAI,CAACqG,IAAI,KAAK,CAAC,EAAE;gBAC7C;gBACAvF,UAAU,CAACnB,UAAU,GAAG,QAAQ;gBAChCmB,UAAU,CAACqB,OAAO,GAAG,KAAK;gBAC1B+C,OAAO,CAAC3C,OAAO,EAAE;cACnB,CAAC,MAAM;gBACLzB,UAAU,CAACnB,UAAU,GAAG,QAAQ;gBAChCuF,OAAO,CAACU,MAAM,EAAE;gBAChBV,OAAO,CAACW,MAAM,CAACS,IAAI,IAAAC,MAAA,CAAIzF,UAAU,CAACO,WAAW,QAAAkF,MAAA,CAAKlB,QAAQ,CAACrF,IAAI,CAACwG,GAAG,IAAI,MAAM,CAAE,CAAC;cAClF;cAAChB,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAiB,EAAA,GAAAjB,QAAA;cAED1E,UAAU,CAACnB,UAAU,GAAG,QAAQ;cAChCuF,OAAO,CAACU,MAAM,EAAE;cAChBV,OAAO,CAACW,MAAM,CAACS,IAAI,IAAAC,MAAA,CAAIzF,UAAU,CAACO,WAAW,QAAAkF,MAAA,CAAKf,QAAA,CAAAiB,EAAA,CAAMjE,OAAO,CAAE,CAAC;YAAA;cAAAgD,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAkB,EAAA,GAAAlB,QAAA;cAAAL,SAAA,CAAAwB,CAAA,CAAAnB,QAAA,CAAAkB,EAAA;YAAA;cAAAlB,QAAA,CAAAC,IAAA;cAAAN,SAAA,CAAAyB,CAAA;cAAA,OAAApB,QAAA,CAAAqB,MAAA;YAAA;cAItE,IAAI,CAACtG,eAAe,CAAC,CAAC;cACtB,IAAI,CAACG,gBAAgB,CAAC,WAAW,EAAE,IAAIM,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC,CAAC;cAAA,OAAAuD,QAAA,CAAAG,MAAA,WAErD;gBACLpD,OAAO,EAAE2C,OAAO,CAACU,MAAM,KAAK,CAAC;gBAC7BpD,OAAO,gDAAA+D,MAAA,CAAarB,OAAO,CAAC3C,OAAO,gCAAAgE,MAAA,CAASrB,OAAO,CAACU,MAAM,YAAI;gBAC9DkB,OAAO,EAAE5B;cACX,CAAC;YAAA;YAAA;cAAA,OAAAM,QAAA,CAAAuB,IAAA;UAAA;QAAA,GAAAhC,OAAA;MAAA,CACF;MAAA,SApDKiC,YAAYA,CAAAC,EAAA;QAAA,OAAAtC,aAAA,CAAAJ,KAAA,OAAAnB,SAAA;MAAA;MAAA,OAAZ4D,YAAY;IAAA;IAsDlB;AACF;AACA;IAFE;EAAA;IAAAlH,GAAA;IAAAC,KAAA;MAAA,IAAAmH,eAAA,GAAAtC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,CAGA,SAAAqC,SAAqBnC,UAAU;QAAA,IAAApC,MAAA,EAAAyC,QAAA,EAAA+B,iBAAA;QAAA,OAAAvC,mBAAA,GAAAS,IAAA,UAAA+B,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAA7B,IAAA,GAAA6B,SAAA,CAAA5B,IAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAErB7C,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;cAAA,IACjCc,MAAM;gBAAA0E,SAAA,CAAA5B,IAAA;gBAAA;cAAA;cAAA,OAAA4B,SAAA,CAAA3B,MAAA,WACF;gBAAEpD,OAAO,EAAE,KAAK;gBAAEC,OAAO,EAAE;cAAQ,CAAC;YAAA;cAAA8E,SAAA,CAAA5B,IAAA;cAAA,OAGtBV,UAAU,CAAC;gBAChCkB,GAAG,EAAE,wBAAwB;gBAC7BC,MAAM,EAAE,KAAK;gBACboB,MAAM,EAAE;kBACNC,IAAI,EAAE,CAAC;kBACPC,KAAK,EAAE,IAAI;kBACX5F,QAAQ,EAAEe;gBACZ;cACF,CAAC,CAAC;YAAA;cARIyC,QAAQ,GAAAiC,SAAA,CAAAlB,IAAA;cAAA,MAUVf,QAAQ,CAACrF,IAAI,IAAIqF,QAAQ,CAACrF,IAAI,CAACqG,IAAI,KAAK,CAAC;gBAAAiB,SAAA,CAAA5B,IAAA;gBAAA;cAAA;cACrC0B,iBAAiB,GAAG/B,QAAQ,CAACrF,IAAI,CAACA,IAAI,CAAC0H,IAAI,IAAI,EAAE,EAEvD;cACA,IAAI,CAACC,gBAAgB,CAACP,iBAAiB,CAAC;cAAA,OAAAE,SAAA,CAAA3B,MAAA,WAEjC;gBACLpD,OAAO,EAAE,IAAI;gBACbC,OAAO,gDAAA+D,MAAA,CAAaa,iBAAiB,CAAC/D,MAAM,oCAAQ;gBACpDrD,IAAI,EAAEoH;cACR,CAAC;YAAA;cAAA,OAAAE,SAAA,CAAA3B,MAAA,WAEM;gBACLpD,OAAO,EAAE,KAAK;gBACdC,OAAO,EAAE6C,QAAQ,CAACrF,IAAI,CAACwG,GAAG,IAAI;cAChC,CAAC;YAAA;cAAAc,SAAA,CAAA5B,IAAA;cAAA;YAAA;cAAA4B,SAAA,CAAA7B,IAAA;cAAA6B,SAAA,CAAAb,EAAA,GAAAa,SAAA;cAGHhH,OAAO,CAACD,KAAK,CAAC,eAAe,EAAAiH,SAAA,CAAAb,EAAO,CAAC;cAAA,OAAAa,SAAA,CAAA3B,MAAA,WAC9B;gBACLpD,OAAO,EAAE,KAAK;gBACdC,OAAO,EAAE;cACX,CAAC;YAAA;YAAA;cAAA,OAAA8E,SAAA,CAAAP,IAAA;UAAA;QAAA,GAAAI,QAAA;MAAA,CAEJ;MAAA,SAzCKS,cAAcA,CAAAC,GAAA;QAAA,OAAAX,eAAA,CAAA3C,KAAA,OAAAnB,SAAA;MAAA;MAAA,OAAdwE,cAAc;IAAA;IA2CpB;AACF;AACA;IAFE;EAAA;IAAA9H,GAAA;IAAAC,KAAA,EAGA,SAAA4H,gBAAgBA,CAACP,iBAAiB,EAAE;MAAA,IAAAU,MAAA;MAClC,IAAMlF,MAAM,GAAG,IAAI,CAACd,gBAAgB,CAAC,CAAC;;MAEtC;MACA,IAAI,CAACrC,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC+D,MAAM,CAAC,UAAAlB,IAAI;QAAA,OAC7CA,IAAI,CAACT,QAAQ,KAAKe,MAAM,IAAIN,IAAI,CAAC3C,UAAU,KAAK,SAAS;MAAA,CAC3D,CAAC;;MAED;MACAyH,iBAAiB,CAAClD,OAAO,CAAC,UAAA6D,UAAU,EAAI;QACtC,IAAM3F,MAAM,GAAG0F,MAAI,CAACrI,WAAW,CAAC4C,IAAI,CAAC,UAAA2F,SAAS;UAAA,OAC5CA,SAAS,CAAC5G,SAAS,KAAK2G,UAAU,CAAC3G,SAAS,IAC5C4G,SAAS,CAACnG,QAAQ,KAAKkG,UAAU,CAAClG,QAAQ;QAAA,CAC5C,CAAC;QAED,IAAI,CAACO,MAAM,EAAE;UACX0F,MAAI,CAACrI,WAAW,CAAC6G,IAAI,CAAA2B,aAAA,CAAAA,aAAA,KAChBF,UAAU;YACb5F,OAAO,EAAE,KAAK;YACdxC,UAAU,EAAE;UAAQ,EACrB,CAAC;QACJ;MACF,CAAC,CAAC;MAEF,IAAI,CAACY,eAAe,CAAC,CAAC;IACxB;;IAEA;AACF;AACA;EAFE;IAAAT,GAAA;IAAAC,KAAA,EAGA,SAAA+B,gBAAgBA,CAAA,EAAG;MACjB,OAAO7B,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAID,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC;IACzE;;IAEA;AACF;AACA;EAFE;IAAAJ,GAAA;IAAAC,KAAA,EAGA,SAAAH,cAAcA,CAAA,EAAG;MACf,IAAI;QACF,IAAMI,IAAI,GAAGC,YAAY,CAACC,OAAO,CAACf,YAAY,CAACE,WAAW,CAAC;QAC3D,OAAOW,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC,GAAG;UAAEkI,MAAM,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAK,CAAC;MACtE,CAAC,CAAC,OAAO9H,KAAK,EAAE;QACd,OAAO;UAAE6H,MAAM,EAAE,OAAO;UAAEC,QAAQ,EAAE;QAAK,CAAC;MAC5C;IACF;;IAEA;AACF;AACA;EAFE;IAAArI,GAAA;IAAAC,KAAA,EAGA,SAAAW,gBAAgBA,CAACwH,MAAM,EAAoB;MAAA,IAAlBE,SAAS,GAAAhF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;MACvC,IAAI,CAACzD,UAAU,GAAG;QAChBuI,MAAM,EAANA,MAAM;QACNC,QAAQ,EAAEC,SAAS,IAAI,IAAI,CAACzI,UAAU,CAACwI,QAAQ;QAC/CE,SAAS,EAAE,IAAIrH,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC;MACpC,CAAC;MAED,IAAI;QACFhC,YAAY,CAACO,OAAO,CAACrB,YAAY,CAACE,WAAW,EAAEc,IAAI,CAACM,SAAS,CAAC,IAAI,CAACd,UAAU,CAAC,CAAC;MACjF,CAAC,CAAC,OAAOU,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACnC;IACF;;IAEA;AACF;AACA;EAFE;IAAAP,GAAA;IAAAC,KAAA,EAGA,SAAAuI,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC3I,UAAU;IACxB;;IAEA;AACF;AACA;EAFE;IAAAG,GAAA;IAAAC,KAAA,EAGA,SAAAwI,iBAAiBA,CAAA,EAAG;MAClB,IAAMvI,IAAI,GAAG;QACXP,WAAW,EAAE,IAAI,CAACA,WAAW;QAC7BE,UAAU,EAAE,IAAI,CAACA,UAAU;QAC3B6I,UAAU,EAAE,IAAIxH,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC;QACpCwG,OAAO,EAAE;MACX,CAAC;MAED,IAAMC,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACxI,IAAI,CAACM,SAAS,CAACT,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QACrD4B,IAAI,EAAE;MACR,CAAC,CAAC;MAEF,IAAMsE,GAAG,GAAG0C,GAAG,CAACC,eAAe,CAACH,IAAI,CAAC;MACrC,IAAMI,CAAC,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;MACrCF,CAAC,CAACG,IAAI,GAAG/C,GAAG;MACZ4C,CAAC,CAACI,QAAQ,yBAAA3C,MAAA,CAAyB,IAAIvF,IAAI,CAAC,CAAC,CAACiB,WAAW,CAAC,CAAC,CAAC6B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UAAO;MAChFiF,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,CAAC,CAAC;MAC5BA,CAAC,CAACO,KAAK,CAAC,CAAC;MACTN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,CAAC,CAAC;MAC5BF,GAAG,CAACW,eAAe,CAACrD,GAAG,CAAC;IAC1B;;IAEA;AACF;AACA;EAFE;IAAApG,GAAA;IAAAC,KAAA,EAGA,SAAAyJ,iBAAiBA,CAACC,IAAI,EAAE;MAAA,IAAAC,MAAA;MACtB,OAAO,IAAIC,OAAO,CAAC,UAACC,OAAO,EAAEC,MAAM,EAAK;QACtC,IAAMC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;QAE/BD,MAAM,CAACE,MAAM,GAAG,UAACrD,CAAC,EAAK;UACrB,IAAI;YACF,IAAM3G,IAAI,GAAGG,IAAI,CAACC,KAAK,CAACuG,CAAC,CAACsD,MAAM,CAACC,MAAM,CAAC;YAExC,IAAIlK,IAAI,CAACP,WAAW,IAAI0K,KAAK,CAACC,OAAO,CAACpK,IAAI,CAACP,WAAW,CAAC,EAAE;cACvDiK,MAAI,CAACjK,WAAW,GAAGO,IAAI,CAACP,WAAW;cACnCiK,MAAI,CAACnJ,eAAe,CAAC,CAAC;cAEtB,IAAIP,IAAI,CAACL,UAAU,EAAE;gBACnB+J,MAAI,CAAC/J,UAAU,GAAGK,IAAI,CAACL,UAAU;gBACjC+J,MAAI,CAAChJ,gBAAgB,CAACV,IAAI,CAACL,UAAU,CAACuI,MAAM,EAAElI,IAAI,CAACL,UAAU,CAACwI,QAAQ,CAAC;cACzE;cAEAyB,OAAO,CAAC;gBACNrH,OAAO,EAAE,IAAI;gBACbC,OAAO,8BAAA+D,MAAA,CAAUvG,IAAI,CAACP,WAAW,CAAC4D,MAAM;cAC1C,CAAC,CAAC;YACJ,CAAC,MAAM;cACLwG,MAAM,CAAC,IAAIQ,KAAK,CAAC,WAAW,CAAC,CAAC;YAChC;UACF,CAAC,CAAC,OAAOhK,KAAK,EAAE;YACdwJ,MAAM,CAAC,IAAIQ,KAAK,CAAC,YAAY,GAAGhK,KAAK,CAACmC,OAAO,CAAC,CAAC;UACjD;QACF,CAAC;QAEDsH,MAAM,CAACQ,OAAO,GAAG,YAAM;UACrBT,MAAM,CAAC,IAAIQ,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B,CAAC;QAEDP,MAAM,CAACS,UAAU,CAACd,IAAI,CAAC;MACzB,CAAC,CAAC;IACJ;EAAC;AAAA,KAGH;AACA,IAAMe,iBAAiB,GAAG,IAAIjL,iBAAiB,CAAC,CAAC;AAEjD,eAAeiL,iBAAiB", "ignoreList": []}]}