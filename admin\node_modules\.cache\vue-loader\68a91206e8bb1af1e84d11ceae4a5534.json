{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue", "mtime": 1750598680704}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HomeStats.vue"], "names": [], "mappings": ";AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HomeStats.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\n  <div class=\"stats-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" v-for=\"(stat, index) in statsData\" :key=\"index\">\n        <div class=\"stat-card\" :class=\"`stat-${index + 1}`\">\n          <div class=\"stat-icon\">\n            <i :class=\"stat.icon\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stat.value }}</div>\n            <div class=\"stat-label\">{{ stat.label }}</div>\n          </div>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeStats',\n  data() {\n    return {\n      statsData: [\n        {\n          label: '总用户数',\n          value: 0,\n          icon: 'el-icon-user'\n        },\n        {\n          label: '场地数量',\n          value: 0,\n          icon: 'el-icon-office-building'\n        },\n        {\n          label: '今日预约',\n          value: 0,\n          icon: 'el-icon-date'\n        },\n        {\n          label: '总收入',\n          value: 0,\n          icon: 'el-icon-money'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadStatsData()\n  },\n  methods: {\n    loadStatsData() {\n      // 获取用户统计\n      this.$http({\n        url: 'yonghu/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[0].value = data.data.total || 0\n        }\n      }).catch(() => {\n        console.error('获取用户统计失败')\n      })\n\n      // 获取场地统计\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[1].value = data.data.total || 0\n        }\n      }).catch(() => {\n        console.error('获取场地统计失败')\n      })\n\n      // 获取今日预约统计\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'changdiOrder/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 1,\n          buyTime: today\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[2].value = data.data.total || 0\n        }\n      }).catch(() => {\n        console.error('获取今日预约统计失败')\n      })\n\n      // 获取总收入统计\n      this.getTotalRevenue()\n    },\n\n    // 获取总收入\n    getTotalRevenue() {\n      this.$http({\n        url: 'changdiOrder/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 1000, // 获取更多数据来计算总收入\n          changdiOrderTypes: 1 // 只统计已确认的预约\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          let totalRevenue = 0\n          const orders = data.data.list || []\n\n          orders.forEach(order => {\n            if (order.changdiOrderTruePrice) {\n              totalRevenue += parseFloat(order.changdiOrderTruePrice) || 0\n            }\n          })\n\n          this.statsData[3].value = '¥' + totalRevenue.toFixed(2)\n        }\n      }).catch(() => {\n        console.error('获取收入统计失败')\n        this.statsData[3].value = '¥0.00'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.stats-container {\n  margin-bottom: 20px;\n  \n  .stat-card {\n    background: white;\n    border-radius: 8px;\n    padding: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    }\n    \n    .stat-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 15px;\n      \n      i {\n        font-size: 24px;\n        color: white;\n      }\n    }\n    \n    .stat-content {\n      flex: 1;\n      \n      .stat-number {\n        font-size: 24px;\n        font-weight: bold;\n        color: #2c3e50;\n        margin-bottom: 5px;\n      }\n      \n      .stat-label {\n        font-size: 14px;\n        color: #909399;\n      }\n    }\n    \n    &.stat-1 .stat-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n    \n    &.stat-2 .stat-icon {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n    }\n    \n    &.stat-3 .stat-icon {\n      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n    }\n    \n    &.stat-4 .stat-icon {\n      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n    }\n  }\n}\n</style>\n"]}]}