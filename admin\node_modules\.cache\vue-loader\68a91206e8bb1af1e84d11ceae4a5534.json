{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue", "mtime": 1750589767885}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HomeStats.vue"], "names": [], "mappings": ";AAmBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "HomeStats.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\n  <div class=\"stats-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" v-for=\"(stat, index) in statsData\" :key=\"index\">\n        <div class=\"stat-card\" :class=\"`stat-${index + 1}`\">\n          <div class=\"stat-icon\">\n            <i :class=\"stat.icon\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stat.value }}</div>\n            <div class=\"stat-label\">{{ stat.label }}</div>\n          </div>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeStats',\n  data() {\n    return {\n      statsData: [\n        {\n          label: '总用户数',\n          value: 0,\n          icon: 'el-icon-user'\n        },\n        {\n          label: '场地数量',\n          value: 0,\n          icon: 'el-icon-office-building'\n        },\n        {\n          label: '今日预约',\n          value: 0,\n          icon: 'el-icon-date'\n        },\n        {\n          label: '总收入',\n          value: 0,\n          icon: 'el-icon-money'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadStatsData()\n  },\n  methods: {\n    loadStatsData() {\n      // 获取用户统计\n      this.$http({\n        url: 'users/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[0].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 获取场地统计\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[1].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 获取预约统计\n      this.$http({\n        url: 'yuyuexinxi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[2].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 模拟收入数据\n      this.statsData[3].value = '¥' + (Math.random() * 100000).toFixed(0)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.stats-container {\n  margin-bottom: 20px;\n  \n  .stat-card {\n    background: white;\n    border-radius: 8px;\n    padding: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    }\n    \n    .stat-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 15px;\n      \n      i {\n        font-size: 24px;\n        color: white;\n      }\n    }\n    \n    .stat-content {\n      flex: 1;\n      \n      .stat-number {\n        font-size: 24px;\n        font-weight: bold;\n        color: #2c3e50;\n        margin-bottom: 5px;\n      }\n      \n      .stat-label {\n        font-size: 14px;\n        color: #909399;\n      }\n    }\n    \n    &.stat-1 .stat-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n    \n    &.stat-2 .stat-icon {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n    }\n    \n    &.stat-3 .stat-icon {\n      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n    }\n    \n    &.stat-4 .stat-icon {\n      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n    }\n  }\n}\n</style>\n"]}]}