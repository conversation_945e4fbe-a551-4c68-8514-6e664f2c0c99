{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\home.vue", "mtime": 1750589826593}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["router", "HomeCarousel", "HomeStats", "HomeChart", "HomeCard", "components", "data", "currentDate", "mounted", "init", "getCurrentDate", "methods", "$storage", "get", "$http", "url", "concat", "method", "then", "_ref", "code", "push", "name", "now", "Date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "weekdays", "weekday", "getDay"], "sources": ["src/views/home.vue"], "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <el-alert :closable=\"false\" title=\"欢迎使用体育馆管理平台\" type=\"success\">\r\n        <template slot>\r\n          <div>\r\n            <p>您好，欢迎使用体育馆管理平台！今天是 {{ currentDate }}</p>\r\n          </div>\r\n        </template>\r\n      </el-alert>\r\n    </div>\r\n\r\n    <!-- 轮播图 -->\r\n    <HomeCarousel />\r\n\r\n    <!-- 统计卡片 -->\r\n    <HomeStats />\r\n\r\n    <!-- 图表和卡片 -->\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"16\">\r\n        <div class=\"chart-container\">\r\n          <h3>数据统计</h3>\r\n          <HomeChart />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"8\">\r\n        <div class=\"card-container\">\r\n          <h3>快捷操作</h3>\r\n          <HomeCard />\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport router from '@/router/router-static'\r\nimport HomeCarousel from '@/components/home/<USER>'\r\nimport HomeStats from '@/components/home/<USER>'\r\nimport HomeChart from '@/components/home/<USER>'\r\nimport HomeCard from '@/components/home/<USER>'\r\n\r\nexport default {\r\n  components: {\r\n    HomeCarousel,\r\n    HomeStats,\r\n    HomeChart,\r\n    HomeCard\r\n  },\r\n  data() {\r\n    return {\r\n      currentDate: ''\r\n    }\r\n  },\r\n  mounted(){\r\n    this.init();\r\n    this.getCurrentDate();\r\n  },\r\n  methods:{\r\n    init(){\r\n        if(this.$storage.get('Token')){\r\n        this.$http({\r\n            url: `${this.$storage.get('sessionTable')}/session`,\r\n            method: \"get\"\r\n        }).then(({ data }) => {\r\n            if (data && data.code != 0) {\r\n            router.push({ name: 'login' })\r\n            }\r\n        });\r\n        }else{\r\n            router.push({ name: 'login' })\r\n        }\r\n    },\r\n    getCurrentDate() {\r\n      const now = new Date()\r\n      const year = now.getFullYear()\r\n      const month = String(now.getMonth() + 1).padStart(2, '0')\r\n      const day = String(now.getDate()).padStart(2, '0')\r\n      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\r\n      const weekday = weekdays[now.getDay()]\r\n      this.currentDate = `${year}年${month}月${day}日 ${weekday}`\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.home-container {\r\n  padding: 20px;\r\n\r\n  .welcome-banner {\r\n    margin-bottom: 20px;\r\n\r\n    ::v-deep .el-alert {\r\n      border-radius: 8px;\r\n      border: none;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n      .el-alert__title {\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .el-alert__content {\r\n        font-size: 14px;\r\n        margin-top: 8px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .chart-container, .card-container {\r\n    background: white;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    h3 {\r\n      margin: 0 0 20px 0;\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #2c3e50;\r\n      border-bottom: 2px solid #00c292;\r\n      padding-bottom: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;AAqCA,OAAAA,MAAA;AACA,OAAAC,YAAA;AACA,OAAAC,SAAA;AACA,OAAAC,SAAA;AACA,OAAAC,QAAA;AAEA;EACAC,UAAA;IACAJ,YAAA,EAAAA,YAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,SAAA,EAAAA,SAAA;IACAC,QAAA,EAAAA;EACA;EACAE,IAAA,WAAAA,KAAA;IACA;MACAC,WAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,IAAA;IACA,KAAAC,cAAA;EACA;EACAC,OAAA;IACAF,IAAA,WAAAA,KAAA;MACA,SAAAG,QAAA,CAAAC,GAAA;QACA,KAAAC,KAAA;UACAC,GAAA,KAAAC,MAAA,MAAAJ,QAAA,CAAAC,GAAA;UACAI,MAAA;QACA,GAAAC,IAAA,WAAAC,IAAA;UAAA,IAAAb,IAAA,GAAAa,IAAA,CAAAb,IAAA;UACA,IAAAA,IAAA,IAAAA,IAAA,CAAAc,IAAA;YACApB,MAAA,CAAAqB,IAAA;cAAAC,IAAA;YAAA;UACA;QACA;MACA;QACAtB,MAAA,CAAAqB,IAAA;UAAAC,IAAA;QAAA;MACA;IACA;IACAZ,cAAA,WAAAA,eAAA;MACA,IAAAa,GAAA,OAAAC,IAAA;MACA,IAAAC,IAAA,GAAAF,GAAA,CAAAG,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAL,GAAA,CAAAM,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAL,GAAA,CAAAS,OAAA,IAAAF,QAAA;MACA,IAAAG,QAAA;MACA,IAAAC,OAAA,GAAAD,QAAA,CAAAV,GAAA,CAAAY,MAAA;MACA,KAAA5B,WAAA,MAAAS,MAAA,CAAAS,IAAA,YAAAT,MAAA,CAAAW,KAAA,YAAAX,MAAA,CAAAe,GAAA,aAAAf,MAAA,CAAAkB,OAAA;IACA;EACA;AACA", "ignoreList": []}]}