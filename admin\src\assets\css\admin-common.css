/* 管理后台通用样式 */

/* 主容器样式 */
.main-content {
  padding: 20px;
  background: #f8f9fa;
  min-height: calc(100vh - 60px);
}

/* 搜索表单样式 */
.form-content {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.form-content .slt {
  margin-bottom: 15px !important;
}

.form-content .slt .el-form-item {
  margin-bottom: 0;
  margin-right: 20px;
}

.form-content .slt .el-input__inner {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
}

.form-content .slt .el-input__inner:focus {
  border-color: #00c292;
  box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);
}

/* 操作按钮区域 */
.form-content .ad {
  padding-top: 15px;
  border-top: 1px solid #ebeef5;
  margin-top: 15px;
}

.form-content .ad .el-button {
  border-radius: 6px;
  padding: 10px 20px;
  font-weight: 600;
  margin-right: 10px;
  margin-bottom: 10px;
}

.form-content .ad .el-button--success {
  background: linear-gradient(135deg, #00c292 0%, #00a085 100%);
  border: none;
  color: white;
}

.form-content .ad .el-button--success:hover {
  background: linear-gradient(135deg, #00a085 0%, #008f75 100%);
}

.form-content .ad .el-button--danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
  color: white;
}

.form-content .ad .el-button--danger:hover {
  background: linear-gradient(135deg, #ee5a52 0%, #dc3545 100%);
}

.form-content .ad .el-button--warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  border: none;
  color: white;
}

.form-content .ad .el-button--warning:hover {
  background: linear-gradient(135deg, #e0a800 0%, #d39e00 100%);
}

/* 表格容器样式 */
.table-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 表格样式 */
.tables {
  border-radius: 8px 8px 0 0;
}

.tables .el-table__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.tables .el-table__header th {
  background: transparent !important;
  color: white !important;
  font-weight: 600;
  border: none;
}

.tables .el-table__body tr:hover {
  background-color: #f8f9fa !important;
}

.tables .el-table__body .el-button {
  margin: 2px;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
}

.tables .el-table__body .el-button--success {
  background: rgba(0, 194, 146, 0.1);
  border: 1px solid #00c292;
  color: #00c292;
}

.tables .el-table__body .el-button--success:hover {
  background: #00c292;
  color: white;
}

.tables .el-table__body .el-button--primary {
  background: rgba(64, 158, 255, 0.1);
  border: 1px solid #409eff;
  color: #409eff;
}

.tables .el-table__body .el-button--primary:hover {
  background: #409eff;
  color: white;
}

.tables .el-table__body .el-button--danger {
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid #f56c6c;
  color: #f56c6c;
}

.tables .el-table__body .el-button--danger:hover {
  background: #f56c6c;
  color: white;
}

.tables .el-table__body .el-button--warning {
  background: rgba(230, 162, 60, 0.1);
  border: 1px solid #e6a23c;
  color: #e6a23c;
}

.tables .el-table__body .el-button--warning:hover {
  background: #e6a23c;
  color: white;
}

/* 分页样式 */
.pagination-content {
  padding: 20px;
  background: white;
  border-top: 1px solid #ebeef5;
}

.pagination-content .el-pagination {
  text-align: center;
}

.pagination-content .el-pagination .el-pager li {
  border-radius: 4px;
  margin: 0 2px;
}

.pagination-content .el-pagination .el-pager li.active {
  background: #00c292;
  border-color: #00c292;
}

.pagination-content .el-pagination .btn-prev,
.pagination-content .el-pagination .btn-next {
  border-radius: 4px;
}

/* 图片预览样式 */
.table-content img {
  border-radius: 6px;
  border: 2px solid #ebeef5;
  transition: all 0.3s ease;
  cursor: pointer;
}

.table-content img:hover {
  border-color: #00c292;
  transform: scale(1.05);
}

/* 状态标签样式 */
.status-tag {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-tag.active {
  background: rgba(0, 194, 146, 0.1);
  color: #00c292;
  border: 1px solid #00c292;
}

.status-tag.inactive {
  background: rgba(245, 108, 108, 0.1);
  color: #f56c6c;
  border: 1px solid #f56c6c;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding: 10px;
  }
  
  .form-content {
    padding: 15px;
  }
  
  .form-content .slt {
    flex-direction: column;
  }
  
  .form-content .slt .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .form-content .ad {
    text-align: center;
  }
  
  .tables .el-table__body .el-button {
    margin: 1px;
    padding: 4px 8px;
    font-size: 11px;
  }
}

/* 加载动画优化 */
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
}

.el-loading-spinner .circular {
  width: 50px;
  height: 50px;
}

.el-loading-spinner .path {
  stroke: #00c292;
}

/* 消息提示样式优化 */
.el-message--success {
  background-color: #f0f9ff;
  border-color: #00c292;
}

.el-message--success .el-message__icon {
  color: #00c292;
}

/* 对话框样式优化 */
.el-dialog__header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
}

.el-dialog__title {
  color: white;
  font-weight: 600;
}

.el-dialog__headerbtn .el-dialog__close {
  color: white;
}

.el-dialog__body {
  padding: 30px;
}

/* 表单样式优化 */
.el-form-item__label {
  font-weight: 600;
  color: #2c3e50;
}

.el-input__inner,
.el-textarea__inner,
.el-select .el-input__inner {
  border-radius: 6px;
  border: 1px solid #dcdfe6;
  transition: all 0.3s ease;
}

.el-input__inner:focus,
.el-textarea__inner:focus,
.el-select .el-input__inner:focus {
  border-color: #00c292;
  box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);
}

/* 上传组件样式 */
.el-upload-dragger {
  border-radius: 8px;
  border: 2px dashed #d9d9d9;
  transition: all 0.3s ease;
}

.el-upload-dragger:hover {
  border-color: #00c292;
}

.el-upload-dragger .el-icon-upload {
  color: #00c292;
}
