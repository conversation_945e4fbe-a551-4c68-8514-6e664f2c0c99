{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\home.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\home.vue", "mtime": 1750589826593}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQppbXBvcnQgcm91dGVyIGZyb20gJ0Avcm91dGVyL3JvdXRlci1zdGF0aWMnDQppbXBvcnQgSG9tZUNhcm91c2VsIGZyb20gJ0AvY29tcG9uZW50cy9ob21lL0hvbWVDYXJvdXNlbC52dWUnDQppbXBvcnQgSG9tZVN0YXRzIGZyb20gJ0AvY29tcG9uZW50cy9ob21lL0hvbWVTdGF0cy52dWUnDQppbXBvcnQgSG9tZUNoYXJ0IGZyb20gJ0AvY29tcG9uZW50cy9ob21lL0hvbWVDaGFydC52dWUnDQppbXBvcnQgSG9tZUNhcmQgZnJvbSAnQC9jb21wb25lbnRzL2hvbWUvSG9tZUNhcmQudnVlJw0KDQpleHBvcnQgZGVmYXVsdCB7DQogIGNvbXBvbmVudHM6IHsNCiAgICBIb21lQ2Fyb3VzZWwsDQogICAgSG9tZVN0YXRzLA0KICAgIEhvbWVDaGFydCwNCiAgICBIb21lQ2FyZA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBjdXJyZW50RGF0ZTogJycNCiAgICB9DQogIH0sDQogIG1vdW50ZWQoKXsNCiAgICB0aGlzLmluaXQoKTsNCiAgICB0aGlzLmdldEN1cnJlbnREYXRlKCk7DQogIH0sDQogIG1ldGhvZHM6ew0KICAgIGluaXQoKXsNCiAgICAgICAgaWYodGhpcy4kc3RvcmFnZS5nZXQoJ1Rva2VuJykpew0KICAgICAgICB0aGlzLiRodHRwKHsNCiAgICAgICAgICAgIHVybDogYCR7dGhpcy4kc3RvcmFnZS5nZXQoJ3Nlc3Npb25UYWJsZScpfS9zZXNzaW9uYCwNCiAgICAgICAgICAgIG1ldGhvZDogImdldCINCiAgICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSAhPSAwKSB7DQogICAgICAgICAgICByb3V0ZXIucHVzaCh7IG5hbWU6ICdsb2dpbicgfSkNCiAgICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICAgIH1lbHNlew0KICAgICAgICAgICAgcm91dGVyLnB1c2goeyBuYW1lOiAnbG9naW4nIH0pDQogICAgICAgIH0NCiAgICB9LA0KICAgIGdldEN1cnJlbnREYXRlKCkgew0KICAgICAgY29uc3Qgbm93ID0gbmV3IERhdGUoKQ0KICAgICAgY29uc3QgeWVhciA9IG5vdy5nZXRGdWxsWWVhcigpDQogICAgICBjb25zdCBtb250aCA9IFN0cmluZyhub3cuZ2V0TW9udGgoKSArIDEpLnBhZFN0YXJ0KDIsICcwJykNCiAgICAgIGNvbnN0IGRheSA9IFN0cmluZyhub3cuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpDQogICAgICBjb25zdCB3ZWVrZGF5cyA9IFsn5pif5pyf5pelJywgJ+aYn+acn+S4gCcsICfmmJ/mnJ/kuownLCAn5pif5pyf5LiJJywgJ+aYn+acn+WbmycsICfmmJ/mnJ/kupQnLCAn5pif5pyf5YWtJ10NCiAgICAgIGNvbnN0IHdlZWtkYXkgPSB3ZWVrZGF5c1tub3cuZ2V0RGF5KCldDQogICAgICB0aGlzLmN1cnJlbnREYXRlID0gYCR7eWVhcn3lubQke21vbnRofeaciCR7ZGF5feaXpSAke3dlZWtkYXl9YA0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";AAqCA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <el-alert :closable=\"false\" title=\"欢迎使用体育馆管理平台\" type=\"success\">\r\n        <template slot>\r\n          <div>\r\n            <p>您好，欢迎使用体育馆管理平台！今天是 {{ currentDate }}</p>\r\n          </div>\r\n        </template>\r\n      </el-alert>\r\n    </div>\r\n\r\n    <!-- 轮播图 -->\r\n    <HomeCarousel />\r\n\r\n    <!-- 统计卡片 -->\r\n    <HomeStats />\r\n\r\n    <!-- 图表和卡片 -->\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"16\">\r\n        <div class=\"chart-container\">\r\n          <h3>数据统计</h3>\r\n          <HomeChart />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"8\">\r\n        <div class=\"card-container\">\r\n          <h3>快捷操作</h3>\r\n          <HomeCard />\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport router from '@/router/router-static'\r\nimport HomeCarousel from '@/components/home/<USER>'\r\nimport HomeStats from '@/components/home/<USER>'\r\nimport HomeChart from '@/components/home/<USER>'\r\nimport HomeCard from '@/components/home/<USER>'\r\n\r\nexport default {\r\n  components: {\r\n    HomeCarousel,\r\n    HomeStats,\r\n    HomeChart,\r\n    HomeCard\r\n  },\r\n  data() {\r\n    return {\r\n      currentDate: ''\r\n    }\r\n  },\r\n  mounted(){\r\n    this.init();\r\n    this.getCurrentDate();\r\n  },\r\n  methods:{\r\n    init(){\r\n        if(this.$storage.get('Token')){\r\n        this.$http({\r\n            url: `${this.$storage.get('sessionTable')}/session`,\r\n            method: \"get\"\r\n        }).then(({ data }) => {\r\n            if (data && data.code != 0) {\r\n            router.push({ name: 'login' })\r\n            }\r\n        });\r\n        }else{\r\n            router.push({ name: 'login' })\r\n        }\r\n    },\r\n    getCurrentDate() {\r\n      const now = new Date()\r\n      const year = now.getFullYear()\r\n      const month = String(now.getMonth() + 1).padStart(2, '0')\r\n      const day = String(now.getDate()).padStart(2, '0')\r\n      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\r\n      const weekday = weekdays[now.getDay()]\r\n      this.currentDate = `${year}年${month}月${day}日 ${weekday}`\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.home-container {\r\n  padding: 20px;\r\n\r\n  .welcome-banner {\r\n    margin-bottom: 20px;\r\n\r\n    ::v-deep .el-alert {\r\n      border-radius: 8px;\r\n      border: none;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n      .el-alert__title {\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .el-alert__content {\r\n        font-size: 14px;\r\n        margin-top: 8px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .chart-container, .card-container {\r\n    background: white;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    h3 {\r\n      margin: 0 0 20px 0;\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #2c3e50;\r\n      border-bottom: 2px solid #00c292;\r\n      padding-bottom: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}