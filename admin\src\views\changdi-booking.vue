<template>
  <div class="booking-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1><i class="el-icon-date"></i> 场地预约</h1>
          <p>选择您喜欢的场地，享受运动的乐趣</p>
        </div>
        <div class="stats-cards">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-location"></i>
            </div>
            <div class="stat-info">
              <h3>{{ totalVenues }}</h3>
              <p>可用场地</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-time"></i>
            </div>
            <div class="stat-info">
              <h3>{{ availableSlots }}</h3>
              <p>可预约时段</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="stat-info">
              <h3>{{ myBookings }}</h3>
              <p>我的预约</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索筛选区域 -->
    <div class="search-section">
      <el-card class="search-card">
        <div class="search-form">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="search-item">
                <label>场地类型</label>
                <el-select v-model="searchForm.changdiTypes" placeholder="请选择场地类型" clearable>
                  <el-option label="全部类型" value=""></el-option>
                  <el-option
                    v-for="item in changdiTypesOptions"
                    :key="item.codeIndex"
                    :label="item.indexName"
                    :value="item.codeIndex">
                  </el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="search-item">
                <label>预约日期</label>
                <el-date-picker
                  v-model="searchForm.bookingDate"
                  type="date"
                  placeholder="选择预约日期"
                  :picker-options="datePickerOptions"
                  clearable>
                </el-date-picker>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="search-item">
                <label>价格范围</label>
                <el-select v-model="searchForm.priceRange" placeholder="请选择价格范围" clearable>
                  <el-option label="全部价格" value=""></el-option>
                  <el-option label="0-50元" value="0-50"></el-option>
                  <el-option label="50-100元" value="50-100"></el-option>
                  <el-option label="100-200元" value="100-200"></el-option>
                  <el-option label="200元以上" value="200+"></el-option>
                </el-select>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="search-actions">
                <el-button type="primary" @click="searchVenues" :loading="searchLoading">
                  <i class="el-icon-search"></i>
                  搜索场地
                </el-button>
                <el-button @click="resetSearch">
                  <i class="el-icon-refresh"></i>
                  重置
                </el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>

    <!-- 场地列表 -->
    <div class="venues-section">
      <div class="section-header">
        <h2>可预约场地</h2>
        <div class="view-toggle">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="grid">
              <i class="el-icon-menu"></i> 网格视图
            </el-radio-button>
            <el-radio-button label="list">
              <i class="el-icon-tickets"></i> 列表视图
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="venues-grid" v-loading="dataListLoading">
        <div v-if="dataList.length === 0" class="empty-state">
          <i class="el-icon-basketball"></i>
          <h3>暂无可预约场地</h3>
          <p>请尝试调整搜索条件</p>
        </div>
        <div v-else class="venue-cards">
          <div v-for="venue in dataList" :key="venue.id" class="venue-card">
            <div class="venue-image">
              <img :src="venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'" :alt="venue.changdiName">
              <div class="venue-status" :class="venue.shangxiaTypes === 1 ? 'available' : 'unavailable'">
                {{ venue.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}
              </div>
            </div>
            <div class="venue-info">
              <h3>{{ venue.changdiName }}</h3>
              <div class="venue-details">
                <div class="detail-item">
                  <i class="el-icon-location"></i>
                  <span>{{ venue.changdiValue }}</span>
                </div>
                <div class="detail-item">
                  <i class="el-icon-time"></i>
                  <span>{{ venue.shijianduan }}</span>
                </div>
                <div class="detail-item">
                  <i class="el-icon-star-on"></i>
                  <span>{{ venue.banquanValue }}</span>
                </div>
              </div>
              <div class="venue-price">
                <span class="current-price">¥{{ venue.changdiNewMoney }}</span>
                <span v-if="venue.changdiOldMoney !== venue.changdiNewMoney" class="original-price">
                  ¥{{ venue.changdiOldMoney }}
                </span>
              </div>
              <div class="venue-actions">
                <el-button type="primary" @click="bookVenue(venue)" :disabled="venue.shangxiaTypes !== 1">
                  <i class="el-icon-date"></i>
                  立即预约
                </el-button>
                <el-button type="text" @click="viewVenueDetails(venue)">
                  <i class="el-icon-view"></i>
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-if="viewMode === 'list'" class="venues-list" v-loading="dataListLoading">
        <el-table :data="dataList" style="width: 100%">
          <el-table-column prop="changdiPhoto" label="场地图片" width="120">
            <template slot-scope="scope">
              <img :src="scope.row.changdiPhoto || '/tiyuguan/img/noimg.jpg'" 
                   style="width: 80px; height: 60px; object-fit: cover; border-radius: 4px;">
            </template>
          </el-table-column>
          <el-table-column prop="changdiName" label="场地名称" min-width="150"></el-table-column>
          <el-table-column prop="changdiValue" label="场地类型" width="120"></el-table-column>
          <el-table-column prop="shijianduan" label="时间段" width="150"></el-table-column>
          <el-table-column prop="banquanValue" label="半全场" width="100"></el-table-column>
          <el-table-column label="价格" width="120">
            <template slot-scope="scope">
              <div class="price-cell">
                <span class="current-price">¥{{ scope.row.changdiNewMoney }}</span>
                <span v-if="scope.row.changdiOldMoney !== scope.row.changdiNewMoney" class="original-price">
                  ¥{{ scope.row.changdiOldMoney }}
                </span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="状态" width="100">
            <template slot-scope="scope">
              <el-tag :type="scope.row.shangxiaTypes === 1 ? 'success' : 'danger'">
                {{ scope.row.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200">
            <template slot-scope="scope">
              <el-button type="primary" size="mini" @click="bookVenue(scope.row)" 
                         :disabled="scope.row.shangxiaTypes !== 1">
                立即预约
              </el-button>
              <el-button type="text" size="mini" @click="viewVenueDetails(scope.row)">
                查看详情
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          @size-change="sizeChangeHandle"
          @current-change="currentChangeHandle"
          :current-page="pageIndex"
          :page-sizes="[12, 24, 48]"
          :page-size="pageSize"
          :total="totalPage"
          layout="total, sizes, prev, pager, next, jumper">
        </el-pagination>
      </div>
    </div>

    <!-- 预约对话框 -->
    <!-- <booking-dialog
      v-if="bookingDialogVisible"
      :visible.sync="bookingDialogVisible"
      :venue="selectedVenue"
      @booking-success="onBookingSuccess">
    </booking-dialog> -->

    <!-- 场地详情对话框 -->
    <!-- <venue-details-dialog
      v-if="detailsDialogVisible"
      :visible.sync="detailsDialogVisible"
      :venue="selectedVenue">
    </venue-details-dialog> -->
  </div>
</template>

<script>
// import BookingDialog from './components/BookingDialog.vue'
// import VenueDetailsDialog from './components/VenueDetailsDialog.vue'

export default {
  name: 'ChangdiBooking',
  components: {
    // BookingDialog,
    // VenueDetailsDialog
  },
  data() {
    return {
      // 统计数据
      totalVenues: 0,
      availableSlots: 0,
      myBookings: 0,
      
      // 搜索表单
      searchForm: {
        changdiTypes: '',
        bookingDate: '',
        priceRange: ''
      },
      
      // 视图模式
      viewMode: 'grid',
      
      // 数据列表
      dataList: [],
      pageIndex: 1,
      pageSize: 12,
      totalPage: 0,
      dataListLoading: false,
      searchLoading: false,
      
      // 选项数据
      changdiTypesOptions: [],
      
      // 对话框状态
      bookingDialogVisible: false,
      detailsDialogVisible: false,
      selectedVenue: null,
      
      // 日期选择器配置
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期
        }
      }
    }
  },
  
  created() {
    this.init()
    this.getDataList()
    this.getStats()
  },
  
  methods: {
    // 初始化
    init() {
      this.getChangdiTypesOptions()
    },
    
    // 获取场地类型选项
    getChangdiTypesOptions() {
      this.$http({
        url: 'dictionary/page',
        method: 'get',
        params: {
          page: 1,
          limit: 100,
          dicCode: 'changdi_types'
        }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.changdiTypesOptions = data.data.list || []
        }
      })
    },
    
    // 获取统计数据
    getStats() {
      // 获取总场地数
      this.$http({
        url: 'changdi/page',
        method: 'get',
        params: { page: 1, limit: 1 }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.totalVenues = data.data.total || 0
        }
      })
      
      // 获取我的预约数（如果是用户登录）
      const role = this.$storage.get('role')
      if (role === '用户') {
        this.$http({
          url: 'changdiOrder/page',
          method: 'get',
          params: { page: 1, limit: 1 }
        }).then(({ data }) => {
          if (data && data.code === 0) {
            this.myBookings = data.data.total || 0
          }
        })
      }
    },

    // 获取场地列表
    getDataList() {
      this.dataListLoading = true
      const params = {
        page: this.pageIndex,
        limit: this.pageSize,
        shangxiaTypes: 1 // 只获取上架的场地
      }

      // 添加搜索条件
      if (this.searchForm.changdiTypes) {
        params.changdiTypes = this.searchForm.changdiTypes
      }

      this.$http({
        url: 'changdi/page',
        method: 'get',
        params: params
      }).then(({ data }) => {
        this.dataListLoading = false
        if (data && data.code === 0) {
          this.dataList = data.data.list || []
          this.totalPage = data.data.total || 0
          this.availableSlots = this.dataList.length * 8 // 假设每个场地有8个时段
        } else {
          this.$message.error(data.msg || '获取场地列表失败')
        }
      }).catch(() => {
        this.dataListLoading = false
        this.$message.error('网络错误，请稍后重试')
      })
    },

    // 搜索场地
    searchVenues() {
      this.searchLoading = true
      this.pageIndex = 1
      setTimeout(() => {
        this.getDataList()
        this.searchLoading = false
      }, 500)
    },

    // 重置搜索
    resetSearch() {
      this.searchForm = {
        changdiTypes: '',
        bookingDate: '',
        priceRange: ''
      }
      this.pageIndex = 1
      this.getDataList()
    },

    // 预约场地
    bookVenue(venue) {
      const role = this.$storage.get('role')
      if (!role || role !== '用户') {
        this.$message.warning('请先登录用户账户')
        this.$router.push('/login')
        return
      }

      // 暂时使用简单的预约逻辑
      this.$confirm(`确定要预约场地"${venue.changdiName}"吗？`, '预约确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里可以调用预约API
        this.$message.success('预约功能开发中，敬请期待！')
      }).catch(() => {
        this.$message.info('已取消预约')
      })
    },

    // 查看场地详情
    viewVenueDetails(venue) {
      this.$alert(`
        <div style="text-align: left;">
          <h3>${venue.changdiName}</h3>
          <p><strong>场地编号：</strong>${venue.changdiUuidNumber}</p>
          <p><strong>场地类型：</strong>${venue.changdiValue}</p>
          <p><strong>价格：</strong>¥${venue.changdiNewMoney}</p>
          <p><strong>时间段：</strong>${venue.shijianduan}</p>
          <p><strong>半全场：</strong>${venue.banquanValue}</p>
          <p><strong>推荐信息：</strong>${venue.tuijian || '暂无'}</p>
        </div>
      `, '场地详情', {
        dangerouslyUseHTMLString: true,
        confirmButtonText: '关闭'
      })
    },

    // 预约成功回调
    onBookingSuccess() {
      this.$message.success('预约成功！')
      this.getStats() // 刷新统计数据
    },

    // 分页处理
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },

    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    }
  }
}
</script>

<style lang="scss" scoped>
.booking-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);

  .page-header {
    margin-bottom: 30px;

    .header-content {
      .title-section {
        text-align: center;
        margin-bottom: 30px;

        h1 {
          font-size: 32px;
          color: #2c3e50;
          margin: 0 0 10px 0;

          i {
            color: #00c292;
            margin-right: 15px;
          }
        }

        p {
          color: #909399;
          font-size: 18px;
          margin: 0;
        }
      }

      .stats-cards {
        display: flex;
        justify-content: center;
        gap: 30px;

        .stat-card {
          background: white;
          border-radius: 12px;
          padding: 25px;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          display: flex;
          align-items: center;
          gap: 20px;
          min-width: 200px;
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
          }

          .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #00c292, #00a085);
            display: flex;
            align-items: center;
            justify-content: center;

            i {
              font-size: 24px;
              color: white;
            }
          }

          .stat-info {
            h3 {
              font-size: 28px;
              font-weight: 700;
              color: #2c3e50;
              margin: 0 0 5px 0;
            }

            p {
              color: #909399;
              margin: 0;
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .search-section {
    margin-bottom: 30px;

    .search-card {
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

      .search-form {
        .search-item {
          label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
            font-size: 14px;
          }

          ::v-deep .el-input__inner,
          ::v-deep .el-select .el-input__inner {
            border-radius: 8px;
            border: 2px solid #e8f4f8;
            transition: all 0.3s ease;

            &:focus {
              border-color: #00c292;
              box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.1);
            }
          }
        }

        .search-actions {
          display: flex;
          gap: 10px;
          align-items: end;
          height: 100%;
          padding-top: 22px;

          .el-button {
            border-radius: 8px;
            font-weight: 600;

            &.el-button--primary {
              background: linear-gradient(45deg, #00c292, #00a085);
              border: none;

              &:hover {
                background: linear-gradient(45deg, #00a085, #008f75);
              }
            }
          }
        }
      }
    }
  }

  .venues-section {
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;

      h2 {
        font-size: 24px;
        color: #2c3e50;
        margin: 0;
      }

      .view-toggle {
        ::v-deep .el-radio-group {
          .el-radio-button__inner {
            border-radius: 8px;
            border: 2px solid #e8f4f8;
            background: white;
            color: #666;

            &:hover {
              color: #00c292;
              border-color: #00c292;
            }
          }

          .el-radio-button__orig-radio:checked + .el-radio-button__inner {
            background: #00c292;
            border-color: #00c292;
            color: white;
          }
        }
      }
    }

    .venues-grid {
      .empty-state {
        text-align: center;
        padding: 80px 20px;
        color: #909399;

        i {
          font-size: 64px;
          margin-bottom: 20px;
          color: #ddd;
        }

        h3 {
          font-size: 20px;
          margin: 0 0 10px 0;
        }

        p {
          margin: 0;
          font-size: 14px;
        }
      }

      .venue-cards {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 25px;

        .venue-card {
          background: white;
          border-radius: 16px;
          overflow: hidden;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
          transition: all 0.3s ease;

          &:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
          }

          .venue-image {
            position: relative;
            height: 200px;
            overflow: hidden;

            img {
              width: 100%;
              height: 100%;
              object-fit: cover;
              transition: transform 0.3s ease;
            }

            &:hover img {
              transform: scale(1.05);
            }

            .venue-status {
              position: absolute;
              top: 15px;
              right: 15px;
              padding: 6px 12px;
              border-radius: 20px;
              font-size: 12px;
              font-weight: 600;

              &.available {
                background: rgba(0, 194, 146, 0.9);
                color: white;
              }

              &.unavailable {
                background: rgba(245, 108, 108, 0.9);
                color: white;
              }
            }
          }

          .venue-info {
            padding: 25px;

            h3 {
              font-size: 20px;
              font-weight: 600;
              color: #2c3e50;
              margin: 0 0 15px 0;
            }

            .venue-details {
              margin-bottom: 20px;

              .detail-item {
                display: flex;
                align-items: center;
                margin-bottom: 8px;
                color: #666;
                font-size: 14px;

                i {
                  color: #00c292;
                  margin-right: 8px;
                  width: 16px;
                }
              }
            }

            .venue-price {
              margin-bottom: 20px;

              .current-price {
                font-size: 24px;
                font-weight: 700;
                color: #00c292;
              }

              .original-price {
                font-size: 16px;
                color: #999;
                text-decoration: line-through;
                margin-left: 10px;
              }
            }

            .venue-actions {
              display: flex;
              gap: 10px;

              .el-button {
                border-radius: 8px;
                font-weight: 600;

                &.el-button--primary {
                  background: linear-gradient(45deg, #00c292, #00a085);
                  border: none;
                  flex: 1;

                  &:hover:not(:disabled) {
                    background: linear-gradient(45deg, #00a085, #008f75);
                  }

                  &:disabled {
                    background: #ddd;
                    color: #999;
                  }
                }

                &.el-button--text {
                  color: #00c292;

                  &:hover {
                    color: #00a085;
                  }
                }
              }
            }
          }
        }
      }
    }

    .venues-list {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);

      ::v-deep .el-table {
        .price-cell {
          .current-price {
            font-size: 16px;
            font-weight: 600;
            color: #00c292;
          }

          .original-price {
            font-size: 12px;
            color: #999;
            text-decoration: line-through;
            display: block;
          }
        }

        .el-button {
          border-radius: 6px;
          font-weight: 600;

          &.el-button--primary {
            background: linear-gradient(45deg, #00c292, #00a085);
            border: none;

            &:hover:not(:disabled) {
              background: linear-gradient(45deg, #00a085, #008f75);
            }
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: 30px;
      text-align: center;

      ::v-deep .el-pagination {
        .el-pager li {
          border-radius: 8px;
          margin: 0 4px;

          &.active {
            background: #00c292;
            border-color: #00c292;
          }
        }

        .btn-prev,
        .btn-next {
          border-radius: 8px;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .booking-container {
    .page-header .header-content .stats-cards {
      flex-direction: column;
      align-items: center;
      gap: 20px;
    }

    .venues-section .venue-cards {
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    }
  }
}

@media (max-width: 768px) {
  .booking-container {
    padding: 15px;

    .search-section .search-form {
      .el-row {
        flex-direction: column;

        .el-col {
          width: 100%;
          margin-bottom: 15px;
        }
      }
    }

    .venues-section {
      .section-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
      }

      .venue-cards {
        grid-template-columns: 1fr;
      }
    }
  }
}
</style>
