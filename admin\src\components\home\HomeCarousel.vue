<template>
  <div class="carousel-container">
    <el-carousel :interval="4000" type="card" height="300px" indicator-position="outside">
      <el-carousel-item v-for="(item, index) in carouselData" :key="index">
        <div class="carousel-item" :style="{ backgroundImage: `url(${item.image})` }">
          <div class="carousel-content">
            <h3>{{ item.title }}</h3>
            <p>{{ item.description }}</p>
          </div>
        </div>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
export default {
  name: 'HomeCarousel',
  data() {
    return {
      carouselData: [
        {
          title: '体育馆管理系统',
          description: '现代化的体育场馆管理解决方案',
          image: '/tiyuguan/img/img/back-img-bg.jpg'
        },
        {
          title: '场地预约',
          description: '便捷的在线场地预约服务',
          image: '/tiyuguan/img/img/back-img-bg.jpg'
        },
        {
          title: '智能管理',
          description: '高效的场馆运营管理平台',
          image: '/tiyuguan/img/img/back-img-bg.jpg'
        }
      ]
    }
  },
  mounted() {
    this.loadCarouselData()
  },
  methods: {
    loadCarouselData() {
      // 从后端获取轮播图数据
      this.$http({
        url: 'config/list',
        method: 'get',
        params: {
          page: 1,
          limit: 5
        }
      }).then(({ data }) => {
        if (data && data.code === 0 && data.list && data.list.length > 0) {
          this.carouselData = data.list.map((item, index) => ({
            title: item.name || `轮播图 ${index + 1}`,
            description: item.value ? '精彩内容等您发现' : '欢迎使用体育馆管理系统',
            image: item.value || '/tiyuguan/img/img/back-img-bg.jpg'
          }))
        }
      }).catch(err => {
        console.log('获取轮播图数据失败:', err)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.carousel-container {
  margin-bottom: 20px;
  
  .carousel-item {
    width: 100%;
    height: 300px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
      z-index: 1;
    }
    
    .carousel-content {
      position: absolute;
      bottom: 30px;
      left: 30px;
      color: white;
      z-index: 2;
      
      h3 {
        font-size: 24px;
        font-weight: bold;
        margin: 0 0 10px 0;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }
      
      p {
        font-size: 16px;
        margin: 0;
        opacity: 0.9;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
      }
    }
  }
}

::v-deep .el-carousel__indicator {
  .el-carousel__button {
    background-color: rgba(255, 255, 255, 0.5);
    
    &.is-active {
      background-color: #00c292;
    }
  }
}
</style>
