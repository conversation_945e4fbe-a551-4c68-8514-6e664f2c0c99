{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\components\\BookingDialog.vue?vue&type=style&index=0&id=8413ec7c&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\components\\BookingDialog.vue", "mtime": 1750596787915}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["BookingDialog.vue"], "names": [], "mappings": ";AAwSA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "BookingDialog.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"场地预约\"\n    :visible.sync=\"dialogVisible\"\n    width=\"600px\"\n    :before-close=\"handleClose\"\n    class=\"booking-dialog\">\n    \n    <div class=\"booking-content\">\n      <!-- 场地信息 -->\n      <div class=\"venue-summary\">\n        <div class=\"venue-image\">\n          <img :src=\"venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" :alt=\"venue.changdiName\">\n        </div>\n        <div class=\"venue-info\">\n          <h3>{{ venue.changdiName }}</h3>\n          <div class=\"venue-details\">\n            <div class=\"detail-item\">\n              <i class=\"el-icon-location\"></i>\n              <span>{{ venue.changdiValue }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <i class=\"el-icon-star-on\"></i>\n              <span>{{ venue.banquanValue }}</span>\n            </div>\n          </div>\n          <div class=\"venue-price\">\n            <span class=\"current-price\">¥{{ venue.changdiNewMoney }}</span>\n            <span v-if=\"venue.changdiOldMoney !== venue.changdiNewMoney\" class=\"original-price\">\n              ¥{{ venue.changdiOldMoney }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 预约表单 -->\n      <el-form :model=\"bookingForm\" :rules=\"rules\" ref=\"bookingForm\" label-width=\"100px\">\n        <el-form-item label=\"预约日期\" prop=\"bookingDate\">\n          <el-date-picker\n            v-model=\"bookingForm.bookingDate\"\n            type=\"date\"\n            placeholder=\"选择预约日期\"\n            :picker-options=\"datePickerOptions\"\n            style=\"width: 100%\">\n          </el-date-picker>\n        </el-form-item>\n        \n        <el-form-item label=\"时间段\" prop=\"timeSlot\">\n          <el-select v-model=\"bookingForm.timeSlot\" placeholder=\"请选择时间段\" style=\"width: 100%\">\n            <el-option\n              v-for=\"slot in timeSlots\"\n              :key=\"slot.value\"\n              :label=\"slot.label\"\n              :value=\"slot.value\"\n              :disabled=\"slot.disabled\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"预约人数\" prop=\"peopleCount\">\n          <el-input-number\n            v-model=\"bookingForm.peopleCount\"\n            :min=\"1\"\n            :max=\"20\"\n            style=\"width: 100%\">\n          </el-input-number>\n        </el-form-item>\n        \n        <el-form-item label=\"联系电话\" prop=\"phone\">\n          <el-input v-model=\"bookingForm.phone\" placeholder=\"请输入联系电话\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"备注信息\">\n          <el-input\n            v-model=\"bookingForm.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入备注信息（选填）\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 价格信息 -->\n      <div class=\"price-summary\">\n        <div class=\"price-item\">\n          <span>场地费用：</span>\n          <span>¥{{ venue.changdiNewMoney }}</span>\n        </div>\n        <div class=\"price-item\">\n          <span>预约人数：</span>\n          <span>{{ bookingForm.peopleCount }}人</span>\n        </div>\n        <div class=\"price-item total\">\n          <span>总计费用：</span>\n          <span class=\"total-price\">¥{{ totalPrice }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取消</el-button>\n      <el-button type=\"primary\" @click=\"submitBooking\" :loading=\"submitting\">\n        确认预约\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nexport default {\n  name: 'BookingDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    venue: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  data() {\n    return {\n      dialogVisible: false,\n      submitting: false,\n      \n      bookingForm: {\n        bookingDate: '',\n        timeSlot: '',\n        peopleCount: 1,\n        phone: '',\n        remark: ''\n      },\n      \n      rules: {\n        bookingDate: [\n          { required: true, message: '请选择预约日期', trigger: 'change' }\n        ],\n        timeSlot: [\n          { required: true, message: '请选择时间段', trigger: 'change' }\n        ],\n        peopleCount: [\n          { required: true, message: '请输入预约人数', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入联系电话', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\n        ]\n      },\n      \n      timeSlots: [\n        { label: '08:00-10:00', value: '08:00-10:00', disabled: false },\n        { label: '10:00-12:00', value: '10:00-12:00', disabled: false },\n        { label: '12:00-14:00', value: '12:00-14:00', disabled: false },\n        { label: '14:00-16:00', value: '14:00-16:00', disabled: false },\n        { label: '16:00-18:00', value: '16:00-18:00', disabled: false },\n        { label: '18:00-20:00', value: '18:00-20:00', disabled: false },\n        { label: '20:00-22:00', value: '20:00-22:00', disabled: false }\n      ],\n      \n      datePickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期\n        }\n      }\n    }\n  },\n  \n  computed: {\n    totalPrice() {\n      return (this.venue.changdiNewMoney || 0) * (this.bookingForm.peopleCount || 1)\n    }\n  },\n  \n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.initForm()\n      }\n    },\n    \n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  \n  methods: {\n    // 初始化表单\n    initForm() {\n      // 获取用户信息\n      this.$http({\n        url: `${this.$storage.get(\"sessionTable\")}/session`,\n        method: \"get\"\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.bookingForm.phone = data.data.yonghuPhone || ''\n        }\n      })\n      \n      // 重置表单\n      this.bookingForm = {\n        bookingDate: '',\n        timeSlot: '',\n        peopleCount: 1,\n        phone: this.bookingForm.phone || '',\n        remark: ''\n      }\n    },\n    \n    // 提交预约\n    submitBooking() {\n      console.log('开始提交预约')\n\n      // 检查登录状态\n      const role = this.$storage.get('role')\n      const userId = this.$storage.get('userId')\n      const sessionTable = this.$storage.get('sessionTable')\n\n      console.log('用户信息:', { role, userId, sessionTable })\n\n      if (!role) {\n        this.$message.warning('请先登录账户')\n        this.$router.push('/login')\n        return\n      }\n\n      if (role !== '用户') {\n        this.$message.warning('只有用户可以预约场地，当前角色：' + role)\n        this.handleClose()\n        return\n      }\n\n      this.$refs.bookingForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n\n          const bookingData = {\n            changdiId: this.venue.id,\n            changdiOrderTruePrice: this.totalPrice,\n            changdiOrderTypes: 1, // 预约状态\n            shijianduan: this.bookingForm.timeSlot,\n            buyTime: this.formatDate(this.bookingForm.bookingDate),\n            insertTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\n            yonghuId: userId || 1 // 用户ID\n          }\n\n          console.log('预约数据:', bookingData)\n\n          this.$http({\n            url: 'changdiOrder/save',\n            method: 'post',\n            data: bookingData\n          }).then(({ data }) => {\n            console.log('预约响应:', data)\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('预约成功！')\n              this.$emit('booking-success')\n              this.handleClose()\n            } else {\n              this.$message.error(data.msg || '预约失败，请稍后重试')\n              console.error('预约失败:', data)\n            }\n          }).catch((error) => {\n            console.error('预约请求错误:', error)\n            this.submitting = false\n            this.$message.error('网络错误，请稍后重试')\n          })\n        } else {\n          console.log('表单验证失败')\n        }\n      })\n    },\n    \n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n      this.$refs.bookingForm.resetFields()\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return ''\n      const d = new Date(date)\n      const year = d.getFullYear()\n      const month = String(d.getMonth() + 1).padStart(2, '0')\n      const day = String(d.getDate()).padStart(2, '0')\n      return `${year}-${month}-${day}`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .booking-dialog {\n  .el-dialog__header {\n    background: linear-gradient(45deg, #00c292, #00a085);\n    color: white;\n    padding: 20px 30px;\n    \n    .el-dialog__title {\n      color: white;\n      font-size: 18px;\n      font-weight: 600;\n    }\n    \n    .el-dialog__close {\n      color: white;\n      font-size: 20px;\n    }\n  }\n  \n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n.booking-content {\n  .venue-summary {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 30px;\n    padding: 20px;\n    background: #f8fffe;\n    border-radius: 12px;\n    border: 1px solid rgba(0, 194, 146, 0.1);\n    \n    .venue-image {\n      flex: 0 0 120px;\n      \n      img {\n        width: 120px;\n        height: 90px;\n        object-fit: cover;\n        border-radius: 8px;\n      }\n    }\n    \n    .venue-info {\n      flex: 1;\n      \n      h3 {\n        font-size: 20px;\n        color: #2c3e50;\n        margin: 0 0 15px 0;\n      }\n      \n      .venue-details {\n        margin-bottom: 15px;\n        \n        .detail-item {\n          display: flex;\n          align-items: center;\n          margin-bottom: 5px;\n          color: #666;\n          font-size: 14px;\n          \n          i {\n            color: #00c292;\n            margin-right: 8px;\n            width: 16px;\n          }\n        }\n      }\n      \n      .venue-price {\n        .current-price {\n          font-size: 20px;\n          font-weight: 700;\n          color: #00c292;\n        }\n        \n        .original-price {\n          font-size: 14px;\n          color: #999;\n          text-decoration: line-through;\n          margin-left: 10px;\n        }\n      }\n    }\n  }\n  \n  .price-summary {\n    margin-top: 30px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    \n    .price-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 10px;\n      \n      &.total {\n        border-top: 1px solid #ddd;\n        padding-top: 10px;\n        margin-top: 10px;\n        font-weight: 600;\n        \n        .total-price {\n          font-size: 18px;\n          color: #00c292;\n        }\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  \n  .el-button {\n    border-radius: 8px;\n    font-weight: 600;\n    \n    &.el-button--primary {\n      background: linear-gradient(45deg, #00c292, #00a085);\n      border: none;\n      \n      &:hover {\n        background: linear-gradient(45deg, #00a085, #008f75);\n      }\n    }\n  }\n}\n</style>\n"]}]}