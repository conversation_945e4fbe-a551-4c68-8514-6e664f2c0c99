{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\register.vue?vue&type=style&index=0&id=77453986&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\register.vue", "mtime": 1750594699322}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["register.vue"], "names": [], "mappings": ";AAgTA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "register.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div class=\"register-container\">\r\n        <!-- 背景装饰 -->\r\n        <div class=\"background-decoration\">\r\n            <div class=\"decoration-circle circle-1\"></div>\r\n            <div class=\"decoration-circle circle-2\"></div>\r\n            <div class=\"decoration-circle circle-3\"></div>\r\n        </div>\r\n\r\n        <!-- 主要内容区域 -->\r\n        <div class=\"main-content\">\r\n            <!-- 左侧信息区域 -->\r\n            <div class=\"info-section\">\r\n                <div class=\"brand-info\">\r\n                    <div class=\"logo-container\">\r\n                        <i class=\"el-icon-user-solid\"></i>\r\n                        <h1>用户注册</h1>\r\n                    </div>\r\n                    <p class=\"brand-description\">\r\n                        加入我们的体育馆管理平台<br>\r\n                        享受便捷的场地预约服务\r\n                    </p>\r\n                    <div class=\"features\">\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-check\"></i>\r\n                            <span>快速注册，立即使用</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-star-on\"></i>\r\n                            <span>专业的场地管理</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-service\"></i>\r\n                            <span>贴心的客户服务</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 右侧注册表单 -->\r\n            <div class=\"register-section\">\r\n                <div class=\"register-card\">\r\n                    <div class=\"card-header\">\r\n                        <h2>创建新账户</h2>\r\n                        <p>请填写以下信息完成注册</p>\r\n                    </div>\r\n\r\n                    <el-form ref=\"rgsForm\" class=\"register-form\" :model=\"ruleForm\" @submit.native.prevent=\"register\">\r\n                        <div class=\"form-row\">\r\n                            <el-form-item class=\"form-item\">\r\n                                <div class=\"input-wrapper\">\r\n                                    <i class=\"el-icon-user input-icon\"></i>\r\n                                    <el-input\r\n                                        v-model=\"ruleForm.username\"\r\n                                        placeholder=\"请输入账号\"\r\n                                        size=\"large\"\r\n                                        clearable>\r\n                                    </el-input>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </div>\r\n\r\n                        <div class=\"form-row\">\r\n                            <el-form-item class=\"form-item half-width\">\r\n                                <div class=\"input-wrapper\">\r\n                                    <i class=\"el-icon-lock input-icon\"></i>\r\n                                    <el-input\r\n                                        v-model=\"ruleForm.password\"\r\n                                        type=\"password\"\r\n                                        placeholder=\"请输入密码\"\r\n                                        size=\"large\"\r\n                                        show-password\r\n                                        clearable>\r\n                                    </el-input>\r\n                                </div>\r\n                            </el-form-item>\r\n\r\n                            <el-form-item class=\"form-item half-width\">\r\n                                <div class=\"input-wrapper\">\r\n                                    <i class=\"el-icon-key input-icon\"></i>\r\n                                    <el-input\r\n                                        v-model=\"ruleForm.repetitionPassword\"\r\n                                        type=\"password\"\r\n                                        placeholder=\"确认密码\"\r\n                                        size=\"large\"\r\n                                        show-password\r\n                                        clearable>\r\n                                    </el-input>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </div>\r\n\r\n                        <div v-if=\"tableName=='yonghu'\" class=\"user-info-section\">\r\n                            <div class=\"section-title\">\r\n                                <i class=\"el-icon-info\"></i>\r\n                                个人信息\r\n                            </div>\r\n\r\n                            <div class=\"form-row\">\r\n                                <el-form-item class=\"form-item half-width\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-user-solid input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuName\"\r\n                                            placeholder=\"请输入真实姓名\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n\r\n                                <el-form-item class=\"form-item half-width\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-phone input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuPhone\"\r\n                                            placeholder=\"请输入手机号\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n                            </div>\r\n\r\n                            <div class=\"form-row\">\r\n                                <el-form-item class=\"form-item\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-postcard input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuIdNumber\"\r\n                                            placeholder=\"请输入身份证号\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n                            </div>\r\n\r\n                            <div class=\"form-row\">\r\n                                <el-form-item class=\"form-item\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-message input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuEmail\"\r\n                                            placeholder=\"请输入电子邮箱\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"form-actions\">\r\n                            <el-button\r\n                                type=\"primary\"\r\n                                @click=\"register()\"\r\n                                class=\"register-button\"\r\n                                size=\"large\"\r\n                                :loading=\"loading\">\r\n                                <i class=\"el-icon-check\"></i>\r\n                                立即注册\r\n                            </el-button>\r\n\r\n                            <el-button\r\n                                @click=\"close()\"\r\n                                class=\"cancel-button\"\r\n                                size=\"large\">\r\n                                <i class=\"el-icon-close\"></i>\r\n                                取消\r\n                            </el-button>\r\n                        </div>\r\n\r\n                        <div class=\"form-footer\">\r\n                            <div class=\"login-link\" @click=\"close()\">\r\n                                <i class=\"el-icon-back\"></i>\r\n                                已有账户？立即登录\r\n                            </div>\r\n                        </div>\r\n                    </el-form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                ruleForm: {\r\n                    username: '',\r\n                    password: '',\r\n                    repetitionPassword: '',\r\n                    yonghuName: '',\r\n                    yonghuPhone: '',\r\n                    yonghuIdNumber: '',\r\n                    yonghuEmail: ''\r\n                },\r\n                tableName: \"\",\r\n                rules: {},\r\n                sexTypesOptions: [],\r\n            };\r\n        },\r\n        mounted(){\r\n            let table = this.$storage.get(\"loginTable\");\r\n            this.tableName = table || 'yonghu';\r\n        },\r\n        methods: {\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n\r\n            // 返回登录页面\r\n            close(){\r\n                this.$router.push({ path: \"/login\" });\r\n            },\r\n\r\n            // 验证表单\r\n            validateForm() {\r\n                // 基础验证\r\n                if (!this.ruleForm.username) {\r\n                    this.$message.error('账号不能为空');\r\n                    return false;\r\n                }\r\n                if (this.ruleForm.username.length < 3) {\r\n                    this.$message.error('账号长度至少3位');\r\n                    return false;\r\n                }\r\n                if (!this.ruleForm.password) {\r\n                    this.$message.error('密码不能为空');\r\n                    return false;\r\n                }\r\n                if (this.ruleForm.password.length < 6) {\r\n                    this.$message.error('密码长度至少6位');\r\n                    return false;\r\n                }\r\n                if (!this.ruleForm.repetitionPassword) {\r\n                    this.$message.error('请确认密码');\r\n                    return false;\r\n                }\r\n                if (this.ruleForm.repetitionPassword !== this.ruleForm.password) {\r\n                    this.$message.error('两次输入的密码不一致');\r\n                    return false;\r\n                }\r\n\r\n                // 用户信息验证\r\n                if (this.tableName === 'yonghu') {\r\n                    if (!this.ruleForm.yonghuName) {\r\n                        this.$message.error('用户姓名不能为空');\r\n                        return false;\r\n                    }\r\n                    if (this.ruleForm.yonghuPhone && !this.$validate.isMobile(this.ruleForm.yonghuPhone)) {\r\n                        this.$message.error('请输入正确的手机号格式');\r\n                        return false;\r\n                    }\r\n                    if (!this.ruleForm.yonghuIdNumber) {\r\n                        this.$message.error('身份证号不能为空');\r\n                        return false;\r\n                    }\r\n                    if (this.ruleForm.yonghuEmail && !this.$validate.isEmail(this.ruleForm.yonghuEmail)) {\r\n                        this.$message.error('请输入正确的邮箱格式');\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                return true;\r\n            },\r\n\r\n            // 注册\r\n            register() {\r\n                if (!this.validateForm()) {\r\n                    return;\r\n                }\r\n\r\n                this.loading = true;\r\n                this.$http({\r\n                    url: `${this.tableName}/register`,\r\n                    method: \"post\",\r\n                    data: this.ruleForm\r\n                }).then(({ data }) => {\r\n                    this.loading = false;\r\n                    if (data && data.code === 0) {\r\n                        this.$message({\r\n                            message: \"注册成功！请登录后在个人中心完善个人信息\",\r\n                            type: \"success\",\r\n                            duration: 2000,\r\n                            onClose: () => {\r\n                                this.$router.replace({ path: \"/login\" });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        this.$message.error(data.msg || '注册失败，请稍后重试');\r\n                    }\r\n                }).catch(() => {\r\n                    this.loading = false;\r\n                    this.$message.error('网络错误，请稍后重试');\r\n                });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  // 背景装饰\r\n  .background-decoration {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    pointer-events: none;\r\n\r\n    .decoration-circle {\r\n      position: absolute;\r\n      border-radius: 50%;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      animation: float 6s ease-in-out infinite;\r\n\r\n      &.circle-1 {\r\n        width: 200px;\r\n        height: 200px;\r\n        top: 10%;\r\n        left: 10%;\r\n        animation-delay: 0s;\r\n      }\r\n\r\n      &.circle-2 {\r\n        width: 150px;\r\n        height: 150px;\r\n        top: 60%;\r\n        right: 15%;\r\n        animation-delay: 2s;\r\n      }\r\n\r\n      &.circle-3 {\r\n        width: 100px;\r\n        height: 100px;\r\n        bottom: 20%;\r\n        left: 20%;\r\n        animation-delay: 4s;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域\r\n  .main-content {\r\n    display: flex;\r\n    width: 100%;\r\n    max-width: 1400px;\r\n    margin: 0 auto;\r\n    padding: 40px;\r\n    gap: 60px;\r\n    align-items: center;\r\n\r\n    // 左侧信息区域\r\n    .info-section {\r\n      flex: 1;\r\n      color: white;\r\n\r\n      .brand-info {\r\n        .logo-container {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 30px;\r\n\r\n          i {\r\n            font-size: 48px;\r\n            color: #00c292;\r\n            margin-right: 20px;\r\n          }\r\n\r\n          h1 {\r\n            font-size: 36px;\r\n            font-weight: 700;\r\n            margin: 0;\r\n            background: linear-gradient(45deg, #00c292, #00e5ff);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            background-clip: text;\r\n          }\r\n        }\r\n\r\n        .brand-description {\r\n          font-size: 18px;\r\n          line-height: 1.6;\r\n          margin-bottom: 40px;\r\n          opacity: 0.9;\r\n        }\r\n\r\n        .features {\r\n          .feature-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n\r\n            i {\r\n              font-size: 20px;\r\n              color: #00c292;\r\n              margin-right: 15px;\r\n              width: 24px;\r\n            }\r\n\r\n            span {\r\n              font-size: 16px;\r\n              opacity: 0.9;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 右侧注册表单\r\n    .register-section {\r\n      flex: 0 0 550px;\r\n\r\n      .register-card {\r\n        background: rgba(255, 255, 255, 0.95);\r\n        backdrop-filter: blur(20px);\r\n        border-radius: 20px;\r\n        padding: 40px;\r\n        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid rgba(255, 255, 255, 0.2);\r\n        max-height: 90vh;\r\n        overflow-y: auto;\r\n\r\n        .card-header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n\r\n          h2 {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #2c3e50 !important;\r\n            margin: 0 0 10px 0;\r\n          }\r\n\r\n          p {\r\n            color: #666 !important;\r\n            font-size: 16px;\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        .register-form {\r\n          .form-row {\r\n            display: flex;\r\n            gap: 15px;\r\n            margin-bottom: 20px;\r\n\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n          }\r\n\r\n          .form-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n\r\n            &.half-width {\r\n              flex: 0 0 calc(50% - 7.5px);\r\n            }\r\n\r\n            .input-wrapper {\r\n              position: relative;\r\n\r\n              .input-icon {\r\n                position: absolute;\r\n                left: 15px;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                color: #00c292;\r\n                font-size: 18px;\r\n                z-index: 2;\r\n              }\r\n\r\n              ::v-deep .el-input__inner {\r\n                height: 50px;\r\n                padding-left: 50px;\r\n                border: 2px solid #e8f4f8;\r\n                border-radius: 12px;\r\n                font-size: 16px;\r\n                transition: all 0.3s ease;\r\n                background: #f8fffe !important;\r\n                color: #333 !important;\r\n\r\n                &:focus {\r\n                  border-color: #00c292;\r\n                  box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);\r\n                  background: white !important;\r\n                  color: #333 !important;\r\n                }\r\n\r\n                &::placeholder {\r\n                  color: #999 !important;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .user-info-section {\r\n            margin: 30px 0;\r\n            padding: 25px;\r\n            background: rgba(0, 194, 146, 0.05);\r\n            border-radius: 15px;\r\n            border: 1px solid rgba(0, 194, 146, 0.1);\r\n\r\n            .section-title {\r\n              display: flex;\r\n              align-items: center;\r\n              margin-bottom: 20px;\r\n              font-size: 18px;\r\n              font-weight: 600;\r\n              color: #2c3e50 !important;\r\n\r\n              i {\r\n                margin-right: 10px;\r\n                color: #00c292;\r\n                font-size: 20px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .form-actions {\r\n            display: flex;\r\n            gap: 15px;\r\n            margin-top: 30px;\r\n\r\n            .register-button {\r\n              flex: 1;\r\n              height: 50px;\r\n              font-size: 18px;\r\n              font-weight: 600;\r\n              border-radius: 12px;\r\n              background: linear-gradient(45deg, #00c292, #00a085);\r\n              border: none;\r\n              transition: all 0.3s ease;\r\n\r\n              &:hover {\r\n                transform: translateY(-2px);\r\n                box-shadow: 0 8px 25px rgba(0, 194, 146, 0.3);\r\n              }\r\n\r\n              &:active {\r\n                transform: translateY(0);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n              }\r\n            }\r\n\r\n            .cancel-button {\r\n              flex: 0 0 120px;\r\n              height: 50px;\r\n              font-size: 16px;\r\n              font-weight: 600;\r\n              border-radius: 12px;\r\n              background: white;\r\n              border: 2px solid #e8f4f8;\r\n              color: #666;\r\n              transition: all 0.3s ease;\r\n\r\n              &:hover {\r\n                border-color: #00c292;\r\n                color: #00c292;\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .form-footer {\r\n            text-align: center;\r\n            margin-top: 25px;\r\n\r\n            .login-link {\r\n              color: #00c292;\r\n              cursor: pointer;\r\n              font-size: 16px;\r\n              transition: all 0.3s ease;\r\n              display: inline-flex;\r\n              align-items: center;\r\n\r\n              &:hover {\r\n                color: #00a085;\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n                font-size: 18px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .register-container .main-content {\r\n    .register-section {\r\n      flex: 0 0 500px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 1024px) {\r\n  .register-container .main-content {\r\n    flex-direction: column;\r\n    gap: 40px;\r\n    padding: 20px;\r\n\r\n    .info-section {\r\n      text-align: center;\r\n\r\n      .brand-info .logo-container {\r\n        justify-content: center;\r\n\r\n        h1 {\r\n          font-size: 28px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .register-section {\r\n      flex: none;\r\n      width: 100%;\r\n      max-width: 550px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .register-container .main-content {\r\n    padding: 15px;\r\n\r\n    .register-section .register-card {\r\n      padding: 30px 20px;\r\n\r\n      .card-header h2 {\r\n        font-size: 24px;\r\n      }\r\n\r\n      .register-form {\r\n        .form-row {\r\n          flex-direction: column;\r\n          gap: 0;\r\n\r\n          .form-item {\r\n            margin-bottom: 20px;\r\n\r\n            &.half-width {\r\n              flex: 1;\r\n            }\r\n          }\r\n        }\r\n\r\n        .form-actions {\r\n          flex-direction: column;\r\n\r\n          .cancel-button {\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .info-section .brand-info .logo-container h1 {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n// 滚动条样式\r\n.register-card::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.register-card::-webkit-scrollbar-track {\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 3px;\r\n}\r\n\r\n.register-card::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 194, 146, 0.3);\r\n  border-radius: 3px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 194, 146, 0.5);\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}