{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue?vue&type=template&id=90178fb0&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue", "mtime": 1750603368047}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}