{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue?vue&type=template&id=90178fb0&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue", "mtime": 1750602879994}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}