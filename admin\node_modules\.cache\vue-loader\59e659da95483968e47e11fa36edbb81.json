{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdi\\add-or-update.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdi\\add-or-update.vue", "mtime": 1750590566807}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AAwOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/changdi", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"changdi-form-container\">\r\n        <div class=\"form-header\">\r\n            <h3 v-if=\"type === 'info'\">场地详情</h3>\r\n            <h3 v-else-if=\"!ruleForm.id\">新增场地</h3>\r\n            <h3 v-else>编辑场地</h3>\r\n        </div>\r\n\r\n        <el-card class=\"form-card\">\r\n            <el-form\r\n                class=\"changdi-form\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\">\r\n\r\n                <el-row :gutter=\"20\">\r\n                    <input id=\"updateId\" name=\"id\" type=\"hidden\">\r\n\r\n                    <!-- 基本信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">基本信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiUuidNumber\"\r\n                                placeholder=\"请输入场地编号\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiUuidNumber || type === 'info'\"\r\n                                prefix-icon=\"el-icon-postcard\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地名称\" prop=\"changdiName\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiName\"\r\n                                placeholder=\"请输入场地名称\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiName || type === 'info'\"\r\n                                prefix-icon=\"el-icon-office-building\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地类型\" prop=\"changdiTypes\">\r\n                            <el-select\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.changdiTypes\"\r\n                                placeholder=\"请选择场地类型\"\r\n                                style=\"width: 100%\">\r\n                                <el-option\r\n                                    v-for=\"(item,index) in changdiTypesOptions\"\r\n                                    v-bind:key=\"item.codeIndex\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                </el-option>\r\n                            </el-select>\r\n                            <el-input\r\n                                v-else\r\n                                v-model=\"ruleForm.changdiValue\"\r\n                                placeholder=\"场地类型\"\r\n                                readonly\r\n                                prefix-icon=\"el-icon-menu\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"半全场\" prop=\"banquanTypes\">\r\n                            <el-select\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.banquanTypes\"\r\n                                placeholder=\"请选择半全场\"\r\n                                style=\"width: 100%\">\r\n                                <el-option\r\n                                    v-for=\"(item,index) in banquanTypesOptions\"\r\n                                    v-bind:key=\"item.codeIndex\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                </el-option>\r\n                            </el-select>\r\n                            <el-input\r\n                                v-else\r\n                                v-model=\"ruleForm.banquanValue\"\r\n                                placeholder=\"半全场\"\r\n                                readonly\r\n                                prefix-icon=\"el-icon-s-grid\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <!-- 场地照片 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">场地图片</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"24\">\r\n                        <el-form-item label=\"场地照片\" prop=\"changdiPhoto\">\r\n                            <file-upload\r\n                                v-if=\"type !== 'info' && !ro.changdiPhoto\"\r\n                                tip=\"点击上传场地照片，支持多张图片\"\r\n                                action=\"file/upload\"\r\n                                :limit=\"5\"\r\n                                :multiple=\"true\"\r\n                                :fileUrls=\"ruleForm.changdiPhoto?ruleForm.changdiPhoto:''\"\r\n                                @change=\"changdiPhotoUploadChange\"\r\n                            ></file-upload>\r\n                            <div v-else-if=\"ruleForm.changdiPhoto\" class=\"photo-preview\">\r\n                                <img\r\n                                    v-for=\"(item,index) in (ruleForm.changdiPhoto || '').split(',')\"\r\n                                    :key=\"index\"\r\n                                    :src=\"item\"\r\n                                    class=\"preview-image\"\r\n                                    @click=\"previewImage(item)\">\r\n                            </div>\r\n                            <div v-else class=\"no-image\">暂无图片</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <!-- 价格信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">价格信息</div>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地原价\" prop=\"changdiOldMoney\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiOldMoney\"\r\n                                placeholder=\"请输入场地原价\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiOldMoney || type === 'info'\"\r\n                                prefix-icon=\"el-icon-price-tag\">\r\n                                <template slot=\"append\">元/小时</template>\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地现价\" prop=\"changdiNewMoney\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiNewMoney\"\r\n                                placeholder=\"请输入场地现价\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiNewMoney || type === 'info'\"\r\n                                prefix-icon=\"el-icon-sell\">\r\n                                <template slot=\"append\">元/小时</template>\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <!-- 时间信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">时间信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"可用时间段\" prop=\"shijianduan\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.shijianduan\"\r\n                                placeholder=\"例如：8-10,10-12,14-16,16-18\"\r\n                                clearable\r\n                                :readonly=\"ro.shijianduan || type === 'info'\"\r\n                                prefix-icon=\"el-icon-time\">\r\n                            </el-input>\r\n                            <div class=\"form-tip\">请用逗号分隔多个时间段，格式：开始时间-结束时间</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"推荐餐厅\" prop=\"tuijian\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.tuijian\"\r\n                                placeholder=\"请输入推荐的餐厅地点\"\r\n                                clearable\r\n                                :readonly=\"ro.tuijian || type === 'info'\"\r\n                                prefix-icon=\"el-icon-food\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                    <!-- 详细信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">详细信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"24\">\r\n                        <el-form-item label=\"场地简介\" prop=\"changdiContent\">\r\n                            <editor\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.changdiContent\"\r\n                                class=\"editor\"\r\n                                action=\"file/upload\"\r\n                                placeholder=\"请输入场地的详细介绍...\">\r\n                            </editor>\r\n                            <div v-else-if=\"ruleForm.changdiContent\" class=\"content-preview\">\r\n                                <div v-html=\"ruleForm.changdiContent\"></div>\r\n                            </div>\r\n                            <div v-else class=\"no-content\">暂无简介</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                </el-row>\r\n\r\n                <!-- 操作按钮 -->\r\n                <div class=\"form-actions\">\r\n                    <el-button\r\n                        v-if=\"type !== 'info'\"\r\n                        type=\"primary\"\r\n                        @click=\"onSubmit\"\r\n                        :loading=\"loading\">\r\n                        <i class=\"el-icon-check\"></i>\r\n                        {{ !ruleForm.id ? '新增场地' : '保存修改' }}\r\n                    </el-button>\r\n                    <el-button @click=\"back()\">\r\n                        <i class=\"el-icon-back\"></i>\r\n                        {{ type === 'info' ? '返回' : '取消' }}\r\n                    </el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-card>\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                sessionTable : \"\",//登录账户所在表名\r\n                role : \"\",//权限\r\n                loading: false,\r\n                ro:{\r\n                    changdiUuidNumber: false,\r\n                    changdiName: false,\r\n                    changdiPhoto: false,\r\n                    changdiTypes: false,\r\n                    changdiOldMoney: false,\r\n                    changdiNewMoney: false,\r\n                    shijianduan: false,\r\n                    shijianduanRen: false,\r\n                    changdiClicknum: false,\r\n                    banquanTypes: false,\r\n                    shangxiaTypes: false,\r\n                    tuijian: false,\r\n                    changdiDelete: false,\r\n                    changdiContent: false,\r\n                },\r\n                ruleForm: {\r\n                    changdiUuidNumber: new Date().getTime(),\r\n                    changdiName: '',\r\n                    changdiPhoto: '',\r\n                    changdiTypes: '',\r\n                    changdiOldMoney: '',\r\n                    changdiNewMoney: '',\r\n                    shijianduan: '8-10,10-12,14-16,16-18',\r\n                    shijianduanRen: '',\r\n                    changdiClicknum: '',\r\n                    banquanTypes: '',\r\n                    shangxiaTypes: '',\r\n                    tuijian: '',\r\n                    changdiDelete: '',\r\n                    changdiContent: '',\r\n                },\r\n                changdiTypesOptions : [],\r\n                banquanTypesOptions : [],\r\n                shangxiaTypesOptions : [],\r\n                rules: {\r\n                   changdiUuidNumber: [\r\n                              { required: true, message: '场地编号不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiName: [\r\n                              { required: true, message: '场地名称不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiPhoto: [\r\n                              { required: true, message: '场地照片不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiTypes: [\r\n                              { required: true, message: '场地类型不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiOldMoney: [\r\n                              { required: true, message: '场地原价不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[0-9]{0,6}(\\.[0-9]{1,2})?$/,\r\n                                  message: '只允许输入整数6位,小数2位的数字',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiNewMoney: [\r\n                              { required: true, message: '场地现价不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[0-9]{0,6}(\\.[0-9]{1,2})?$/,\r\n                                  message: '只允许输入整数6位,小数2位的数字',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   shijianduan: [\r\n                              { required: true, message: '时间段不能为空', trigger: 'blur' },\r\n                          ],\r\n                   shijianduanRen: [\r\n                              { required: true, message: '人数不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiClicknum: [\r\n                              { required: true, message: '点击次数不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   banquanTypes: [\r\n                              { required: true, message: '半全场不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   shangxiaTypes: [\r\n                              { required: true, message: '是否上架不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   tuijian: [\r\n                              { required: true, message: '推荐吃饭地点不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiDelete: [\r\n                              { required: true, message: '逻辑删除不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiContent: [\r\n                              { required: true, message: '场地简介不能为空', trigger: 'blur' },\r\n                          ],\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n            if (this.role != \"管理员\"){\r\n                this.ro.changdiOldMoney = true;\r\n                this.ro.changdiNewMoney = true;\r\n            }\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n            //获取下拉框信息\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=changdi_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.changdiTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=banquan_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.banquanTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangxia_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.shangxiaTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n\r\n\r\n        },\r\n        mounted() {\r\n        },\r\n        methods: {\r\n            // 下载\r\n            download(file){\r\n                window.open(`${file}`)\r\n            },\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else if(this.type=='cross'){\r\n                    var obj = this.$storage.getObj('crossObj');\r\n                    for (var o in obj){\r\n\r\n                      if(o=='changdiUuidNumber'){\r\n                          this.ruleForm.changdiUuidNumber = obj[o];\r\n                          this.ro.changdiUuidNumber = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiName'){\r\n                          this.ruleForm.changdiName = obj[o];\r\n                          this.ro.changdiName = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiPhoto'){\r\n                          this.ruleForm.changdiPhoto = obj[o];\r\n                          this.ro.changdiPhoto = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiTypes'){\r\n                          this.ruleForm.changdiTypes = obj[o];\r\n                          this.ro.changdiTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiOldMoney'){\r\n                          this.ruleForm.changdiOldMoney = obj[o];\r\n                          this.ro.changdiOldMoney = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiNewMoney'){\r\n                          this.ruleForm.changdiNewMoney = obj[o];\r\n                          this.ro.changdiNewMoney = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shijianduan'){\r\n                          this.ruleForm.shijianduan = obj[o];\r\n                          this.ro.shijianduan = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shijianduanRen'){\r\n                          this.ruleForm.shijianduanRen = obj[o];\r\n                          this.ro.shijianduanRen = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiClicknum'){\r\n                          this.ruleForm.changdiClicknum = obj[o];\r\n                          this.ro.changdiClicknum = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='banquanTypes'){\r\n                          this.ruleForm.banquanTypes = obj[o];\r\n                          this.ro.banquanTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shangxiaTypes'){\r\n                          this.ruleForm.shangxiaTypes = obj[o];\r\n                          this.ro.shangxiaTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='tuijian'){\r\n                          this.ruleForm.tuijian = obj[o];\r\n                          this.ro.tuijian = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiDelete'){\r\n                          this.ruleForm.changdiDelete = obj[o];\r\n                          this.ro.changdiDelete = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiContent'){\r\n                          this.ruleForm.changdiContent = obj[o];\r\n                          this.ro.changdiContent = true;\r\n                          continue;\r\n                      }\r\n                    }\r\n                }\r\n                // 获取用户信息\r\n                this.$http({\r\n                    url:`${this.$storage.get(\"sessionTable\")}/session`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        var json = data.data;\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `changdi/info/${id}`,\r\n                    method: 'get'\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.ruleForm = data.data;\r\n                        //解决前台上传图片后台不显示的问题\r\n                        let reg=new RegExp('../../../upload','g')//g代表全部\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 图片预览\r\n            previewImage(url) {\r\n                this.$alert(`<img src=\"${url}\" style=\"width: 100%; max-width: 500px;\">`, '图片预览', {\r\n                    dangerouslyUseHTMLString: true,\r\n                    showConfirmButton: false,\r\n                    showCancelButton: true,\r\n                    cancelButtonText: '关闭'\r\n                });\r\n            },\r\n\r\n            // 提交\r\n            onSubmit() {\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        this.loading = true;\r\n                        this.$http({\r\n                            url:`changdi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: this.ruleForm\r\n                        }).then(({ data }) => {\r\n                            this.loading = false;\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.changdiCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        }).catch(() => {\r\n                            this.loading = false;\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.changdiCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            //图片\r\n            changdiPhotoUploadChange(fileUrls){\r\n                this.ruleForm.changdiPhoto = fileUrls;\r\n                this.addEditUploadStyleChange()\r\n            },\r\n\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputFontColor\r\n                        el.style.fontSize = this.addEditForm.inputFontSize\r\n                        el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                        el.style.borderColor = this.addEditForm.inputBorderColor\r\n                        el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.inputBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputLableColor\r\n                        el.style.fontSize = this.addEditForm.inputLableFontSize\r\n                    })\r\n                    // select\r\n                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectFontColor\r\n                        el.style.fontSize = this.addEditForm.selectFontSize\r\n                        el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                        el.style.borderColor = this.addEditForm.selectBorderColor\r\n                        el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.selectBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectLableColor\r\n                        el.style.fontSize = this.addEditForm.selectLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                        el.style.color = this.addEditForm.selectIconFontColor\r\n                        el.style.fontSize = this.addEditForm.selectIconFontSize\r\n                    })\r\n                    // date\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateFontColor\r\n                        el.style.fontSize = this.addEditForm.dateFontSize\r\n                        el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                        el.style.borderColor = this.addEditForm.dateBorderColor\r\n                        el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.dateBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateLableColor\r\n                        el.style.fontSize = this.addEditForm.dateLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                        el.style.color = this.addEditForm.dateIconFontColor\r\n                        el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                    })\r\n                    // upload\r\n                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.uploadHeight\r\n                        el.style.color = this.addEditForm.uploadLableColor\r\n                        el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                        el.style.color = this.addEditForm.uploadIconFontColor\r\n                        el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                        el.style.lineHeight = iconLineHeight\r\n                        el.style.display = 'block'\r\n                    })\r\n                    // 多文本输入框\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaFontColor\r\n                        el.style.fontSize = this.addEditForm.textareaFontSize\r\n                        el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                        el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                        el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                        // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaLableColor\r\n                        el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n                    })\r\n                    // 保存\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnSaveWidth\r\n                        el.style.height = this.addEditForm.btnSaveHeight\r\n                        el.style.color = this.addEditForm.btnSaveFontColor\r\n                        el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n                    })\r\n                    // 返回\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnCancelWidth\r\n                        el.style.height = this.addEditForm.btnCancelHeight\r\n                        el.style.color = this.addEditForm.btnCancelFontColor\r\n                        el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n                    })\r\n                })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.changdi-form-container {\r\n  padding: 20px;\r\n\r\n  .form-header {\r\n    text-align: center;\r\n    margin-bottom: 30px;\r\n\r\n    h3 {\r\n      font-size: 24px;\r\n      color: #2c3e50;\r\n      margin: 0;\r\n    }\r\n  }\r\n\r\n  .form-card {\r\n    ::v-deep .el-card__body {\r\n      padding: 30px;\r\n    }\r\n\r\n    .changdi-form {\r\n      .section-title {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #2c3e50;\r\n        margin-bottom: 20px;\r\n        padding-bottom: 10px;\r\n        border-bottom: 2px solid #00c292;\r\n        position: relative;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          bottom: -2px;\r\n          left: 0;\r\n          width: 50px;\r\n          height: 2px;\r\n          background: #00c292;\r\n        }\r\n      }\r\n\r\n      .el-form-item {\r\n        margin-bottom: 25px;\r\n\r\n        ::v-deep .el-form-item__label {\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n        }\r\n\r\n        ::v-deep .el-input__inner {\r\n          border-radius: 6px;\r\n          border: 1px solid #dcdfe6;\r\n          transition: all 0.3s ease;\r\n\r\n          &:focus {\r\n            border-color: #00c292;\r\n            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n          }\r\n        }\r\n\r\n        ::v-deep .el-select {\r\n          width: 100%;\r\n        }\r\n\r\n        .form-tip {\r\n          font-size: 12px;\r\n          color: #909399;\r\n          margin-top: 5px;\r\n        }\r\n      }\r\n\r\n      .photo-preview {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .preview-image {\r\n          width: 100px;\r\n          height: 100px;\r\n          object-fit: cover;\r\n          border-radius: 8px;\r\n          cursor: pointer;\r\n          border: 2px solid #dcdfe6;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            border-color: #00c292;\r\n            transform: scale(1.05);\r\n          }\r\n        }\r\n      }\r\n\r\n      .no-image, .no-content {\r\n        color: #909399;\r\n        font-style: italic;\r\n        text-align: center;\r\n        padding: 20px;\r\n        background: #f8f9fa;\r\n        border-radius: 6px;\r\n        border: 1px dashed #dcdfe6;\r\n      }\r\n\r\n      .content-preview {\r\n        padding: 15px;\r\n        background: #f8f9fa;\r\n        border-radius: 6px;\r\n        border: 1px solid #dcdfe6;\r\n        min-height: 100px;\r\n\r\n        ::v-deep img {\r\n          max-width: 100%;\r\n          height: auto;\r\n        }\r\n      }\r\n    }\r\n\r\n    .form-actions {\r\n      text-align: center;\r\n      margin-top: 30px;\r\n      padding-top: 20px;\r\n      border-top: 1px solid #ebeef5;\r\n\r\n      .el-button {\r\n        border-radius: 6px;\r\n        padding: 12px 30px;\r\n        font-weight: 600;\r\n        margin: 0 10px;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.editor {\r\n  height: 400px;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n\r\n  ::v-deep .ql-container {\r\n    height: 310px;\r\n  }\r\n\r\n  ::v-deep .ql-toolbar {\r\n    border-bottom: 1px solid #dcdfe6;\r\n  }\r\n}\r\n\r\n::v-deep .el-upload {\r\n  .el-upload-dragger {\r\n    border-radius: 8px;\r\n    border: 2px dashed #d9d9d9;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      border-color: #00c292;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}