<template>
  <div class="about-container">
    <div class="about-header">
      <h1>关于体育馆管理系统</h1>
      <p>现代化的体育场馆管理解决方案</p>
    </div>
    
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="info-card">
          <div slot="header">
            <span>系统介绍</span>
          </div>
          <div class="card-content">
            <p>体育馆管理系统是一个现代化的场馆管理平台，致力于为体育场馆提供全方位的管理服务。</p>
            <ul>
              <li>场地预约管理</li>
              <li>用户信息管理</li>
              <li>收费标准管理</li>
              <li>数据统计分析</li>
              <li>公告信息发布</li>
            </ul>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="info-card">
          <div slot="header">
            <span>技术特色</span>
          </div>
          <div class="card-content">
            <p>采用先进的技术架构，确保系统的稳定性和可扩展性。</p>
            <ul>
              <li>前端：Vue.js + Element UI</li>
              <li>后端：Spring Boot + MyBatis</li>
              <li>数据库：MySQL</li>
              <li>响应式设计，支持多设备访问</li>
              <li>模块化架构，易于维护和扩展</li>
            </ul>
          </div>
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="8">
        <el-card class="feature-card">
          <div class="feature-icon">
            <i class="el-icon-date"></i>
          </div>
          <h3>智能预约</h3>
          <p>支持在线预约，实时查看场地状态，智能冲突检测</p>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="feature-card">
          <div class="feature-icon">
            <i class="el-icon-data-analysis"></i>
          </div>
          <h3>数据分析</h3>
          <p>提供详细的数据统计和分析报告，助力科学决策</p>
        </el-card>
      </el-col>
      
      <el-col :span="8">
        <el-card class="feature-card">
          <div class="feature-icon">
            <i class="el-icon-user"></i>
          </div>
          <h3>用户管理</h3>
          <p>完善的用户权限管理，支持多角色多权限配置</p>
        </el-card>
      </el-col>
    </el-row>
    
    <el-card class="contact-card" style="margin-top: 20px;">
      <div slot="header">
        <span>联系我们</span>
      </div>
      <div class="contact-content">
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="contact-item">
              <i class="el-icon-phone"></i>
              <span>电话：************</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="contact-item">
              <i class="el-icon-message"></i>
              <span>邮箱：<EMAIL></span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="contact-item">
              <i class="el-icon-location"></i>
              <span>地址：北京市朝阳区体育馆路1号</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'About',
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.about-container {
  padding: 20px;
  
  .about-header {
    text-align: center;
    margin-bottom: 30px;
    
    h1 {
      font-size: 32px;
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    p {
      font-size: 16px;
      color: #909399;
    }
  }
  
  .info-card, .feature-card, .contact-card {
    margin-bottom: 20px;
    
    .card-content {
      p {
        margin-bottom: 15px;
        line-height: 1.6;
        color: #606266;
      }
      
      ul {
        margin: 0;
        padding-left: 20px;
        
        li {
          margin-bottom: 8px;
          color: #606266;
        }
      }
    }
  }
  
  .feature-card {
    text-align: center;
    
    .feature-icon {
      font-size: 48px;
      color: #00c292;
      margin-bottom: 15px;
    }
    
    h3 {
      font-size: 18px;
      color: #2c3e50;
      margin-bottom: 10px;
    }
    
    p {
      color: #909399;
      line-height: 1.6;
    }
  }
  
  .contact-content {
    .contact-item {
      display: flex;
      align-items: center;
      
      i {
        font-size: 18px;
        color: #00c292;
        margin-right: 10px;
      }
      
      span {
        color: #606266;
      }
    }
  }
}
</style>
