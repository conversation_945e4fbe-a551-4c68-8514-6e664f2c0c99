{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue?vue&type=template&id=71e3f342&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue", "mtime": 1750599440132}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}