<template>
    <div class="register-container">
        <!-- 背景装饰 -->
        <div class="background-decoration">
            <div class="decoration-circle circle-1"></div>
            <div class="decoration-circle circle-2"></div>
            <div class="decoration-circle circle-3"></div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 左侧信息区域 -->
            <div class="info-section">
                <div class="brand-info">
                    <div class="logo-container">
                        <i class="el-icon-user-solid"></i>
                        <h1>用户注册</h1>
                    </div>
                    <p class="brand-description">
                        加入我们的体育馆管理平台<br>
                        享受便捷的场地预约服务
                    </p>
                    <div class="features">
                        <div class="feature-item">
                            <i class="el-icon-check"></i>
                            <span>快速注册，立即使用</span>
                        </div>
                        <div class="feature-item">
                            <i class="el-icon-star-on"></i>
                            <span>专业的场地管理</span>
                        </div>
                        <div class="feature-item">
                            <i class="el-icon-service"></i>
                            <span>贴心的客户服务</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧注册表单 -->
            <div class="register-section">
                <div class="register-card">
                    <div class="card-header">
                        <h2>创建新账户</h2>
                        <p>请填写以下信息完成注册</p>
                    </div>

                    <el-form ref="rgsForm" class="register-form" :model="ruleForm" @submit.native.prevent="register">
                        <div class="form-row">
                            <el-form-item class="form-item">
                                <div class="input-wrapper">
                                    <i class="el-icon-user input-icon"></i>
                                    <el-input
                                        v-model="ruleForm.username"
                                        placeholder="请输入账号"
                                        size="large"
                                        clearable>
                                    </el-input>
                                </div>
                            </el-form-item>
                        </div>

                        <div class="form-row">
                            <el-form-item class="form-item half-width">
                                <div class="input-wrapper">
                                    <i class="el-icon-lock input-icon"></i>
                                    <el-input
                                        v-model="ruleForm.password"
                                        type="password"
                                        placeholder="请输入密码"
                                        size="large"
                                        show-password
                                        clearable>
                                    </el-input>
                                </div>
                            </el-form-item>

                            <el-form-item class="form-item half-width">
                                <div class="input-wrapper">
                                    <i class="el-icon-key input-icon"></i>
                                    <el-input
                                        v-model="ruleForm.repetitionPassword"
                                        type="password"
                                        placeholder="确认密码"
                                        size="large"
                                        show-password
                                        clearable>
                                    </el-input>
                                </div>
                            </el-form-item>
                        </div>

                        <div v-if="tableName=='yonghu'" class="user-info-section">
                            <div class="section-title">
                                <i class="el-icon-info"></i>
                                个人信息
                            </div>

                            <div class="form-row">
                                <el-form-item class="form-item half-width">
                                    <div class="input-wrapper">
                                        <i class="el-icon-user-solid input-icon"></i>
                                        <el-input
                                            v-model="ruleForm.yonghuName"
                                            placeholder="请输入真实姓名"
                                            size="large"
                                            clearable>
                                        </el-input>
                                    </div>
                                </el-form-item>

                                <el-form-item class="form-item half-width">
                                    <div class="input-wrapper">
                                        <i class="el-icon-phone input-icon"></i>
                                        <el-input
                                            v-model="ruleForm.yonghuPhone"
                                            placeholder="请输入手机号"
                                            size="large"
                                            clearable>
                                        </el-input>
                                    </div>
                                </el-form-item>
                            </div>

                            <div class="form-row">
                                <el-form-item class="form-item">
                                    <div class="input-wrapper">
                                        <i class="el-icon-postcard input-icon"></i>
                                        <el-input
                                            v-model="ruleForm.yonghuIdNumber"
                                            placeholder="请输入身份证号"
                                            size="large"
                                            clearable>
                                        </el-input>
                                    </div>
                                </el-form-item>
                            </div>

                            <div class="form-row">
                                <el-form-item class="form-item">
                                    <div class="input-wrapper">
                                        <i class="el-icon-message input-icon"></i>
                                        <el-input
                                            v-model="ruleForm.yonghuEmail"
                                            placeholder="请输入电子邮箱"
                                            size="large"
                                            clearable>
                                        </el-input>
                                    </div>
                                </el-form-item>
                            </div>
                        </div>

                        <div class="form-actions">
                            <el-button
                                type="primary"
                                @click="register()"
                                class="register-button"
                                size="large"
                                :loading="loading">
                                <i class="el-icon-check"></i>
                                立即注册
                            </el-button>

                            <el-button
                                @click="close()"
                                class="cancel-button"
                                size="large">
                                <i class="el-icon-close"></i>
                                取消
                            </el-button>
                        </div>

                        <div class="form-footer">
                            <div class="login-link" @click="close()">
                                <i class="el-icon-back"></i>
                                已有账户？立即登录
                            </div>
                        </div>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
    export default {
        data() {
            return {
                loading: false,
                ruleForm: {
                    username: '',
                    password: '',
                    repetitionPassword: '',
                    yonghuName: '',
                    yonghuPhone: '',
                    yonghuIdNumber: '',
                    yonghuEmail: ''
                },
                tableName: "",
                rules: {},
                sexTypesOptions: [],
            };
        },
        mounted(){
            let table = this.$storage.get("loginTable");
            this.tableName = table || 'yonghu';
        },
        methods: {
            // 获取uuid
            getUUID () {
                return new Date().getTime();
            },

            // 返回登录页面
            close(){
                this.$router.push({ path: "/login" });
            },

            // 验证表单
            validateForm() {
                // 基础验证
                if (!this.ruleForm.username) {
                    this.$message.error('账号不能为空');
                    return false;
                }
                if (this.ruleForm.username.length < 3) {
                    this.$message.error('账号长度至少3位');
                    return false;
                }
                if (!this.ruleForm.password) {
                    this.$message.error('密码不能为空');
                    return false;
                }
                if (this.ruleForm.password.length < 6) {
                    this.$message.error('密码长度至少6位');
                    return false;
                }
                if (!this.ruleForm.repetitionPassword) {
                    this.$message.error('请确认密码');
                    return false;
                }
                if (this.ruleForm.repetitionPassword !== this.ruleForm.password) {
                    this.$message.error('两次输入的密码不一致');
                    return false;
                }

                // 用户信息验证
                if (this.tableName === 'yonghu') {
                    if (!this.ruleForm.yonghuName) {
                        this.$message.error('用户姓名不能为空');
                        return false;
                    }
                    if (this.ruleForm.yonghuPhone && !this.$validate.isMobile(this.ruleForm.yonghuPhone)) {
                        this.$message.error('请输入正确的手机号格式');
                        return false;
                    }
                    if (!this.ruleForm.yonghuIdNumber) {
                        this.$message.error('身份证号不能为空');
                        return false;
                    }
                    if (this.ruleForm.yonghuEmail && !this.$validate.isEmail(this.ruleForm.yonghuEmail)) {
                        this.$message.error('请输入正确的邮箱格式');
                        return false;
                    }
                }

                return true;
            },

            // 注册
            register() {
                if (!this.validateForm()) {
                    return;
                }

                this.loading = true;
                this.$http({
                    url: `${this.tableName}/register`,
                    method: "post",
                    data: this.ruleForm
                }).then(({ data }) => {
                    this.loading = false;
                    if (data && data.code === 0) {
                        this.$message({
                            message: "注册成功！请登录后在个人中心完善个人信息",
                            type: "success",
                            duration: 2000,
                            onClose: () => {
                                this.$router.replace({ path: "/login" });
                            }
                        });
                    } else {
                        this.$message.error(data.msg || '注册失败，请稍后重试');
                    }
                }).catch(() => {
                    this.loading = false;
                    this.$message.error('网络错误，请稍后重试');
                });
            }
        }
    };
</script>
<style lang="scss" scoped>
.register-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;

  // 背景装饰
  .background-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;

    .decoration-circle {
      position: absolute;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.1);
      animation: float 6s ease-in-out infinite;

      &.circle-1 {
        width: 200px;
        height: 200px;
        top: 10%;
        left: 10%;
        animation-delay: 0s;
      }

      &.circle-2 {
        width: 150px;
        height: 150px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.circle-3 {
        width: 100px;
        height: 100px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
      }
    }
  }

  // 主要内容区域
  .main-content {
    display: flex;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
    padding: 40px;
    gap: 60px;
    align-items: center;

    // 左侧信息区域
    .info-section {
      flex: 1;
      color: white;

      .brand-info {
        .logo-container {
          display: flex;
          align-items: center;
          margin-bottom: 30px;

          i {
            font-size: 48px;
            color: #00c292;
            margin-right: 20px;
          }

          h1 {
            font-size: 36px;
            font-weight: 700;
            margin: 0;
            background: linear-gradient(45deg, #00c292, #00e5ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
          }
        }

        .brand-description {
          font-size: 18px;
          line-height: 1.6;
          margin-bottom: 40px;
          opacity: 0.9;
        }

        .features {
          .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;

            i {
              font-size: 20px;
              color: #00c292;
              margin-right: 15px;
              width: 24px;
            }

            span {
              font-size: 16px;
              opacity: 0.9;
            }
          }
        }
      }
    }

    // 右侧注册表单
    .register-section {
      flex: 0 0 550px;

      .register-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 40px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-height: 90vh;
        overflow-y: auto;

        .card-header {
          text-align: center;
          margin-bottom: 30px;

          h2 {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50 !important;
            margin: 0 0 10px 0;
          }

          p {
            color: #666 !important;
            font-size: 16px;
            margin: 0;
          }
        }

        .register-form {
          .form-row {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          .form-item {
            flex: 1;
            margin-bottom: 0;

            &.half-width {
              flex: 0 0 calc(50% - 7.5px);
            }

            .input-wrapper {
              position: relative;

              .input-icon {
                position: absolute;
                left: 15px;
                top: 50%;
                transform: translateY(-50%);
                color: #00c292;
                font-size: 18px;
                z-index: 2;
              }

              ::v-deep .el-input__inner {
                height: 50px;
                padding-left: 50px;
                border: 2px solid #e8f4f8;
                border-radius: 12px;
                font-size: 16px;
                transition: all 0.3s ease;
                background: #f8fffe !important;
                color: #333 !important;

                &:focus {
                  border-color: #00c292;
                  box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);
                  background: white !important;
                  color: #333 !important;
                }

                &::placeholder {
                  color: #999 !important;
                }
              }
            }
          }

          .user-info-section {
            margin: 30px 0;
            padding: 25px;
            background: rgba(0, 194, 146, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(0, 194, 146, 0.1);

            .section-title {
              display: flex;
              align-items: center;
              margin-bottom: 20px;
              font-size: 18px;
              font-weight: 600;
              color: #2c3e50 !important;

              i {
                margin-right: 10px;
                color: #00c292;
                font-size: 20px;
              }
            }
          }

          .form-actions {
            display: flex;
            gap: 15px;
            margin-top: 30px;

            .register-button {
              flex: 1;
              height: 50px;
              font-size: 18px;
              font-weight: 600;
              border-radius: 12px;
              background: linear-gradient(45deg, #00c292, #00a085);
              border: none;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0, 194, 146, 0.3);
              }

              &:active {
                transform: translateY(0);
              }

              i {
                margin-right: 8px;
              }
            }

            .cancel-button {
              flex: 0 0 120px;
              height: 50px;
              font-size: 16px;
              font-weight: 600;
              border-radius: 12px;
              background: white;
              border: 2px solid #e8f4f8;
              color: #666;
              transition: all 0.3s ease;

              &:hover {
                border-color: #00c292;
                color: #00c292;
                transform: translateY(-1px);
              }

              i {
                margin-right: 8px;
              }
            }
          }

          .form-footer {
            text-align: center;
            margin-top: 25px;

            .login-link {
              color: #00c292;
              cursor: pointer;
              font-size: 16px;
              transition: all 0.3s ease;
              display: inline-flex;
              align-items: center;

              &:hover {
                color: #00a085;
                transform: translateY(-1px);
              }

              i {
                margin-right: 8px;
                font-size: 18px;
              }
            }
          }
        }
      }
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .register-container .main-content {
    .register-section {
      flex: 0 0 500px;
    }
  }
}

@media (max-width: 1024px) {
  .register-container .main-content {
    flex-direction: column;
    gap: 40px;
    padding: 20px;

    .info-section {
      text-align: center;

      .brand-info .logo-container {
        justify-content: center;

        h1 {
          font-size: 28px;
        }
      }
    }

    .register-section {
      flex: none;
      width: 100%;
      max-width: 550px;
    }
  }
}

@media (max-width: 768px) {
  .register-container .main-content {
    padding: 15px;

    .register-section .register-card {
      padding: 30px 20px;

      .card-header h2 {
        font-size: 24px;
      }

      .register-form {
        .form-row {
          flex-direction: column;
          gap: 0;

          .form-item {
            margin-bottom: 20px;

            &.half-width {
              flex: 1;
            }
          }
        }

        .form-actions {
          flex-direction: column;

          .cancel-button {
            flex: 1;
          }
        }
      }
    }

    .info-section .brand-info .logo-container h1 {
      font-size: 24px;
    }
  }
}

// 滚动条样式
.register-card::-webkit-scrollbar {
  width: 6px;
}

.register-card::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.register-card::-webkit-scrollbar-thumb {
  background: rgba(0, 194, 146, 0.3);
  border-radius: 3px;

  &:hover {
    background: rgba(0, 194, 146, 0.5);
  }
}
</style>

