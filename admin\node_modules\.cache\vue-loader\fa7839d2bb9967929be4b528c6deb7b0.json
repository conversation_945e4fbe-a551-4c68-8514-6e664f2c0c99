{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\login.vue", "mtime": 1750594624583}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AAmIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div class=\"login-container\">\r\n        <!-- 背景装饰 -->\r\n        <div class=\"background-decoration\">\r\n            <div class=\"decoration-circle circle-1\"></div>\r\n            <div class=\"decoration-circle circle-2\"></div>\r\n            <div class=\"decoration-circle circle-3\"></div>\r\n        </div>\r\n\r\n        <!-- 主要内容区域 -->\r\n        <div class=\"main-content\">\r\n            <!-- 左侧信息区域 -->\r\n            <div class=\"info-section\">\r\n                <div class=\"brand-info\">\r\n                    <div class=\"logo-container\">\r\n                        <i class=\"el-icon-trophy-1\"></i>\r\n                        <h1>体育馆管理系统</h1>\r\n                    </div>\r\n                    <p class=\"brand-description\">\r\n                        现代化的体育场馆预约管理平台<br>\r\n                        为您提供便捷、高效的场地预约服务\r\n                    </p>\r\n                    <div class=\"features\">\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-time\"></i>\r\n                            <span>24小时在线预约</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-location\"></i>\r\n                            <span>多场地智能管理</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>用户友好界面</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 右侧登录表单 -->\r\n            <div class=\"login-section\">\r\n                <div class=\"login-card\">\r\n                    <div class=\"card-header\">\r\n                        <h2>欢迎登录</h2>\r\n                        <p>请输入您的账户信息</p>\r\n                    </div>\r\n\r\n                    <el-form class=\"login-form\" :model=\"rulesForm\" @submit.native.prevent=\"login\">\r\n                        <el-form-item class=\"form-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-user input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.username\"\r\n                                    placeholder=\"请输入用户名\"\r\n                                    size=\"large\"\r\n                                    clearable>\r\n                                </el-input>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item class=\"form-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-lock input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.password\"\r\n                                    type=\"password\"\r\n                                    placeholder=\"请输入密码\"\r\n                                    size=\"large\"\r\n                                    show-password\r\n                                    clearable>\r\n                                </el-input>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item v-if=\"showCaptcha\" class=\"form-item captcha-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-key input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.code\"\r\n                                    placeholder=\"请输入验证码\"\r\n                                    size=\"large\"\r\n                                    clearable>\r\n                                </el-input>\r\n                                <div class=\"captcha-code\" @click=\"getRandCode(4)\">\r\n                                    <span v-for=\"(item, index) in codes\" :key=\"index\"\r\n                                          :style=\"{color:item.color,transform:item.rotate,fontSize:item.size}\">\r\n                                        {{ item.num }}\r\n                                    </span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item class=\"form-item role-item\">\r\n                            <label class=\"role-label\">选择角色</label>\r\n                            <div class=\"role-options\">\r\n                                <el-radio-group v-model=\"rulesForm.role\" class=\"role-group\">\r\n                                    <el-radio\r\n                                        v-for=\"item in menus\"\r\n                                        v-if=\"item.hasBackLogin=='是'\"\r\n                                        :key=\"item.roleName\"\r\n                                        :label=\"item.roleName\"\r\n                                        class=\"role-radio\">\r\n                                        {{ item.roleName }}\r\n                                    </el-radio>\r\n                                </el-radio-group>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            @click=\"login()\"\r\n                            class=\"login-button\"\r\n                            size=\"large\"\r\n                            :loading=\"loading\">\r\n                            <i class=\"el-icon-right\"></i>\r\n                            立即登录\r\n                        </el-button>\r\n\r\n                        <div class=\"form-footer\">\r\n                            <div class=\"register-link\" @click=\"register('yonghu')\">\r\n                                <i class=\"el-icon-user-solid\"></i>\r\n                                还没有账户？立即注册\r\n                            </div>\r\n                        </div>\r\n                    </el-form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import menu from \"@/utils/menu\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                showCaptcha: false, // 是否显示验证码\r\n                rulesForm: {\r\n                    username: \"\",\r\n                    password: \"\",\r\n                    role: \"\",\r\n                    code: '',\r\n                },\r\n                menus: [],\r\n                tableName: \"\",\r\n                codes: [{\r\n                    num: 1,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 2,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 3,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 4,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                }],\r\n            };\r\n        },\r\n        mounted() {\r\n            let menus = menu.list();\r\n            this.menus = menus;\r\n        },\r\n        created() {\r\n            this.setInputColor()\r\n            this.getRandCode()\r\n        },\r\n        methods: {\r\n            setInputColor(){\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{\r\n                        el.style.backgroundColor = \"rgba(255, 255, 255, 1)\"\r\n                        el.style.color = \"rgba(51, 51, 51, 1)\"\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                        el.style.borderRadius = \"30px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{\r\n                        el.style.color = \"rgba(59, 160, 215, 1)\"\r\n                    })\r\n                    setTimeout(()=>{\r\n                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{\r\n                            el.style.color = \"#fff\"\r\n                        })\r\n                    },350)\r\n                })\r\n\r\n            },\r\n            register(tableName){\r\n                this.$storage.set(\"loginTable\", tableName);\r\n                this.$router.push({path:'/register'})\r\n            },\r\n            // 登陆\r\n            login() {\r\n                // 验证码检查\r\n                if (this.showCaptcha) {\r\n                    let code = ''\r\n                    for(let i in this.codes) {\r\n                        code += this.codes[i].num\r\n                    }\r\n                    if (!this.rulesForm.code) {\r\n                        this.$message.error(\"请输入验证码\");\r\n                        return;\r\n                    }\r\n                    if (this.rulesForm.code.toLowerCase() != code.toLowerCase()) {\r\n                        this.$message.error(\"验证码输入有误\");\r\n                        this.getRandCode()\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // 表单验证\r\n                if (!this.rulesForm.username) {\r\n                    this.$message.error(\"请输入用户名\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.password) {\r\n                    this.$message.error(\"请输入密码\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.role) {\r\n                    this.$message.error(\"请选择角色\");\r\n                    return;\r\n                }\r\n\r\n                // 获取表名\r\n                let menus = this.menus;\r\n                for (let i = 0; i < menus.length; i++) {\r\n                    if (menus[i].roleName == this.rulesForm.role) {\r\n                        this.tableName = menus[i].tableName;\r\n                    }\r\n                }\r\n\r\n                // 开始登录\r\n                this.loading = true;\r\n                this.$http({\r\n                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n                    method: \"post\"\r\n                }).then(({ data }) => {\r\n                    this.loading = false;\r\n                    if (data && data.code === 0) {\r\n                        this.$storage.set(\"Token\", data.token);\r\n                        this.$storage.set(\"role\", this.rulesForm.role);\r\n                        this.$storage.set(\"sessionTable\", this.tableName);\r\n                        this.$storage.set(\"adminName\", this.rulesForm.username);\r\n\r\n                        this.$message({\r\n                            message: \"登录成功！\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                this.$router.replace({ path: \"/index/\" });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        this.$message.error(data.msg || \"登录失败，请检查用户名和密码\");\r\n                    }\r\n                }).catch(() => {\r\n                    this.loading = false;\r\n                    this.$message.error(\"网络错误，请稍后重试\");\r\n                });\r\n            },\r\n            getRandCode(len = 4){\r\n                this.randomString(len)\r\n            },\r\n            randomString(len = 4) {\r\n                let chars = [\r\n                    \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\",\r\n                    \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\",\r\n                    \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\",\r\n                    \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\",\r\n                    \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"0\", \"1\", \"2\",\r\n                    \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"\r\n                ]\r\n                let colors = [\"0\", \"1\", \"2\",\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\"]\r\n                let sizes = ['14', '15', '16', '17', '18']\r\n\r\n                let output = [];\r\n                for (let i = 0; i < len; i++) {\r\n                    // 随机验证码\r\n                    let key = Math.floor(Math.random()*chars.length)\r\n                    this.codes[i].num = chars[key]\r\n                    // 随机验证码颜色\r\n                    let code = '#'\r\n                    for (let j = 0; j < 6; j++) {\r\n                        let key = Math.floor(Math.random()*colors.length)\r\n                        code += colors[key]\r\n                    }\r\n                    this.codes[i].color = code\r\n                    // 随机验证码方向\r\n                    let rotate = Math.floor(Math.random()*60)\r\n                    let plus = Math.floor(Math.random()*2)\r\n                    if(plus == 1) rotate = '-'+rotate\r\n                    this.codes[i].rotate = 'rotate('+rotate+'deg)'\r\n                    // 随机验证码字体大小\r\n                    let size = Math.floor(Math.random()*sizes.length)\r\n                    this.codes[i].size = sizes[size]+'px'\r\n                }\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  // 背景装饰\r\n  .background-decoration {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    pointer-events: none;\r\n\r\n    .decoration-circle {\r\n      position: absolute;\r\n      border-radius: 50%;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      animation: float 6s ease-in-out infinite;\r\n\r\n      &.circle-1 {\r\n        width: 200px;\r\n        height: 200px;\r\n        top: 10%;\r\n        left: 10%;\r\n        animation-delay: 0s;\r\n      }\r\n\r\n      &.circle-2 {\r\n        width: 150px;\r\n        height: 150px;\r\n        top: 60%;\r\n        right: 15%;\r\n        animation-delay: 2s;\r\n      }\r\n\r\n      &.circle-3 {\r\n        width: 100px;\r\n        height: 100px;\r\n        bottom: 20%;\r\n        left: 20%;\r\n        animation-delay: 4s;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域\r\n  .main-content {\r\n    display: flex;\r\n    width: 100%;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 40px;\r\n    gap: 60px;\r\n    align-items: center;\r\n\r\n    // 左侧信息区域\r\n    .info-section {\r\n      flex: 1;\r\n      color: white;\r\n\r\n      .brand-info {\r\n        .logo-container {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 30px;\r\n\r\n          i {\r\n            font-size: 48px;\r\n            color: #00c292;\r\n            margin-right: 20px;\r\n          }\r\n\r\n          h1 {\r\n            font-size: 36px;\r\n            font-weight: 700;\r\n            margin: 0;\r\n            background: linear-gradient(45deg, #00c292, #00e5ff);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            background-clip: text;\r\n          }\r\n        }\r\n\r\n        .brand-description {\r\n          font-size: 18px;\r\n          line-height: 1.6;\r\n          margin-bottom: 40px;\r\n          opacity: 0.9;\r\n        }\r\n\r\n        .features {\r\n          .feature-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n\r\n            i {\r\n              font-size: 20px;\r\n              color: #00c292;\r\n              margin-right: 15px;\r\n              width: 24px;\r\n            }\r\n\r\n            span {\r\n              font-size: 16px;\r\n              opacity: 0.9;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 右侧登录表单\r\n    .login-section {\r\n      flex: 0 0 450px;\r\n\r\n      .login-card {\r\n        background: rgba(255, 255, 255, 0.95);\r\n        backdrop-filter: blur(20px);\r\n        border-radius: 20px;\r\n        padding: 40px;\r\n        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid rgba(255, 255, 255, 0.2);\r\n\r\n        .card-header {\r\n          text-align: center;\r\n          margin-bottom: 40px;\r\n\r\n          h2 {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #2c3e50;\r\n            margin: 0 0 10px 0;\r\n          }\r\n\r\n          p {\r\n            color: #666;\r\n            font-size: 16px;\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        .login-form {\r\n          .form-item {\r\n            margin-bottom: 25px;\r\n\r\n            .input-wrapper {\r\n              position: relative;\r\n\r\n              .input-icon {\r\n                position: absolute;\r\n                left: 15px;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                color: #00c292;\r\n                font-size: 18px;\r\n                z-index: 2;\r\n              }\r\n\r\n              ::v-deep .el-input__inner {\r\n                height: 50px;\r\n                padding-left: 50px;\r\n                border: 2px solid #e8f4f8;\r\n                border-radius: 12px;\r\n                font-size: 16px;\r\n                transition: all 0.3s ease;\r\n                background: #f8fffe !important;\r\n                color: #333 !important;\r\n\r\n                &:focus {\r\n                  border-color: #00c292;\r\n                  box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);\r\n                  background: white !important;\r\n                  color: #333 !important;\r\n                }\r\n\r\n                &::placeholder {\r\n                  color: #999 !important;\r\n                }\r\n              }\r\n            }\r\n\r\n            // 验证码特殊样式\r\n            &.captcha-item {\r\n              .input-wrapper {\r\n                display: flex;\r\n                gap: 10px;\r\n\r\n                .el-input {\r\n                  flex: 1;\r\n                }\r\n\r\n                .captcha-code {\r\n                  width: 120px;\r\n                  height: 50px;\r\n                  background: linear-gradient(45deg, #00c292, #00a085);\r\n                  border-radius: 12px;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  cursor: pointer;\r\n                  transition: all 0.3s ease;\r\n\r\n                  &:hover {\r\n                    transform: scale(1.05);\r\n                  }\r\n\r\n                  span {\r\n                    font-weight: 600;\r\n                    color: white;\r\n                    margin: 0 2px;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            // 角色选择样式\r\n            &.role-item {\r\n              .role-label {\r\n                display: block;\r\n                margin-bottom: 15px;\r\n                font-weight: 600;\r\n                color: #2c3e50 !important;\r\n                font-size: 16px;\r\n              }\r\n\r\n              .role-options {\r\n                .role-group {\r\n                  display: flex;\r\n                  flex-wrap: wrap;\r\n                  gap: 15px;\r\n\r\n                  .role-radio {\r\n                    margin: 0;\r\n\r\n                    ::v-deep .el-radio__input.is-checked .el-radio__inner {\r\n                      background-color: #00c292;\r\n                      border-color: #00c292;\r\n                    }\r\n\r\n                    ::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n                      color: #00c292 !important;\r\n                      font-weight: 600;\r\n                    }\r\n\r\n                    ::v-deep .el-radio__label {\r\n                      font-size: 16px;\r\n                      color: #666 !important;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .login-button {\r\n            width: 100%;\r\n            height: 50px;\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            border-radius: 12px;\r\n            background: linear-gradient(45deg, #00c292, #00a085);\r\n            border: none;\r\n            margin-top: 20px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover {\r\n              transform: translateY(-2px);\r\n              box-shadow: 0 8px 25px rgba(0, 194, 146, 0.3);\r\n            }\r\n\r\n            &:active {\r\n              transform: translateY(0);\r\n            }\r\n\r\n            i {\r\n              margin-right: 8px;\r\n            }\r\n          }\r\n\r\n          .form-footer {\r\n            text-align: center;\r\n            margin-top: 30px;\r\n\r\n            .register-link {\r\n              color: #00c292;\r\n              cursor: pointer;\r\n              font-size: 16px;\r\n              transition: all 0.3s ease;\r\n              display: inline-flex;\r\n              align-items: center;\r\n\r\n              &:hover {\r\n                color: #00a085;\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n                font-size: 18px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1024px) {\r\n  .login-container .main-content {\r\n    flex-direction: column;\r\n    gap: 40px;\r\n    padding: 20px;\r\n\r\n    .info-section {\r\n      text-align: center;\r\n\r\n      .brand-info .logo-container {\r\n        justify-content: center;\r\n\r\n        h1 {\r\n          font-size: 28px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .login-section {\r\n      flex: none;\r\n      width: 100%;\r\n      max-width: 450px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .login-container .main-content {\r\n    padding: 15px;\r\n\r\n    .login-section .login-card {\r\n      padding: 30px 20px;\r\n\r\n      .card-header h2 {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n\r\n    .info-section .brand-info .logo-container h1 {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}