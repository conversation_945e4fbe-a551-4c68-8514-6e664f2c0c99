{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\login.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\login.vue", "mtime": 1750589328266}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQogICAgaW1wb3J0IG1lbnUgZnJvbSAiQC91dGlscy9tZW51IjsNCiAgICBleHBvcnQgZGVmYXVsdCB7DQogICAgICAgIGRhdGEoKSB7DQogICAgICAgICAgICByZXR1cm4gew0KICAgICAgICAgICAgICAgIHJ1bGVzRm9ybTogew0KICAgICAgICAgICAgICAgICAgICB1c2VybmFtZTogIiIsDQogICAgICAgICAgICAgICAgICAgIHBhc3N3b3JkOiAiIiwNCiAgICAgICAgICAgICAgICAgICAgcm9sZTogIiIsDQogICAgICAgICAgICAgICAgICAgIGNvZGU6ICcnLA0KICAgICAgICAgICAgICAgIH0sDQogICAgICAgICAgICAgICAgbWVudXM6IFtdLA0KICAgICAgICAgICAgICAgIHRhYmxlTmFtZTogIiIsDQogICAgICAgICAgICAgICAgY29kZXM6IFt7DQogICAgICAgICAgICAgICAgICAgIG51bTogMSwNCiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMDAwJywNCiAgICAgICAgICAgICAgICAgICAgcm90YXRlOiAnMTBkZWcnLA0KICAgICAgICAgICAgICAgICAgICBzaXplOiAnMTZweCcNCiAgICAgICAgICAgICAgICB9LHsNCiAgICAgICAgICAgICAgICAgICAgbnVtOiAyLA0KICAgICAgICAgICAgICAgICAgICBjb2xvcjogJyMwMDAnLA0KICAgICAgICAgICAgICAgICAgICByb3RhdGU6ICcxMGRlZycsDQogICAgICAgICAgICAgICAgICAgIHNpemU6ICcxNnB4Jw0KICAgICAgICAgICAgICAgIH0sew0KICAgICAgICAgICAgICAgICAgICBudW06IDMsDQogICAgICAgICAgICAgICAgICAgIGNvbG9yOiAnIzAwMCcsDQogICAgICAgICAgICAgICAgICAgIHJvdGF0ZTogJzEwZGVnJywNCiAgICAgICAgICAgICAgICAgICAgc2l6ZTogJzE2cHgnDQogICAgICAgICAgICAgICAgfSx7DQogICAgICAgICAgICAgICAgICAgIG51bTogNCwNCiAgICAgICAgICAgICAgICAgICAgY29sb3I6ICcjMDAwJywNCiAgICAgICAgICAgICAgICAgICAgcm90YXRlOiAnMTBkZWcnLA0KICAgICAgICAgICAgICAgICAgICBzaXplOiAnMTZweCcNCiAgICAgICAgICAgICAgICB9XSwNCiAgICAgICAgICAgIH07DQogICAgICAgIH0sDQogICAgICAgIG1vdW50ZWQoKSB7DQogICAgICAgICAgICBsZXQgbWVudXMgPSBtZW51Lmxpc3QoKTsNCiAgICAgICAgICAgIHRoaXMubWVudXMgPSBtZW51czsNCiAgICAgICAgfSwNCiAgICAgICAgY3JlYXRlZCgpIHsNCiAgICAgICAgICAgIHRoaXMuc2V0SW5wdXRDb2xvcigpDQogICAgICAgICAgICB0aGlzLmdldFJhbmRDb2RlKCkNCiAgICAgICAgfSwNCiAgICAgICAgbWV0aG9kczogew0KICAgICAgICAgICAgc2V0SW5wdXRDb2xvcigpew0KICAgICAgICAgICAgICAgIHRoaXMuJG5leHRUaWNrKCgpPT57DQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5sb2dpbkluIC5lbC1pbnB1dF9faW5uZXInKS5mb3JFYWNoKGVsPT57DQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5iYWNrZ3JvdW5kQ29sb3IgPSAicmdiYSgyNTUsIDI1NSwgMjU1LCAxKSINCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmNvbG9yID0gInJnYmEoNTEsIDUxLCA1MSwgMSkiDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5oZWlnaHQgPSAiNDRweCINCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmxpbmVIZWlnaHQgPSAiNDRweCINCiAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmJvcmRlclJhZGl1cyA9ICIzMHB4Ig0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcubG9naW5JbiAuc3R5bGUzIC5lbC1mb3JtLWl0ZW1fX2xhYmVsJykuZm9yRWFjaChlbD0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuaGVpZ2h0ID0gIjQ0cHgiDQogICAgICAgICAgICAgICAgICAgICAgICBlbC5zdHlsZS5saW5lSGVpZ2h0ID0gIjQ0cHgiDQogICAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgICAgIGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3JBbGwoJy5sb2dpbkluIC5lbC1mb3JtLWl0ZW1fX2xhYmVsJykuZm9yRWFjaChlbD0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgZWwuc3R5bGUuY29sb3IgPSAicmdiYSg1OSwgMTYwLCAyMTUsIDEpIg0KICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICBzZXRUaW1lb3V0KCgpPT57DQogICAgICAgICAgICAgICAgICAgICAgICBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCcubG9naW5JbiAucm9sZSAuZWwtcmFkaW9fX2xhYmVsJykuZm9yRWFjaChlbD0+ew0KICAgICAgICAgICAgICAgICAgICAgICAgICAgIGVsLnN0eWxlLmNvbG9yID0gIiNmZmYiDQogICAgICAgICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgICAgICB9LDM1MCkNCiAgICAgICAgICAgICAgICB9KQ0KDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgcmVnaXN0ZXIodGFibGVOYW1lKXsNCiAgICAgICAgICAgICAgICB0aGlzLiRzdG9yYWdlLnNldCgibG9naW5UYWJsZSIsIHRhYmxlTmFtZSk7DQogICAgICAgICAgICAgICAgdGhpcy4kcm91dGVyLnB1c2goe3BhdGg6Jy9yZWdpc3Rlcid9KQ0KICAgICAgICAgICAgfSwNCiAgICAgICAgICAgIC8vIOeZu+mZhg0KICAgICAgICAgICAgbG9naW4oKSB7DQogICAgICAgICAgICAgICAgbGV0IGNvZGUgPSAnJw0KICAgICAgICAgICAgICAgIGZvcihsZXQgaSBpbiB0aGlzLmNvZGVzKSB7DQogICAgICAgICAgICAgICAgICAgIGNvZGUgKz0gdGhpcy5jb2Rlc1tpXS5udW0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKCcwJyA9PSAnMScgJiYgIXRoaXMucnVsZXNGb3JtLmNvZGUpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+36L6T5YWl6aqM6K+B56CBIik7DQogICAgICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKCcwJyA9PSAnMScgJiYgdGhpcy5ydWxlc0Zvcm0uY29kZS50b0xvd2VyQ2FzZSgpICE9IGNvZGUudG9Mb3dlckNhc2UoKSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLpqozor4HnoIHovpPlhaXmnInor68iKTsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5nZXRSYW5kQ29kZSgpDQogICAgICAgICAgICAgICAgICAgIHJldHVybjsNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKCF0aGlzLnJ1bGVzRm9ybS51c2VybmFtZSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fovpPlhaXnlKjmiLflkI0iKTsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBpZiAoIXRoaXMucnVsZXNGb3JtLnBhc3N3b3JkKSB7DQogICAgICAgICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+i+k+WFpeWvhueggSIpOw0KICAgICAgICAgICAgICAgICAgICByZXR1cm47DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGlmICghdGhpcy5ydWxlc0Zvcm0ucm9sZSkgew0KICAgICAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7fpgInmi6nop5LoibIiKTsNCiAgICAgICAgICAgICAgICAgICAgcmV0dXJuOw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBsZXQgbWVudXMgPSB0aGlzLm1lbnVzOw0KICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbWVudXMubGVuZ3RoOyBpKyspIHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKG1lbnVzW2ldLnJvbGVOYW1lID09IHRoaXMucnVsZXNGb3JtLnJvbGUpIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMudGFibGVOYW1lID0gbWVudXNbaV0udGFibGVOYW1lOw0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHRoaXMuJGh0dHAoew0KICAgICAgICAgICAgICAgICAgICB1cmw6IGAke3RoaXMudGFibGVOYW1lfS9sb2dpbj91c2VybmFtZT0ke3RoaXMucnVsZXNGb3JtLnVzZXJuYW1lfSZwYXNzd29yZD0ke3RoaXMucnVsZXNGb3JtLnBhc3N3b3JkfWAsDQogICAgICAgICAgICAgICAgICAgIG1ldGhvZDogInBvc3QiDQogICAgICAgICAgICAgICAgfSkudGhlbigoeyBkYXRhIH0pID0+IHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRzdG9yYWdlLnNldCgiVG9rZW4iLCBkYXRhLnRva2VuKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHN0b3JhZ2Uuc2V0KCJyb2xlIiwgdGhpcy5ydWxlc0Zvcm0ucm9sZSk7DQogICAgICAgICAgICAgICAgICAgICAgICB0aGlzLiRzdG9yYWdlLnNldCgic2Vzc2lvblRhYmxlIiwgdGhpcy50YWJsZU5hbWUpOw0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kc3RvcmFnZS5zZXQoImFkbWluTmFtZSIsIHRoaXMucnVsZXNGb3JtLnVzZXJuYW1lKTsNCiAgICAgICAgICAgICAgICAgICAgICAgIHRoaXMuJHJvdXRlci5yZXBsYWNlKHsgcGF0aDogIi9pbmRleC8iIH0pOw0KICAgICAgICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyk7DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9KTsNCiAgICAgICAgICAgIH0sDQogICAgICAgICAgICBnZXRSYW5kQ29kZShsZW4gPSA0KXsNCiAgICAgICAgICAgICAgICB0aGlzLnJhbmRvbVN0cmluZyhsZW4pDQogICAgICAgICAgICB9LA0KICAgICAgICAgICAgcmFuZG9tU3RyaW5nKGxlbiA9IDQpIHsNCiAgICAgICAgICAgICAgICBsZXQgY2hhcnMgPSBbDQogICAgICAgICAgICAgICAgICAgICJhIiwgImIiLCAiYyIsICJkIiwgImUiLCAiZiIsICJnIiwgImgiLCAiaSIsICJqIiwgImsiLA0KICAgICAgICAgICAgICAgICAgICAibCIsICJtIiwgIm4iLCAibyIsICJwIiwgInEiLCAiciIsICJzIiwgInQiLCAidSIsICJ2IiwNCiAgICAgICAgICAgICAgICAgICAgInciLCAieCIsICJ5IiwgInoiLCAiQSIsICJCIiwgIkMiLCAiRCIsICJFIiwgIkYiLCAiRyIsDQogICAgICAgICAgICAgICAgICAgICJIIiwgIkkiLCAiSiIsICJLIiwgIkwiLCAiTSIsICJOIiwgIk8iLCAiUCIsICJRIiwgIlIiLA0KICAgICAgICAgICAgICAgICAgICAiUyIsICJUIiwgIlUiLCAiViIsICJXIiwgIlgiLCAiWSIsICJaIiwgIjAiLCAiMSIsICIyIiwNCiAgICAgICAgICAgICAgICAgICAgIjMiLCAiNCIsICI1IiwgIjYiLCAiNyIsICI4IiwgIjkiDQogICAgICAgICAgICAgICAgXQ0KICAgICAgICAgICAgICAgIGxldCBjb2xvcnMgPSBbIjAiLCAiMSIsICIyIiwiMyIsICI0IiwgIjUiLCAiNiIsICI3IiwgIjgiLCAiOSIsICJhIiwgImIiLCAiYyIsICJkIiwgImUiLCAiZiJdDQogICAgICAgICAgICAgICAgbGV0IHNpemVzID0gWycxNCcsICcxNScsICcxNicsICcxNycsICcxOCddDQoNCiAgICAgICAgICAgICAgICBsZXQgb3V0cHV0ID0gW107DQogICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBsZW47IGkrKykgew0KICAgICAgICAgICAgICAgICAgICAvLyDpmo/mnLrpqozor4HnoIENCiAgICAgICAgICAgICAgICAgICAgbGV0IGtleSA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSpjaGFycy5sZW5ndGgpDQogICAgICAgICAgICAgICAgICAgIHRoaXMuY29kZXNbaV0ubnVtID0gY2hhcnNba2V5XQ0KICAgICAgICAgICAgICAgICAgICAvLyDpmo/mnLrpqozor4HnoIHpopzoibINCiAgICAgICAgICAgICAgICAgICAgbGV0IGNvZGUgPSAnIycNCiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaiA9IDA7IGogPCA2OyBqKyspIHsNCiAgICAgICAgICAgICAgICAgICAgICAgIGxldCBrZXkgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkqY29sb3JzLmxlbmd0aCkNCiAgICAgICAgICAgICAgICAgICAgICAgIGNvZGUgKz0gY29sb3JzW2tleV0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB0aGlzLmNvZGVzW2ldLmNvbG9yID0gY29kZQ0KICAgICAgICAgICAgICAgICAgICAvLyDpmo/mnLrpqozor4HnoIHmlrnlkJENCiAgICAgICAgICAgICAgICAgICAgbGV0IHJvdGF0ZSA9IE1hdGguZmxvb3IoTWF0aC5yYW5kb20oKSo2MCkNCiAgICAgICAgICAgICAgICAgICAgbGV0IHBsdXMgPSBNYXRoLmZsb29yKE1hdGgucmFuZG9tKCkqMikNCiAgICAgICAgICAgICAgICAgICAgaWYocGx1cyA9PSAxKSByb3RhdGUgPSAnLScrcm90YXRlDQogICAgICAgICAgICAgICAgICAgIHRoaXMuY29kZXNbaV0ucm90YXRlID0gJ3JvdGF0ZSgnK3JvdGF0ZSsnZGVnKScNCiAgICAgICAgICAgICAgICAgICAgLy8g6ZqP5py66aqM6K+B56CB5a2X5L2T5aSn5bCPDQogICAgICAgICAgICAgICAgICAgIGxldCBzaXplID0gTWF0aC5mbG9vcihNYXRoLnJhbmRvbSgpKnNpemVzLmxlbmd0aCkNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5jb2Rlc1tpXS5zaXplID0gc2l6ZXNbc2l6ZV0rJ3B4Jw0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0sDQogICAgICAgIH0NCiAgICB9Ow0K"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AA2CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"container loginIn\" style=\"backgroundImage: url(/tiyuguan/img/img/back-img-bg.jpg)\">\r\n\r\n            <div :class=\"2 == 1 ? 'left' : 2 == 2 ? 'left center' : 'left right'\" style=\"backgroundColor: rgba(255, 255, 255, 0.9); borderRadius: 15px; boxShadow: 0 8px 32px rgba(0, 0, 0, 0.1); backdropFilter: blur(10px)\">\r\n                <el-form class=\"login-form\" label-position=\"left\" :label-width=\"2 == 3 ? '56px' : '0px'\">\r\n                    <div class=\"title-container\"><h3 class=\"title\" style=\"color: #2c3e50; fontWeight: bold; marginBottom: 30px\">体育馆使用预约平台</h3></div>\r\n                    <el-form-item :label=\"2 == 3 ? '用户名' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"user\" /></span>\r\n                        <el-input placeholder=\"请输入用户名\" name=\"username\" type=\"text\" v-model=\"rulesForm.username\" />\r\n                    </el-form-item>\r\n                    <el-form-item :label=\"2 == 3 ? '密码' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"password\" /></span>\r\n                        <el-input placeholder=\"请输入密码\" name=\"password\" type=\"password\" v-model=\"rulesForm.password\" />\r\n                    </el-form-item>\r\n                    <el-form-item v-if=\"0 == '1'\" class=\"code\" :label=\"2 == 3 ? '验证码' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"code\" /></span>\r\n                        <el-input placeholder=\"请输入验证码\" name=\"code\" type=\"text\" v-model=\"rulesForm.code\" />\r\n                        <div class=\"getCodeBt\" @click=\"getRandCode(4)\" style=\"height:44px;line-height:44px\">\r\n                            <span v-for=\"(item, index) in codes\" :key=\"index\" :style=\"{color:item.color,transform:item.rotate,fontSize:item.size}\">{{ item.num }}</span>\r\n                        </div>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"角色\" prop=\"loginInRole\" class=\"role\">\r\n                        <el-radio\r\n                                v-for=\"item in menus\"\r\n                                v-if=\"item.hasBackLogin=='是'\"\r\n                                v-bind:key=\"item.roleName\"\r\n                                v-model=\"rulesForm.role\"\r\n                                :label=\"item.roleName\"\r\n                        >{{item.roleName}}</el-radio>\r\n                    </el-form-item>\r\n                    <el-button type=\"primary\" @click=\"login()\" class=\"loginInBt\" style=\"padding:0;font-size:16px;border-radius:25px;height:44px;line-height:44px;width:100%;backgroundColor:#00c292; borderColor:#00c292; color:#fff; marginTop:20px; boxShadow: 0 4px 12px rgba(0, 194, 146, 0.3)\">{{'1' == '1' ? '登录' : 'login'}}</el-button>\r\n                    <el-form-item class=\"setting\">\r\n                                                                                                                        \t\t\t\t<div style=\"color:rgba(25, 169, 123, 1)\" class=\"register\" @click=\"register('yonghu')\">用户注册</div>\r\n                                                                                                                                                                                                                                                                                                                                            <!-- <div style=\"color:rgba(14, 14, 14, 0.95)\" class=\"reset\">修改密码</div> -->\r\n                    </el-form-item>\r\n                </el-form>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import menu from \"@/utils/menu\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                rulesForm: {\r\n                    username: \"\",\r\n                    password: \"\",\r\n                    role: \"\",\r\n                    code: '',\r\n                },\r\n                menus: [],\r\n                tableName: \"\",\r\n                codes: [{\r\n                    num: 1,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 2,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 3,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 4,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                }],\r\n            };\r\n        },\r\n        mounted() {\r\n            let menus = menu.list();\r\n            this.menus = menus;\r\n        },\r\n        created() {\r\n            this.setInputColor()\r\n            this.getRandCode()\r\n        },\r\n        methods: {\r\n            setInputColor(){\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{\r\n                        el.style.backgroundColor = \"rgba(255, 255, 255, 1)\"\r\n                        el.style.color = \"rgba(51, 51, 51, 1)\"\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                        el.style.borderRadius = \"30px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{\r\n                        el.style.color = \"rgba(59, 160, 215, 1)\"\r\n                    })\r\n                    setTimeout(()=>{\r\n                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{\r\n                            el.style.color = \"#fff\"\r\n                        })\r\n                    },350)\r\n                })\r\n\r\n            },\r\n            register(tableName){\r\n                this.$storage.set(\"loginTable\", tableName);\r\n                this.$router.push({path:'/register'})\r\n            },\r\n            // 登陆\r\n            login() {\r\n                let code = ''\r\n                for(let i in this.codes) {\r\n                    code += this.codes[i].num\r\n                }\r\n                if ('0' == '1' && !this.rulesForm.code) {\r\n                    this.$message.error(\"请输入验证码\");\r\n                    return;\r\n                }\r\n                if ('0' == '1' && this.rulesForm.code.toLowerCase() != code.toLowerCase()) {\r\n                    this.$message.error(\"验证码输入有误\");\r\n                    this.getRandCode()\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.username) {\r\n                    this.$message.error(\"请输入用户名\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.password) {\r\n                    this.$message.error(\"请输入密码\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.role) {\r\n                    this.$message.error(\"请选择角色\");\r\n                    return;\r\n                }\r\n                let menus = this.menus;\r\n                for (let i = 0; i < menus.length; i++) {\r\n                    if (menus[i].roleName == this.rulesForm.role) {\r\n                        this.tableName = menus[i].tableName;\r\n                    }\r\n                }\r\n                this.$http({\r\n                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n                    method: \"post\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.$storage.set(\"Token\", data.token);\r\n                        this.$storage.set(\"role\", this.rulesForm.role);\r\n                        this.$storage.set(\"sessionTable\", this.tableName);\r\n                        this.$storage.set(\"adminName\", this.rulesForm.username);\r\n                        this.$router.replace({ path: \"/index/\" });\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            getRandCode(len = 4){\r\n                this.randomString(len)\r\n            },\r\n            randomString(len = 4) {\r\n                let chars = [\r\n                    \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\",\r\n                    \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\",\r\n                    \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\",\r\n                    \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\",\r\n                    \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"0\", \"1\", \"2\",\r\n                    \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"\r\n                ]\r\n                let colors = [\"0\", \"1\", \"2\",\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\"]\r\n                let sizes = ['14', '15', '16', '17', '18']\r\n\r\n                let output = [];\r\n                for (let i = 0; i < len; i++) {\r\n                    // 随机验证码\r\n                    let key = Math.floor(Math.random()*chars.length)\r\n                    this.codes[i].num = chars[key]\r\n                    // 随机验证码颜色\r\n                    let code = '#'\r\n                    for (let j = 0; j < 6; j++) {\r\n                        let key = Math.floor(Math.random()*colors.length)\r\n                        code += colors[key]\r\n                    }\r\n                    this.codes[i].color = code\r\n                    // 随机验证码方向\r\n                    let rotate = Math.floor(Math.random()*60)\r\n                    let plus = Math.floor(Math.random()*2)\r\n                    if(plus == 1) rotate = '-'+rotate\r\n                    this.codes[i].rotate = 'rotate('+rotate+'deg)'\r\n                    // 随机验证码字体大小\r\n                    let size = Math.floor(Math.random()*sizes.length)\r\n                    this.codes[i].size = sizes[size]+'px'\r\n                }\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .loginIn {\r\n        min-height: 100vh;\r\n        position: relative;\r\n        background-repeat: no-repeat;\r\n        background-position: center center;\r\n        background-size: cover;\r\n        background-attachment: fixed;\r\n\r\n    .left {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n\r\n    .login-form {\r\n        background-color: transparent;\r\n        width: 100%;\r\n        right: inherit;\r\n        padding: 0 12px;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        justify-content: center;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .title-container {\r\n        text-align: center;\r\n        font-size: 24px;\r\n\r\n    .title {\r\n        margin: 20px 0;\r\n    }\r\n    }\r\n\r\n    .el-form-item {\r\n        position: relative;\r\n\r\n    .svg-container {\r\n        padding: 6px 5px 6px 15px;\r\n        color: #889aa4;\r\n        vertical-align: middle;\r\n        display: inline-block;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        z-index: 1;\r\n        padding: 0;\r\n        line-height: 40px;\r\n        width: 30px;\r\n        text-align: center;\r\n    }\r\n\r\n    .el-input {\r\n        display: inline-block;\r\n        height: 40px;\r\n        width: 100%;\r\n\r\n    & ::v-deep input {\r\n          background: transparent;\r\n          border: 0px;\r\n          -webkit-appearance: none;\r\n          padding: 0 15px 0 30px;\r\n          color: #fff;\r\n          height: 40px;\r\n      }\r\n    }\r\n\r\n    }\r\n\r\n\r\n    }\r\n\r\n    .center {\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        width: 380px;\r\n        transform: translate3d(-50%,-50%,0);\r\n        height: auto;\r\n        border-radius: 15px;\r\n        padding: 40px 30px;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .right {\r\n        position: absolute;\r\n        left: inherit;\r\n        right: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n    }\r\n\r\n    .code {\r\n    .el-form-item__content {\r\n        position: relative;\r\n\r\n    .getCodeBt {\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        line-height: 40px;\r\n        width: 100px;\r\n        background-color: rgba(51,51,51,0.4);\r\n        color: #fff;\r\n        text-align: center;\r\n        border-radius: 0 4px 4px 0;\r\n        height: 40px;\r\n        overflow: hidden;\r\n\r\n    span {\r\n        padding: 0 5px;\r\n        display: inline-block;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n    }\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 130px 0 30px;\r\n      }\r\n    }\r\n    }\r\n    }\r\n\r\n    .setting {\r\n    & ::v-deep .el-form-item__content {\r\n          padding: 0 15px;\r\n          box-sizing: border-box;\r\n          line-height: 32px;\r\n          height: 32px;\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin: 0 !important;\r\n\r\n    .register {\r\n        float: left;\r\n        width: 50%;\r\n    }\r\n\r\n    .reset {\r\n        float: right;\r\n        width: 50%;\r\n        text-align: right;\r\n    }\r\n    }\r\n    }\r\n\r\n    .style2 {\r\n        padding-left: 30px;\r\n\r\n    .svg-container {\r\n        left: -30px !important;\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .code.style2, .code.style3 {\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 115px 0 15px;\r\n      }\r\n    }\r\n    }\r\n\r\n    .style3 {\r\n    & ::v-deep .el-form-item__label {\r\n          padding-right: 6px;\r\n      }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .role {\r\n    & ::v-deep .el-form-item__label {\r\n          width: 56px !important;\r\n      }\r\n\r\n    & ::v-deep .el-radio {\r\n          margin-right: 12px;\r\n      }\r\n    }\r\n\r\n    }\r\n\r\n    /* 添加输入框样式优化 */\r\n    .el-input__inner {\r\n        border-radius: 8px !important;\r\n        border: 1px solid #e0e0e0 !important;\r\n        transition: all 0.3s ease !important;\r\n    }\r\n\r\n    .el-input__inner:focus {\r\n        border-color: #00c292 !important;\r\n        box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2) !important;\r\n    }\r\n\r\n    /* 登录按钮悬停效果 */\r\n    .loginInBt:hover {\r\n        background-color: #00a085 !important;\r\n        border-color: #00a085 !important;\r\n        transform: translateY(-2px) !important;\r\n        box-shadow: 0 6px 16px rgba(0, 194, 146, 0.4) !important;\r\n    }\r\n\r\n    /* 标题样式优化 */\r\n    .title {\r\n        text-align: center;\r\n        font-size: 24px !important;\r\n        font-weight: 600 !important;\r\n        margin-bottom: 30px !important;\r\n        color: #2c3e50 !important;\r\n    }\r\n</style>\r\n\r\n"]}]}