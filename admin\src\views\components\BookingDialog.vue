<template>
  <el-dialog
    title="场地预约"
    :visible.sync="dialogVisible"
    width="600px"
    :before-close="handleClose"
    class="booking-dialog">
    
    <div class="booking-content">
      <!-- 场地信息 -->
      <div class="venue-summary">
        <div class="venue-image">
          <img :src="venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'" :alt="venue.changdiName">
        </div>
        <div class="venue-info">
          <h3>{{ venue.changdiName }}</h3>
          <div class="venue-details">
            <div class="detail-item">
              <i class="el-icon-location"></i>
              <span>{{ venue.changdiValue }}</span>
            </div>
            <div class="detail-item">
              <i class="el-icon-star-on"></i>
              <span>{{ venue.banquanValue }}</span>
            </div>
          </div>
          <div class="venue-price">
            <span class="current-price">¥{{ venue.changdiNewMoney }}</span>
            <span v-if="venue.changdiOldMoney !== venue.changdiNewMoney" class="original-price">
              ¥{{ venue.changdiOldMoney }}
            </span>
          </div>
        </div>
      </div>

      <!-- 预约表单 -->
      <el-form :model="bookingForm" :rules="rules" ref="bookingForm" label-width="100px">
        <el-form-item label="预约日期" prop="bookingDate">
          <el-date-picker
            v-model="bookingForm.bookingDate"
            type="date"
            placeholder="选择预约日期"
            :picker-options="datePickerOptions"
            style="width: 100%">
          </el-date-picker>
        </el-form-item>
        
        <el-form-item label="时间段" prop="timeSlot">
          <el-select v-model="bookingForm.timeSlot" placeholder="请选择时间段" style="width: 100%">
            <el-option
              v-for="slot in timeSlots"
              :key="slot.value"
              :label="slot.label"
              :value="slot.value"
              :disabled="slot.disabled">
            </el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="预约人数" prop="peopleCount">
          <el-input-number
            v-model="bookingForm.peopleCount"
            :min="1"
            :max="20"
            style="width: 100%">
          </el-input-number>
        </el-form-item>
        
        <el-form-item label="联系电话" prop="phone">
          <el-input v-model="bookingForm.phone" placeholder="请输入联系电话"></el-input>
        </el-form-item>
        
        <el-form-item label="备注信息">
          <el-input
            v-model="bookingForm.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息（选填）">
          </el-input>
        </el-form-item>
      </el-form>

      <!-- 价格信息 -->
      <div class="price-summary">
        <div class="price-item">
          <span>场地费用：</span>
          <span>¥{{ venue.changdiNewMoney }}</span>
        </div>
        <div class="price-item">
          <span>预约人数：</span>
          <span>{{ bookingForm.peopleCount }}人</span>
        </div>
        <div class="price-item total">
          <span>总计费用：</span>
          <span class="total-price">¥{{ totalPrice }}</span>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="submitBooking" :loading="submitting">
        确认预约
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'BookingDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    venue: {
      type: Object,
      default: () => ({})
    }
  },
  
  data() {
    return {
      dialogVisible: false,
      submitting: false,
      
      bookingForm: {
        bookingDate: '',
        timeSlot: '',
        peopleCount: 1,
        phone: '',
        remark: ''
      },
      
      rules: {
        bookingDate: [
          { required: true, message: '请选择预约日期', trigger: 'change' }
        ],
        timeSlot: [
          { required: true, message: '请选择时间段', trigger: 'change' }
        ],
        peopleCount: [
          { required: true, message: '请输入预约人数', trigger: 'blur' }
        ],
        phone: [
          { required: true, message: '请输入联系电话', trigger: 'blur' },
          { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
        ]
      },
      
      timeSlots: [
        { label: '08:00-10:00', value: '08:00-10:00', disabled: false },
        { label: '10:00-12:00', value: '10:00-12:00', disabled: false },
        { label: '12:00-14:00', value: '12:00-14:00', disabled: false },
        { label: '14:00-16:00', value: '14:00-16:00', disabled: false },
        { label: '16:00-18:00', value: '16:00-18:00', disabled: false },
        { label: '18:00-20:00', value: '18:00-20:00', disabled: false },
        { label: '20:00-22:00', value: '20:00-22:00', disabled: false }
      ],
      
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期
        }
      }
    }
  },
  
  computed: {
    totalPrice() {
      return (this.venue.changdiNewMoney || 0) * (this.bookingForm.peopleCount || 1)
    }
  },
  
  watch: {
    visible(val) {
      this.dialogVisible = val
      if (val) {
        this.initForm()
      }
    },
    
    dialogVisible(val) {
      this.$emit('update:visible', val)
    }
  },
  
  methods: {
    // 初始化表单
    initForm() {
      // 获取用户信息
      this.$http({
        url: `${this.$storage.get("sessionTable")}/session`,
        method: "get"
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.bookingForm.phone = data.data.yonghuPhone || ''
        }
      })
      
      // 重置表单
      this.bookingForm = {
        bookingDate: '',
        timeSlot: '',
        peopleCount: 1,
        phone: this.bookingForm.phone || '',
        remark: ''
      }
    },
    
    // 提交预约
    submitBooking() {
      this.$refs.bookingForm.validate((valid) => {
        if (valid) {
          this.submitting = true
          
          const bookingData = {
            changdiId: this.venue.id,
            changdiOrderTruePrice: this.totalPrice,
            changdiOrderTypes: 1, // 预约状态
            shijianduan: this.bookingForm.timeSlot,
            buyTime: this.bookingForm.bookingDate,
            insertTime: new Date().toISOString().slice(0, 19).replace('T', ' ')
          }
          
          this.$http({
            url: 'changdiOrder/save',
            method: 'post',
            data: bookingData
          }).then(({ data }) => {
            this.submitting = false
            if (data && data.code === 0) {
              this.$message.success('预约成功！')
              this.$emit('booking-success')
              this.handleClose()
            } else {
              this.$message.error(data.msg || '预约失败')
            }
          }).catch(() => {
            this.submitting = false
            this.$message.error('网络错误，请稍后重试')
          })
        }
      })
    },
    
    // 关闭对话框
    handleClose() {
      this.dialogVisible = false
      this.$refs.bookingForm.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .booking-dialog {
  .el-dialog__header {
    background: linear-gradient(45deg, #00c292, #00a085);
    color: white;
    padding: 20px 30px;
    
    .el-dialog__title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
    
    .el-dialog__close {
      color: white;
      font-size: 20px;
    }
  }
  
  .el-dialog__body {
    padding: 30px;
  }
}

.booking-content {
  .venue-summary {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8fffe;
    border-radius: 12px;
    border: 1px solid rgba(0, 194, 146, 0.1);
    
    .venue-image {
      flex: 0 0 120px;
      
      img {
        width: 120px;
        height: 90px;
        object-fit: cover;
        border-radius: 8px;
      }
    }
    
    .venue-info {
      flex: 1;
      
      h3 {
        font-size: 20px;
        color: #2c3e50;
        margin: 0 0 15px 0;
      }
      
      .venue-details {
        margin-bottom: 15px;
        
        .detail-item {
          display: flex;
          align-items: center;
          margin-bottom: 5px;
          color: #666;
          font-size: 14px;
          
          i {
            color: #00c292;
            margin-right: 8px;
            width: 16px;
          }
        }
      }
      
      .venue-price {
        .current-price {
          font-size: 20px;
          font-weight: 700;
          color: #00c292;
        }
        
        .original-price {
          font-size: 14px;
          color: #999;
          text-decoration: line-through;
          margin-left: 10px;
        }
      }
    }
  }
  
  .price-summary {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    
    .price-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      
      &.total {
        border-top: 1px solid #ddd;
        padding-top: 10px;
        margin-top: 10px;
        font-weight: 600;
        
        .total-price {
          font-size: 18px;
          color: #00c292;
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    border-radius: 8px;
    font-weight: 600;
    
    &.el-button--primary {
      background: linear-gradient(45deg, #00c292, #00a085);
      border: none;
      
      &:hover {
        background: linear-gradient(45deg, #00a085, #008f75);
      }
    }
  }
}
</style>
