{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeChart.vue", "mtime": 1750589717478}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["HomeChart.vue"], "names": [], "mappings": ";AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HomeChart.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\r\n  <div id=\"home-chart\" style=\"width:100%;height:400px;\"></div>\r\n</template>\r\n<script>\r\nexport default {\r\n  mounted() {\r\n    this.homeChart();\r\n  },\r\n  methods: {\r\n    homeChart() {\r\n      // 基于准备好的dom，初始化echarts实例\r\n      var myChart = this.$echarts.init(document.getElementById(\"home-chart\"));\r\n      // 指定图表的配置项和数据\r\n      var option = {\r\n        tooltip: {\r\n          trigger: \"axis\"\r\n        },\r\n        legend: {\r\n          data: [\"访问量\", \"用户量\", \"收入\"]\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"3%\",\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          boundaryGap: false,\r\n          data: [\r\n            \"1月\",\r\n            \"2月\",\r\n            \"3月\",\r\n            \"4月\",\r\n            \"5月\",\r\n            \"6月\",\r\n            \"7月\",\r\n            \"8月\",\r\n            \"9月\",\r\n            \"10月\",\r\n            \"11月\",\r\n            \"12月\"\r\n          ]\r\n        },\r\n        yAxis: {\r\n          type: \"value\"\r\n        },\r\n        series: [\r\n          {\r\n            name: \"访问量\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              120,\r\n              132,\r\n              101,\r\n              134,\r\n              90,\r\n              230,\r\n              210,\r\n              120,\r\n              132,\r\n              101,\r\n              134,\r\n              90,\r\n              230\r\n            ]\r\n          },\r\n          {\r\n            name: \"用户量\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              220,\r\n              182,\r\n              191,\r\n              234,\r\n              290,\r\n              330,\r\n              310,\r\n              182,\r\n              191,\r\n              234,\r\n              290,\r\n              330,\r\n              310\r\n            ]\r\n          },\r\n          {\r\n            name: \"收入\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              150,\r\n              232,\r\n              201,\r\n              154,\r\n              190,\r\n              330,\r\n              410,\r\n              232,\r\n              201,\r\n              154,\r\n              190,\r\n              330,\r\n              410\r\n            ]\r\n          }\r\n        ]\r\n      };\r\n      // // 使用刚指定的配置项和数据显示图表\r\n      myChart.setOption(option);\r\n      //根据窗口的大小变动图表\r\n      window.onresize = function() {\r\n        myChart.resize();\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n#home-chart {\r\n  background: #ffffff;\r\n  padding: 20px 0;\r\n}\r\n</style>\r\n\r\n"]}]}