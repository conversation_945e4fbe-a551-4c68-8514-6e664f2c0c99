{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\test-input.vue?vue&type=style&index=0&id=3378e0e9&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\test-input.vue", "mtime": 1750609938955}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci50ZXN0LWlucHV0LWNvbnRhaW5lciB7CiAgcGFkZGluZzogMjBweDsKICBtYXgtd2lkdGg6IDgwMHB4OwogIG1hcmdpbjogMCBhdXRvOwp9CgoudGVzdC1zZWN0aW9uIHsKICBtYXJnaW46IDMwcHggMDsKICBwYWRkaW5nOiAyMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNkZGQ7CiAgYm9yZGVyLXJhZGl1czogOHB4OwogIGJhY2tncm91bmQ6ICNmOWY5Zjk7Cn0KCi5sb2dpbi1zdHlsZS1mb3JtIHsKICAuZm9ybS1pdGVtIHsKICAgIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgICAKICAgIC5pbnB1dC13cmFwcGVyIHsKICAgICAgcG9zaXRpb246IHJlbGF0aXZlOwogICAgICAKICAgICAgLmlucHV0LWljb24gewogICAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgICBsZWZ0OiAxNXB4OwogICAgICAgIHRvcDogNTAlOwogICAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNTAlKTsKICAgICAgICBjb2xvcjogIzAwYzI5MjsKICAgICAgICBmb250LXNpemU6IDE4cHg7CiAgICAgICAgei1pbmRleDogMjsKICAgICAgfQogICAgICAKICAgICAgOjp2LWRlZXAgLmVsLWlucHV0X19pbm5lciB7CiAgICAgICAgaGVpZ2h0OiA1MHB4OwogICAgICAgIHBhZGRpbmctbGVmdDogNTBweDsKICAgICAgICBib3JkZXI6IDJweCBzb2xpZCAjZThmNGY4OwogICAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICAgIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgICAgICAgYmFja2dyb3VuZDogd2hpdGUgIWltcG9ydGFudDsKICAgICAgICBjb2xvcjogIzMzMyAhaW1wb3J0YW50OwogICAgICAgIHBvaW50ZXItZXZlbnRzOiBhdXRvICFpbXBvcnRhbnQ7CiAgICAgICAgdXNlci1zZWxlY3Q6IHRleHQgIWltcG9ydGFudDsKICAgICAgICBjdXJzb3I6IHRleHQgIWltcG9ydGFudDsKCiAgICAgICAgJjpmb2N1cyB7CiAgICAgICAgICBib3JkZXItY29sb3I6ICMwMGMyOTI7CiAgICAgICAgICBib3gtc2hhZG93OiAwIDAgMCAzcHggcmdiYSgwLCAxOTQsIDE0NiwgMC4xKTsKICAgICAgICAgIGJhY2tncm91bmQ6IHdoaXRlICFpbXBvcnRhbnQ7CiAgICAgICAgICBjb2xvcjogIzMzMyAhaW1wb3J0YW50OwogICAgICAgICAgb3V0bGluZTogbm9uZTsKICAgICAgICB9CgogICAgICAgICY6OnBsYWNlaG9sZGVyIHsKICAgICAgICAgIGNvbG9yOiAjOTk5ICFpbXBvcnRhbnQ7CiAgICAgICAgfQoKICAgICAgICAmOmhvdmVyIHsKICAgICAgICAgIGJvcmRlci1jb2xvcjogIzAwYzI5MjsKICAgICAgICB9CiAgICAgIH0KICAgIH0KICB9Cn0KCi50ZXN0LXJlc3VsdHMgewogIHByZSB7CiAgICBiYWNrZ3JvdW5kOiAjZjVmNWY1OwogICAgcGFkZGluZzogMTBweDsKICAgIGJvcmRlci1yYWRpdXM6IDRweDsKICAgIGZvbnQtc2l6ZTogMTJweDsKICAgIG92ZXJmbG93LXg6IGF1dG87CiAgfQp9Cg=="}, {"version": 3, "sources": ["test-input.vue"], "names": [], "mappings": ";AA+IA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "test-input.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"test-input-container\">\n    <h2>输入框测试页面</h2>\n    \n    <div class=\"test-section\">\n      <h3>1. 基础输入框测试</h3>\n      <el-form>\n        <el-form-item label=\"用户名\">\n          <el-input v-model=\"testForm.username\" placeholder=\"请输入用户名\" clearable></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"密码\">\n          <el-input v-model=\"testForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password clearable></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"邮箱\">\n          <el-input v-model=\"testForm.email\" placeholder=\"请输入邮箱\" clearable></el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n    \n    <div class=\"test-section\">\n      <h3>2. 登录样式输入框测试</h3>\n      <div class=\"login-style-form\">\n        <div class=\"form-item\">\n          <div class=\"input-wrapper\">\n            <i class=\"el-icon-user input-icon\"></i>\n            <el-input v-model=\"loginForm.username\" placeholder=\"请输入用户名\" size=\"large\" clearable></el-input>\n          </div>\n        </div>\n        \n        <div class=\"form-item\">\n          <div class=\"input-wrapper\">\n            <i class=\"el-icon-lock input-icon\"></i>\n            <el-input v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" size=\"large\" show-password clearable></el-input>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"test-section\">\n      <h3>3. 测试结果</h3>\n      <div class=\"test-results\">\n        <p><strong>基础表单数据:</strong></p>\n        <pre>{{ JSON.stringify(testForm, null, 2) }}</pre>\n        \n        <p><strong>登录表单数据:</strong></p>\n        <pre>{{ JSON.stringify(loginForm, null, 2) }}</pre>\n      </div>\n    </div>\n    \n    <div class=\"test-section\">\n      <h3>4. 操作按钮</h3>\n      <el-button @click=\"clearAll\">清空所有</el-button>\n      <el-button @click=\"fillTest\" type=\"primary\">填充测试数据</el-button>\n      <el-button @click=\"checkInputs\" type=\"success\">检查输入框状态</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TestInput',\n  data() {\n    return {\n      testForm: {\n        username: '',\n        password: '',\n        email: ''\n      },\n      loginForm: {\n        username: '',\n        password: ''\n      }\n    }\n  },\n  mounted() {\n    this.enableAllInputs();\n  },\n  methods: {\n    enableAllInputs() {\n      this.$nextTick(() => {\n        // 确保所有输入框都可以正常使用\n        document.querySelectorAll('.el-input__inner').forEach(el => {\n          el.removeAttribute('readonly');\n          el.removeAttribute('disabled');\n          el.style.pointerEvents = 'auto';\n          el.style.userSelect = 'text';\n          el.style.cursor = 'text';\n          el.style.backgroundColor = 'white';\n          el.style.color = '#333';\n        });\n      });\n    },\n    \n    clearAll() {\n      this.testForm = {\n        username: '',\n        password: '',\n        email: ''\n      };\n      this.loginForm = {\n        username: '',\n        password: ''\n      };\n    },\n    \n    fillTest() {\n      this.testForm = {\n        username: 'testuser',\n        password: 'testpass',\n        email: '<EMAIL>'\n      };\n      this.loginForm = {\n        username: 'admin',\n        password: 'admin123'\n      };\n    },\n    \n    checkInputs() {\n      const inputs = document.querySelectorAll('.el-input__inner');\n      let report = [];\n      \n      inputs.forEach((input, index) => {\n        report.push({\n          index: index,\n          readonly: input.hasAttribute('readonly'),\n          disabled: input.hasAttribute('disabled'),\n          pointerEvents: input.style.pointerEvents,\n          userSelect: input.style.userSelect,\n          cursor: input.style.cursor,\n          value: input.value\n        });\n      });\n      \n      console.log('输入框状态检查:', report);\n      this.$message.success('输入框状态已输出到控制台');\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.test-input-container {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin: 30px 0;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background: #f9f9f9;\n}\n\n.login-style-form {\n  .form-item {\n    margin-bottom: 20px;\n    \n    .input-wrapper {\n      position: relative;\n      \n      .input-icon {\n        position: absolute;\n        left: 15px;\n        top: 50%;\n        transform: translateY(-50%);\n        color: #00c292;\n        font-size: 18px;\n        z-index: 2;\n      }\n      \n      ::v-deep .el-input__inner {\n        height: 50px;\n        padding-left: 50px;\n        border: 2px solid #e8f4f8;\n        border-radius: 12px;\n        font-size: 16px;\n        transition: all 0.3s ease;\n        background: white !important;\n        color: #333 !important;\n        pointer-events: auto !important;\n        user-select: text !important;\n        cursor: text !important;\n\n        &:focus {\n          border-color: #00c292;\n          box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);\n          background: white !important;\n          color: #333 !important;\n          outline: none;\n        }\n\n        &::placeholder {\n          color: #999 !important;\n        }\n\n        &:hover {\n          border-color: #00c292;\n        }\n      }\n    }\n  }\n}\n\n.test-results {\n  pre {\n    background: #f5f5f5;\n    padding: 10px;\n    border-radius: 4px;\n    font-size: 12px;\n    overflow-x: auto;\n  }\n}\n</style>\n"]}]}