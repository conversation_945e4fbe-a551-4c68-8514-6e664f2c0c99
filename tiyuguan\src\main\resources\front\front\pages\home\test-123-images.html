<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>测试1.jpg、2.jpg、3.jpg图片</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <style>
        body { padding: 20px; font-family: Arial, sans-serif; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .image-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .image-item { text-align: center; border: 1px solid #eee; padding: 10px; border-radius: 5px; }
        .image-item img { max-width: 100%; height: 200px; object-fit: cover; border-radius: 3px; }
        .status { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>

<h1>测试1.jpg、2.jpg、3.jpg图片</h1>

<div class="test-section">
    <h3>1. 单独图片测试</h3>
    <div class="image-grid">
        <div class="image-item">
            <h4>1.jpg</h4>
            <img id="img1" src="../../xznstatic/img/1.jpg" alt="1.jpg" onload="imageLoaded('img1')" onerror="imageError('img1')"/>
            <div id="status1" class="status">加载中...</div>
        </div>
        <div class="image-item">
            <h4>2.jpg</h4>
            <img id="img2" src="../../xznstatic/img/2.jpg" alt="2.jpg" onload="imageLoaded('img2')" onerror="imageError('img2')"/>
            <div id="status2" class="status">加载中...</div>
        </div>
        <div class="image-item">
            <h4>3.jpg</h4>
            <img id="img3" src="../../xznstatic/img/3.jpg" alt="3.jpg" onload="imageLoaded('img3')" onerror="imageError('img3')"/>
            <div id="status3" class="status">加载中...</div>
        </div>
    </div>
</div>

<div class="test-section">
    <h3>2. 轮播图测试</h3>
    <div class="layui-carousel" id="carousel123" style="width: 100%; height: 400px;">
        <div carousel-item>
            <div><img src="../../xznstatic/img/1.jpg" style="width: 100%;height: 100%;object-fit:cover;" alt="轮播图1"/></div>
            <div><img src="../../xznstatic/img/2.jpg" style="width: 100%;height: 100%;object-fit:cover;" alt="轮播图2"/></div>
            <div><img src="../../xznstatic/img/3.jpg" style="width: 100%;height: 100%;object-fit:cover;" alt="轮播图3"/></div>
        </div>
    </div>
    <div class="status">轮播图自动播放中，每3秒切换一次</div>
</div>

<div class="test-section">
    <h3>3. 文件信息</h3>
    <div id="fileInfo">
        <p><strong>图片路径:</strong></p>
        <ul>
            <li>1.jpg: ../../xznstatic/img/1.jpg</li>
            <li>2.jpg: ../../xznstatic/img/2.jpg</li>
            <li>3.jpg: ../../xznstatic/img/3.jpg</li>
        </ul>
        <p><strong>完整URL:</strong></p>
        <ul id="fullUrls"></ul>
        <p><strong>测试时间:</strong> <span id="testTime"></span></p>
    </div>
</div>

<div class="test-section">
    <h3>4. 操作按钮</h3>
    <button class="layui-btn" onclick="reloadImages()">重新加载图片</button>
    <button class="layui-btn layui-btn-normal" onclick="testCarousel()">重新初始化轮播图</button>
    <button class="layui-btn layui-btn-warm" onclick="checkImageSizes()">检查图片尺寸</button>
</div>

<script src="../../layui/layui.js"></script>
<script>
// 初始化页面信息
document.getElementById('testTime').textContent = new Date().toLocaleString();

// 显示完整URL
const fullUrls = document.getElementById('fullUrls');
const baseUrl = window.location.origin + window.location.pathname.replace('/test-123-images.html', '');
['1.jpg', '2.jpg', '3.jpg'].forEach(filename => {
    const li = document.createElement('li');
    li.textContent = `${filename}: ${baseUrl}/../../xznstatic/img/${filename}`;
    fullUrls.appendChild(li);
});

// 图片加载成功
function imageLoaded(imgId) {
    const statusId = 'status' + imgId.replace('img', '');
    const statusEl = document.getElementById(statusId);
    const imgEl = document.getElementById(imgId);
    
    statusEl.textContent = `✅ 加载成功 (${imgEl.naturalWidth}x${imgEl.naturalHeight})`;
    statusEl.className = 'status success';
}

// 图片加载失败
function imageError(imgId) {
    const statusId = 'status' + imgId.replace('img', '');
    const statusEl = document.getElementById(statusId);
    
    statusEl.textContent = '❌ 加载失败';
    statusEl.className = 'status error';
}

// 重新加载图片
function reloadImages() {
    ['img1', 'img2', 'img3'].forEach(imgId => {
        const img = document.getElementById(imgId);
        const statusId = 'status' + imgId.replace('img', '');
        const statusEl = document.getElementById(statusId);
        
        statusEl.textContent = '重新加载中...';
        statusEl.className = 'status';
        
        // 添加时间戳强制重新加载
        const src = img.src.split('?')[0];
        img.src = src + '?t=' + new Date().getTime();
    });
}

// 测试轮播图
function testCarousel() {
    layui.use('carousel', function(){
        var carousel = layui.carousel;
        carousel.render({
            elem: '#carousel123',
            width: '100%',
            height: '400px',
            arrow: 'hover',
            anim: 'default',
            autoplay: true,
            interval: 3000,
            indicator: 'inside'
        });
    });
    console.log('轮播图重新初始化完成');
}

// 检查图片尺寸
function checkImageSizes() {
    ['img1', 'img2', 'img3'].forEach(imgId => {
        const img = document.getElementById(imgId);
        if (img.complete && img.naturalWidth > 0) {
            console.log(`${imgId}: ${img.naturalWidth}x${img.naturalHeight}, 文件大小: ${img.src.length} bytes (URL长度)`);
        }
    });
}

// 初始化轮播图
layui.use('carousel', function(){
    var carousel = layui.carousel;
    carousel.render({
        elem: '#carousel123',
        width: '100%',
        height: '400px',
        arrow: 'hover',
        anim: 'default',
        autoplay: true,
        interval: 3000,
        indicator: 'inside'
    });
});
</script>

</body>
</html>
