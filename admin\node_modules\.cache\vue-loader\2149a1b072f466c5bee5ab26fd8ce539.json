{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCarousel.vue?vue&type=style&index=0&id=bd762162&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCarousel.vue", "mtime": 1750589744900}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5jYXJvdXNlbC1jb250YWluZXIgewogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgCiAgLmNhcm91c2VsLWl0ZW0gewogICAgd2lkdGg6IDEwMCU7CiAgICBoZWlnaHQ6IDMwMHB4OwogICAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjsKICAgIGJhY2tncm91bmQtcG9zaXRpb246IGNlbnRlcjsKICAgIGJhY2tncm91bmQtcmVwZWF0OiBuby1yZXBlYXQ7CiAgICBib3JkZXItcmFkaXVzOiA4cHg7CiAgICBwb3NpdGlvbjogcmVsYXRpdmU7CiAgICBvdmVyZmxvdzogaGlkZGVuOwogICAgCiAgICAmOjpiZWZvcmUgewogICAgICBjb250ZW50OiAnJzsKICAgICAgcG9zaXRpb246IGFic29sdXRlOwogICAgICB0b3A6IDA7CiAgICAgIGxlZnQ6IDA7CiAgICAgIHJpZ2h0OiAwOwogICAgICBib3R0b206IDA7CiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgcmdiYSgwLCAwLCAwLCAwLjMpLCByZ2JhKDAsIDAsIDAsIDAuMSkpOwogICAgICB6LWluZGV4OiAxOwogICAgfQogICAgCiAgICAuY2Fyb3VzZWwtY29udGVudCB7CiAgICAgIHBvc2l0aW9uOiBhYnNvbHV0ZTsKICAgICAgYm90dG9tOiAzMHB4OwogICAgICBsZWZ0OiAzMHB4OwogICAgICBjb2xvcjogd2hpdGU7CiAgICAgIHotaW5kZXg6IDI7CiAgICAgIAogICAgICBoMyB7CiAgICAgICAgZm9udC1zaXplOiAyNHB4OwogICAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICAgIG1hcmdpbjogMCAwIDEwcHggMDsKICAgICAgICB0ZXh0LXNoYWRvdzogMnB4IDJweCA0cHggcmdiYSgwLCAwLCAwLCAwLjUpOwogICAgICB9CiAgICAgIAogICAgICBwIHsKICAgICAgICBmb250LXNpemU6IDE2cHg7CiAgICAgICAgbWFyZ2luOiAwOwogICAgICAgIG9wYWNpdHk6IDAuOTsKICAgICAgICB0ZXh0LXNoYWRvdzogMXB4IDFweCAycHggcmdiYSgwLCAwLCAwLCAwLjUpOwogICAgICB9CiAgICB9CiAgfQp9Cgo6OnYtZGVlcCAuZWwtY2Fyb3VzZWxfX2luZGljYXRvciB7CiAgLmVsLWNhcm91c2VsX19idXR0b24gewogICAgYmFja2dyb3VuZC1jb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjUpOwogICAgCiAgICAmLmlzLWFjdGl2ZSB7CiAgICAgIGJhY2tncm91bmQtY29sb3I6ICMwMGMyOTI7CiAgICB9CiAgfQp9Cg=="}, {"version": 3, "sources": ["HomeCarousel.vue"], "names": [], "mappings": ";AAqEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "HomeCarousel.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\n  <div class=\"carousel-container\">\n    <el-carousel :interval=\"4000\" type=\"card\" height=\"300px\" indicator-position=\"outside\">\n      <el-carousel-item v-for=\"(item, index) in carouselData\" :key=\"index\">\n        <div class=\"carousel-item\" :style=\"{ backgroundImage: `url(${item.image})` }\">\n          <div class=\"carousel-content\">\n            <h3>{{ item.title }}</h3>\n            <p>{{ item.description }}</p>\n          </div>\n        </div>\n      </el-carousel-item>\n    </el-carousel>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeCarousel',\n  data() {\n    return {\n      carouselData: [\n        {\n          title: '体育馆管理系统',\n          description: '现代化的体育场馆管理解决方案',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        },\n        {\n          title: '场地预约',\n          description: '便捷的在线场地预约服务',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        },\n        {\n          title: '智能管理',\n          description: '高效的场馆运营管理平台',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadCarouselData()\n  },\n  methods: {\n    loadCarouselData() {\n      // 从后端获取轮播图数据\n      this.$http({\n        url: 'config/list',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 5\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0 && data.list && data.list.length > 0) {\n          this.carouselData = data.list.map((item, index) => ({\n            title: item.name || `轮播图 ${index + 1}`,\n            description: item.value ? '精彩内容等您发现' : '欢迎使用体育馆管理系统',\n            image: item.value || '/tiyuguan/img/img/back-img-bg.jpg'\n          }))\n        }\n      }).catch(err => {\n        console.log('获取轮播图数据失败:', err)\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.carousel-container {\n  margin-bottom: 20px;\n  \n  .carousel-item {\n    width: 100%;\n    height: 300px;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n    border-radius: 8px;\n    position: relative;\n    overflow: hidden;\n    \n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));\n      z-index: 1;\n    }\n    \n    .carousel-content {\n      position: absolute;\n      bottom: 30px;\n      left: 30px;\n      color: white;\n      z-index: 2;\n      \n      h3 {\n        font-size: 24px;\n        font-weight: bold;\n        margin: 0 0 10px 0;\n        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n      }\n      \n      p {\n        font-size: 16px;\n        margin: 0;\n        opacity: 0.9;\n        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n      }\n    }\n  }\n}\n\n::v-deep .el-carousel__indicator {\n  .el-carousel__button {\n    background-color: rgba(255, 255, 255, 0.5);\n    \n    &.is-active {\n      background-color: #00c292;\n    }\n  }\n}\n</style>\n"]}]}