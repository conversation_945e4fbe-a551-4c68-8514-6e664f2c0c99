{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue", "mtime": 1750602879994}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0IF9yZWdlbmVyYXRvclJ1bnRpbWUgZnJvbSAiRDovMS90aXl1Z3Vhbi9hZG1pbi9ub2RlX21vZHVsZXMvQGJhYmVsL3J1bnRpbWUvaGVscGVycy9lc20vcmVnZW5lcmF0b3JSdW50aW1lLmpzIjsKaW1wb3J0IF9hc3luY1RvR2VuZXJhdG9yIGZyb20gIkQ6LzEvdGl5dWd1YW4vYWRtaW4vbm9kZV9tb2R1bGVzL0BiYWJlbC9ydW50aW1lL2hlbHBlcnMvZXNtL2FzeW5jVG9HZW5lcmF0b3IuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5tYXAuanMiOwppbXBvcnQgY29sbGVjdGlvblN0b3JhZ2UgZnJvbSAnQC91dGlscy9jb2xsZWN0aW9uLXN0b3JhZ2UnOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0NvbGxlY3Rpb25NYW5hZ2VtZW50JywKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgbG9hZGluZzogZmFsc2UsCiAgICAgIGNvbGxlY3Rpb25MaXN0OiBbXSwKICAgICAgc2VsZWN0ZWRDb2xsZWN0aW9uczogW10sCiAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICBwYWdlU2l6ZTogMjAsCiAgICAgIHRvdGFsQ291bnQ6IDAsCiAgICAgIC8vIOe7n+iuoeaVsOaNrgogICAgICB0b3RhbENvbGxlY3Rpb25zOiAwLAogICAgICB0b2RheUNvbGxlY3Rpb25zOiAwLAogICAgICBhY3RpdmVVc2VyczogMCwKICAgICAgcG9wdWxhclZlbnVlczogMCwKICAgICAgLy8g5pCc57Si6KGo5Y2VCiAgICAgIHNlYXJjaEZvcm06IHsKICAgICAgICBjaGFuZ2RpTmFtZTogJycsCiAgICAgICAgeW9uZ2h1TmFtZTogJycsCiAgICAgICAgZGF0ZVJhbmdlOiBbXQogICAgICB9LAogICAgICAvLyDor6bmg4Xlr7nor53moYYKICAgICAgZGV0YWlsRGlhbG9nVmlzaWJsZTogZmFsc2UsCiAgICAgIGN1cnJlbnRDb2xsZWN0aW9uOiBudWxsLAogICAgICAvLyDlkIzmraXnirbmgIEKICAgICAgc3luY2luZzogZmFsc2UsCiAgICAgIHB1bGxpbmc6IGZhbHNlLAogICAgICBzeW5jU3RhdHVzOiB7CiAgICAgICAgc3RhdHVzOiAnbmV2ZXInLAogICAgICAgIGxhc3RTeW5jOiBudWxsCiAgICAgIH0KICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5sb2FkQ29sbGVjdGlvbnMoKTsKICAgIHRoaXMubG9hZFN0YXRzKCk7CiAgICB0aGlzLmxvYWRMb2NhbENvbGxlY3Rpb25zKCk7CiAgICB0aGlzLnN5bmNTdGF0dXMgPSBjb2xsZWN0aW9uU3RvcmFnZS5nZXRTeW5jU3RhdHVzKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliqDovb3mlLbol4/liJfooagKICAgIGxvYWRDb2xsZWN0aW9uczogZnVuY3Rpb24gbG9hZENvbGxlY3Rpb25zKCkgewogICAgICB2YXIgX3RoaXMgPSB0aGlzOwogICAgICB0aGlzLmxvYWRpbmcgPSB0cnVlOwogICAgICB2YXIgcGFyYW1zID0gewogICAgICAgIHBhZ2U6IHRoaXMuY3VycmVudFBhZ2UsCiAgICAgICAgbGltaXQ6IHRoaXMucGFnZVNpemUKICAgICAgfTsKCiAgICAgIC8vIOa3u+WKoOaQnOe0ouadoeS7tgogICAgICBpZiAodGhpcy5zZWFyY2hGb3JtLmNoYW5nZGlOYW1lKSB7CiAgICAgICAgcGFyYW1zLmNoYW5nZGlOYW1lID0gdGhpcy5zZWFyY2hGb3JtLmNoYW5nZGlOYW1lOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNlYXJjaEZvcm0ueW9uZ2h1TmFtZSkgewogICAgICAgIHBhcmFtcy55b25naHVOYW1lID0gdGhpcy5zZWFyY2hGb3JtLnlvbmdodU5hbWU7CiAgICAgIH0KICAgICAgaWYgKHRoaXMuc2VhcmNoRm9ybS5kYXRlUmFuZ2UgJiYgdGhpcy5zZWFyY2hGb3JtLmRhdGVSYW5nZS5sZW5ndGggPT09IDIpIHsKICAgICAgICBwYXJhbXMuaW5zZXJ0VGltZVN0YXJ0ID0gdGhpcy5zZWFyY2hGb3JtLmRhdGVSYW5nZVswXTsKICAgICAgICBwYXJhbXMuaW5zZXJ0VGltZUVuZCA9IHRoaXMuc2VhcmNoRm9ybS5kYXRlUmFuZ2VbMV07CiAgICAgIH0KICAgICAgdGhpcy4kaHR0cCh7CiAgICAgICAgdXJsOiAnY2hhbmdkaUNvbGxlY3Rpb24vcGFnZScsCiAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICBwYXJhbXM6IHBhcmFtcwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChfcmVmKSB7CiAgICAgICAgdmFyIGRhdGEgPSBfcmVmLmRhdGE7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgewogICAgICAgICAgX3RoaXMuY29sbGVjdGlvbkxpc3QgPSBkYXRhLmRhdGEubGlzdCB8fCBbXTsKICAgICAgICAgIF90aGlzLnRvdGFsQ291bnQgPSBkYXRhLmRhdGEudG90YWwgfHwgMDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IoZGF0YS5tc2cgfHwgJ+iOt+WPluaUtuiXj+WIl+ihqOWksei0pScpOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgX3RoaXMubG9hZGluZyA9IGZhbHNlOwogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veaUtuiXj+WIl+ihqOWksei0pTonLCBlcnJvcik7CiAgICAgICAgLy8g5Yqg6L295pys5Zyw5pS26JeP5pWw5o2u5L2c5Li65aSH55SoCiAgICAgICAgX3RoaXMubG9hZExvY2FsQ29sbGVjdGlvbnMoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yqg6L295pys5Zyw5pS26JeP5pWw5o2uCiAgICBsb2FkTG9jYWxDb2xsZWN0aW9uczogZnVuY3Rpb24gbG9hZExvY2FsQ29sbGVjdGlvbnMoKSB7CiAgICAgIHRyeSB7CiAgICAgICAgdmFyIGxvY2FsQ29sbGVjdGlvbnMgPSBjb2xsZWN0aW9uU3RvcmFnZS5nZXRBbGxDb2xsZWN0aW9ucygpOwogICAgICAgIGlmIChsb2NhbENvbGxlY3Rpb25zLmxlbmd0aCA+IDAgJiYgdGhpcy5jb2xsZWN0aW9uTGlzdC5sZW5ndGggPT09IDApIHsKICAgICAgICAgIHRoaXMuY29sbGVjdGlvbkxpc3QgPSBsb2NhbENvbGxlY3Rpb25zOwogICAgICAgICAgdGhpcy50b3RhbENvdW50ID0gbG9jYWxDb2xsZWN0aW9ucy5sZW5ndGg7CiAgICAgICAgICB0aGlzLiRtZXNzYWdlLmluZm8oIlx1NURGMlx1NTJBMFx1OEY3RFx1NjcyQ1x1NTczMFx1NjUzNlx1ODVDRlx1NjU3MFx1NjM2RSAoIi5jb25jYXQobG9jYWxDb2xsZWN0aW9ucy5sZW5ndGgsICIgXHU2NzYxKSIpKTsKICAgICAgICB9CiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295pys5Zyw5pS26JeP5pWw5o2u5aSx6LSlOicsIGVycm9yKTsKICAgICAgfQogICAgfSwKICAgIC8vIOWQjOatpeWIsOacjeWKoeWZqAogICAgc3luY1RvU2VydmVyOiBmdW5jdGlvbiBzeW5jVG9TZXJ2ZXIoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICByZXR1cm4gX2FzeW5jVG9HZW5lcmF0b3IoIC8qI19fUFVSRV9fKi9fcmVnZW5lcmF0b3JSdW50aW1lKCkubWFyayhmdW5jdGlvbiBfY2FsbGVlKCkgewogICAgICAgIHZhciByZXN1bHQ7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUkKF9jb250ZXh0KSB7CiAgICAgICAgICB3aGlsZSAoMSkgc3dpdGNoIChfY29udGV4dC5wcmV2ID0gX2NvbnRleHQubmV4dCkgewogICAgICAgICAgICBjYXNlIDA6CiAgICAgICAgICAgICAgX3RoaXMyLnN5bmNpbmcgPSB0cnVlOwogICAgICAgICAgICAgIF9jb250ZXh0LnByZXYgPSAxOwogICAgICAgICAgICAgIF9jb250ZXh0Lm5leHQgPSA0OwogICAgICAgICAgICAgIHJldHVybiBjb2xsZWN0aW9uU3RvcmFnZS5zeW5jVG9TZXJ2ZXIoX3RoaXMyLiRodHRwKTsKICAgICAgICAgICAgY2FzZSA0OgogICAgICAgICAgICAgIHJlc3VsdCA9IF9jb250ZXh0LnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7CiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2Uuc3VjY2VzcyhyZXN1bHQubWVzc2FnZSk7CiAgICAgICAgICAgICAgICBfdGhpczIubG9hZENvbGxlY3Rpb25zKCk7IC8vIOmHjeaWsOWKoOi9veaVsOaNrgogICAgICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgICAgICBfdGhpczIuJG1lc3NhZ2UuZXJyb3IocmVzdWx0Lm1lc3NhZ2UpOwogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBfdGhpczIuc3luY1N0YXR1cyA9IGNvbGxlY3Rpb25TdG9yYWdlLmdldFN5bmNTdGF0dXMoKTsKICAgICAgICAgICAgICBfY29udGV4dC5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gOTsKICAgICAgICAgICAgICBfY29udGV4dC50MCA9IF9jb250ZXh0WyJjYXRjaCJdKDEpOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WQjOatpeWksei0pTonLCBfY29udGV4dC50MCk7CiAgICAgICAgICAgICAgX3RoaXMyLiRtZXNzYWdlLmVycm9yKCflkIzmraXlpLHotKU6ICcgKyBfY29udGV4dC50MC5tZXNzYWdlKTsKICAgICAgICAgICAgY2FzZSAxMzoKICAgICAgICAgICAgICBfY29udGV4dC5wcmV2ID0gMTM7CiAgICAgICAgICAgICAgX3RoaXMyLnN5bmNpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuZmluaXNoKDEzKTsKICAgICAgICAgICAgY2FzZSAxNjoKICAgICAgICAgICAgY2FzZSAiZW5kIjoKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQuc3RvcCgpOwogICAgICAgICAgfQogICAgICAgIH0sIF9jYWxsZWUsIG51bGwsIFtbMSwgOSwgMTMsIDE2XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDku47mnI3liqHlmajmi4nlj5bmlbDmja4KICAgIHB1bGxGcm9tU2VydmVyOiBmdW5jdGlvbiBwdWxsRnJvbVNlcnZlcigpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHJldHVybiBfYXN5bmNUb0dlbmVyYXRvciggLyojX19QVVJFX18qL19yZWdlbmVyYXRvclJ1bnRpbWUoKS5tYXJrKGZ1bmN0aW9uIF9jYWxsZWUyKCkgewogICAgICAgIHZhciByZXN1bHQ7CiAgICAgICAgcmV0dXJuIF9yZWdlbmVyYXRvclJ1bnRpbWUoKS53cmFwKGZ1bmN0aW9uIF9jYWxsZWUyJChfY29udGV4dDIpIHsKICAgICAgICAgIHdoaWxlICgxKSBzd2l0Y2ggKF9jb250ZXh0Mi5wcmV2ID0gX2NvbnRleHQyLm5leHQpIHsKICAgICAgICAgICAgY2FzZSAwOgogICAgICAgICAgICAgIF90aGlzMy5wdWxsaW5nID0gdHJ1ZTsKICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDE7CiAgICAgICAgICAgICAgX2NvbnRleHQyLm5leHQgPSA0OwogICAgICAgICAgICAgIHJldHVybiBjb2xsZWN0aW9uU3RvcmFnZS5wdWxsRnJvbVNlcnZlcihfdGhpczMuJGh0dHApOwogICAgICAgICAgICBjYXNlIDQ6CiAgICAgICAgICAgICAgcmVzdWx0ID0gX2NvbnRleHQyLnNlbnQ7CiAgICAgICAgICAgICAgaWYgKHJlc3VsdC5zdWNjZXNzKSB7CiAgICAgICAgICAgICAgICBfdGhpczMuJG1lc3NhZ2Uuc3VjY2VzcyhyZXN1bHQubWVzc2FnZSk7CiAgICAgICAgICAgICAgICBfdGhpczMubG9hZExvY2FsQ29sbGVjdGlvbnMoKTsgLy8g6YeN5paw5Yqg6L295pys5Zyw5pWw5o2uCiAgICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcihyZXN1bHQubWVzc2FnZSk7CiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIF90aGlzMy5zeW5jU3RhdHVzID0gY29sbGVjdGlvblN0b3JhZ2UuZ2V0U3luY1N0YXR1cygpOwogICAgICAgICAgICAgIF9jb250ZXh0Mi5uZXh0ID0gMTM7CiAgICAgICAgICAgICAgYnJlYWs7CiAgICAgICAgICAgIGNhc2UgOToKICAgICAgICAgICAgICBfY29udGV4dDIucHJldiA9IDk7CiAgICAgICAgICAgICAgX2NvbnRleHQyLnQwID0gX2NvbnRleHQyWyJjYXRjaCJdKDEpOwogICAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+aLieWPluaVsOaNruWksei0pTonLCBfY29udGV4dDIudDApOwogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcign5ouJ5Y+W5pWw5o2u5aSx6LSlOiAnICsgX2NvbnRleHQyLnQwLm1lc3NhZ2UpOwogICAgICAgICAgICBjYXNlIDEzOgogICAgICAgICAgICAgIF9jb250ZXh0Mi5wcmV2ID0gMTM7CiAgICAgICAgICAgICAgX3RoaXMzLnB1bGxpbmcgPSBmYWxzZTsKICAgICAgICAgICAgICByZXR1cm4gX2NvbnRleHQyLmZpbmlzaCgxMyk7CiAgICAgICAgICAgIGNhc2UgMTY6CiAgICAgICAgICAgIGNhc2UgImVuZCI6CiAgICAgICAgICAgICAgcmV0dXJuIF9jb250ZXh0Mi5zdG9wKCk7CiAgICAgICAgICB9CiAgICAgICAgfSwgX2NhbGxlZTIsIG51bGwsIFtbMSwgOSwgMTMsIDE2XV0pOwogICAgICB9KSkoKTsKICAgIH0sCiAgICAvLyDojrflj5blkIzmraXnirbmgIHnsbvlnosKICAgIGdldFN5bmNTdGF0dXNUeXBlOiBmdW5jdGlvbiBnZXRTeW5jU3RhdHVzVHlwZSgpIHsKICAgICAgc3dpdGNoICh0aGlzLnN5bmNTdGF0dXMuc3RhdHVzKSB7CiAgICAgICAgY2FzZSAncGVuZGluZyc6CiAgICAgICAgICByZXR1cm4gJ3dhcm5pbmcnOwogICAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6CiAgICAgICAgICByZXR1cm4gJ3N1Y2Nlc3MnOwogICAgICAgIGNhc2UgJ2ZhaWxlZCc6CiAgICAgICAgICByZXR1cm4gJ2Rhbmdlcic7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHJldHVybiAnaW5mbyc7CiAgICAgIH0KICAgIH0sCiAgICAvLyDojrflj5blkIzmraXnirbmgIHmlofmnKwKICAgIGdldFN5bmNTdGF0dXNUZXh0OiBmdW5jdGlvbiBnZXRTeW5jU3RhdHVzVGV4dCgpIHsKICAgICAgc3dpdGNoICh0aGlzLnN5bmNTdGF0dXMuc3RhdHVzKSB7CiAgICAgICAgY2FzZSAncGVuZGluZyc6CiAgICAgICAgICByZXR1cm4gJ+W+heWQjOatpSc7CiAgICAgICAgY2FzZSAnY29tcGxldGVkJzoKICAgICAgICAgIHJldHVybiAn5bey5ZCM5q2lJzsKICAgICAgICBjYXNlICdmYWlsZWQnOgogICAgICAgICAgcmV0dXJuICflkIzmraXlpLHotKUnOwogICAgICAgIGNhc2UgJ25ldmVyJzoKICAgICAgICAgIHJldHVybiAn5LuO5pyq5ZCM5q2lJzsKICAgICAgICBkZWZhdWx0OgogICAgICAgICAgcmV0dXJuICfmnKrnn6XnirbmgIEnOwogICAgICB9CiAgICB9LAogICAgLy8g5Yqg6L2957uf6K6h5pWw5o2uCiAgICBsb2FkU3RhdHM6IGZ1bmN0aW9uIGxvYWRTdGF0cygpIHsKICAgICAgLy8g5qih5ouf57uf6K6h5pWw5o2u77yM5a6e6ZmF5bqU6K+l5LuO5ZCO56uv6I635Y+WCiAgICAgIHRoaXMudG90YWxDb2xsZWN0aW9ucyA9IDE1NjsKICAgICAgdGhpcy50b2RheUNvbGxlY3Rpb25zID0gMTI7CiAgICAgIHRoaXMuYWN0aXZlVXNlcnMgPSA4OTsKICAgICAgdGhpcy5wb3B1bGFyVmVudWVzID0gMjM7CiAgICB9LAogICAgLy8g5pCc57SiCiAgICBoYW5kbGVTZWFyY2g6IGZ1bmN0aW9uIGhhbmRsZVNlYXJjaCgpIHsKICAgICAgdGhpcy5jdXJyZW50UGFnZSA9IDE7CiAgICAgIHRoaXMubG9hZENvbGxlY3Rpb25zKCk7CiAgICB9LAogICAgLy8g6YeN572u5pCc57SiCiAgICBoYW5kbGVSZXNldDogZnVuY3Rpb24gaGFuZGxlUmVzZXQoKSB7CiAgICAgIHRoaXMuc2VhcmNoRm9ybSA9IHsKICAgICAgICBjaGFuZ2RpTmFtZTogJycsCiAgICAgICAgeW9uZ2h1TmFtZTogJycsCiAgICAgICAgZGF0ZVJhbmdlOiBbXQogICAgICB9OwogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5sb2FkQ29sbGVjdGlvbnMoKTsKICAgIH0sCiAgICAvLyDpgInmi6nlj5jljJYKICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnNlbGVjdGVkQ29sbGVjdGlvbnMgPSBzZWxlY3Rpb247CiAgICB9LAogICAgLy8g5om56YeP5Yig6ZmkCiAgICBoYW5kbGVCYXRjaERlbGV0ZTogZnVuY3Rpb24gaGFuZGxlQmF0Y2hEZWxldGUoKSB7CiAgICAgIHZhciBfdGhpczQgPSB0aGlzOwogICAgICBpZiAodGhpcy5zZWxlY3RlZENvbGxlY3Rpb25zLmxlbmd0aCA9PT0gMCkgewogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn6K+36YCJ5oup6KaB5Yig6Zmk55qE5pS26JeP6K6w5b2VJyk7CiAgICAgICAgcmV0dXJuOwogICAgICB9CiAgICAgIHRoaXMuJGNvbmZpcm0oIlx1Nzg2RVx1NUI5QVx1ODk4MVx1NTIyMFx1OTY2NFx1OTAwOVx1NEUyRFx1NzY4NCAiLmNvbmNhdCh0aGlzLnNlbGVjdGVkQ29sbGVjdGlvbnMubGVuZ3RoLCAiIFx1Njc2MVx1NjUzNlx1ODVDRlx1OEJCMFx1NUY1NVx1NTQxN1x1RkYxRiIpLCAn56Gu6K6k5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgdmFyIGlkcyA9IF90aGlzNC5zZWxlY3RlZENvbGxlY3Rpb25zLm1hcChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgcmV0dXJuIGl0ZW0uaWQ7CiAgICAgICAgfSk7CiAgICAgICAgX3RoaXM0LmRlbGV0ZUNvbGxlY3Rpb25zKGlkcyk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWNleS4quWIoOmZpAogICAgaGFuZGxlRGVsZXRlOiBmdW5jdGlvbiBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIHZhciBfdGhpczUgPSB0aGlzOwogICAgICB0aGlzLiRjb25maXJtKCfnoa7lrpropoHliKDpmaTov5nmnaHmlLbol4/orrDlvZXlkJfvvJ8nLCAn56Gu6K6k5Yig6ZmkJywgewogICAgICAgIGNvbmZpcm1CdXR0b25UZXh0OiAn56Gu5a6aJywKICAgICAgICBjYW5jZWxCdXR0b25UZXh0OiAn5Y+W5raIJywKICAgICAgICB0eXBlOiAnd2FybmluZycKICAgICAgfSkudGhlbihmdW5jdGlvbiAoKSB7CiAgICAgICAgX3RoaXM1LmRlbGV0ZUNvbGxlY3Rpb25zKFtyb3cuaWRdKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yig6Zmk5pS26JePCiAgICBkZWxldGVDb2xsZWN0aW9uczogZnVuY3Rpb24gZGVsZXRlQ29sbGVjdGlvbnMoaWRzKSB7CiAgICAgIHZhciBfdGhpczYgPSB0aGlzOwogICAgICB0aGlzLiRodHRwKHsKICAgICAgICB1cmw6ICdjaGFuZ2RpQ29sbGVjdGlvbi9kZWxldGUnLAogICAgICAgIG1ldGhvZDogJ3Bvc3QnLAogICAgICAgIGRhdGE6IGlkcwogICAgICB9KS50aGVuKGZ1bmN0aW9uIChfcmVmMikgewogICAgICAgIHZhciBkYXRhID0gX3JlZjIuZGF0YTsKICAgICAgICBpZiAoZGF0YSAmJiBkYXRhLmNvZGUgPT09IDApIHsKICAgICAgICAgIF90aGlzNi4kbWVzc2FnZS5zdWNjZXNzKCfliKDpmaTmiJDlip8nKTsKICAgICAgICAgIF90aGlzNi5sb2FkQ29sbGVjdGlvbnMoKTsKICAgICAgICAgIF90aGlzNi5zZWxlY3RlZENvbGxlY3Rpb25zID0gW107CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIF90aGlzNi4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyB8fCAn5Yig6Zmk5aSx6LSlJyk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfliKDpmaTlpLHotKU6JywgZXJyb3IpOwogICAgICAgIF90aGlzNi4kbWVzc2FnZS5lcnJvcign5Yig6Zmk5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOafpeeci+ivpuaDhQogICAgdmlld0RldGFpbHM6IGZ1bmN0aW9uIHZpZXdEZXRhaWxzKHJvdykgewogICAgICB0aGlzLmN1cnJlbnRDb2xsZWN0aW9uID0gcm93OwogICAgICB0aGlzLmRldGFpbERpYWxvZ1Zpc2libGUgPSB0cnVlOwogICAgfSwKICAgIC8vIOWvvOWHuuaVsOaNrgogICAgZXhwb3J0Q29sbGVjdGlvbnM6IGZ1bmN0aW9uIGV4cG9ydENvbGxlY3Rpb25zKCkgewogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+WvvOWHuuWKn+iDveW8gOWPkeS4rS4uLicpOwogICAgfSwKICAgIC8vIOWIhumhteWkp+Wwj+WPmOWMlgogICAgaGFuZGxlU2l6ZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2l6ZUNoYW5nZShzaXplKSB7CiAgICAgIHRoaXMucGFnZVNpemUgPSBzaXplOwogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5sb2FkQ29sbGVjdGlvbnMoKTsKICAgIH0sCiAgICAvLyDlvZPliY3pobXlj5jljJYKICAgIGhhbmRsZUN1cnJlbnRDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZUN1cnJlbnRDaGFuZ2UocGFnZSkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gcGFnZTsKICAgICAgdGhpcy5sb2FkQ29sbGVjdGlvbnMoKTsKICAgIH0sCiAgICAvLyDojrflj5bmlLbol4/nsbvlnovmoIfnrb4KICAgIGdldENvbGxlY3Rpb25UeXBlVGFnOiBmdW5jdGlvbiBnZXRDb2xsZWN0aW9uVHlwZVRhZyh0eXBlKSB7CiAgICAgIHN3aXRjaCAodHlwZSkgewogICAgICAgIGNhc2UgMToKICAgICAgICAgIHJldHVybiAnc3VjY2Vzcyc7CiAgICAgICAgY2FzZSAyOgogICAgICAgICAgcmV0dXJuICd3YXJuaW5nJzsKICAgICAgICBjYXNlIDM6CiAgICAgICAgICByZXR1cm4gJ2Rhbmdlcic7CiAgICAgICAgZGVmYXVsdDoKICAgICAgICAgIHJldHVybiAnaW5mbyc7CiAgICAgIH0KICAgIH0sCiAgICAvLyDmoLzlvI/ljJbml6XmnJ8KICAgIGZvcm1hdERhdGU6IGZ1bmN0aW9uIGZvcm1hdERhdGUoZGF0ZSkgewogICAgICBpZiAoIWRhdGUpIHJldHVybiAnLSc7CiAgICAgIHJldHVybiBuZXcgRGF0ZShkYXRlKS50b0xvY2FsZVN0cmluZygnemgtQ04nKTsKICAgIH0sCiAgICAvLyDlm77niYfplJnor6/lpITnkIYKICAgIGhhbmRsZUltYWdlRXJyb3I6IGZ1bmN0aW9uIGhhbmRsZUltYWdlRXJyb3IoZXZlbnQpIHsKICAgICAgZXZlbnQudGFyZ2V0LnNyYyA9ICcvc3RhdGljL2ltYWdlcy9kZWZhdWx0LXZlbnVlLnBuZyc7CiAgICB9CiAgfQp9Ow=="}, {"version": 3, "names": ["collectionStorage", "name", "data", "loading", "collectionList", "selectedCollections", "currentPage", "pageSize", "totalCount", "totalCollections", "todayCollections", "activeUsers", "popularVenues", "searchForm", "changdiName", "yo<PERSON><PERSON><PERSON><PERSON>", "date<PERSON><PERSON><PERSON>", "detailDialogVisible", "currentCollection", "syncing", "pulling", "syncStatus", "status", "lastSync", "mounted", "loadCollections", "loadStats", "loadLocalCollections", "getSyncStatus", "methods", "_this", "params", "page", "limit", "length", "insertTimeStart", "insertTimeEnd", "$http", "url", "method", "then", "_ref", "code", "list", "total", "$message", "error", "msg", "catch", "console", "localCollections", "getAllCollections", "info", "concat", "syncToServer", "_this2", "_asyncToGenerator", "_regeneratorRuntime", "mark", "_callee", "result", "wrap", "_callee$", "_context", "prev", "next", "sent", "success", "message", "t0", "finish", "stop", "pullFromServer", "_this3", "_callee2", "_callee2$", "_context2", "getSyncStatusType", "getSyncStatusText", "handleSearch", "handleReset", "handleSelectionChange", "selection", "handleBatchDelete", "_this4", "warning", "$confirm", "confirmButtonText", "cancelButtonText", "type", "ids", "map", "item", "id", "deleteCollections", "handleDelete", "row", "_this5", "_this6", "_ref2", "viewDetails", "exportCollections", "handleSizeChange", "size", "handleCurrentChange", "getCollectionTypeTag", "formatDate", "date", "Date", "toLocaleString", "handleImageError", "event", "target", "src"], "sources": ["src/views/collection-management.vue"], "sourcesContent": ["<template>\n  <div class=\"collection-management\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">\n        <i class=\"el-icon-star-on\"></i>\n        收藏管理\n      </h1>\n      <p class=\"page-description\">管理用户的场地收藏记录，查看收藏统计和趋势</p>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon total\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ totalCollections }}</h3>\n              <p>总收藏数</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon today\">\n              <i class=\"el-icon-date\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ todayCollections }}</h3>\n              <p>今日新增</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon users\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ activeUsers }}</h3>\n              <p>活跃用户</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon venues\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ popularVenues }}</h3>\n              <p>热门场地</p>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"search-section\">\n      <el-card>\n        <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n          <el-form-item label=\"场地名称\">\n            <el-input\n              v-model=\"searchForm.changdiName\"\n              placeholder=\"请输入场地名称\"\n              clearable\n              @keyup.enter.native=\"handleSearch\">\n            </el-input>\n          </el-form-item>\n          <el-form-item label=\"用户姓名\">\n            <el-input\n              v-model=\"searchForm.yonghuName\"\n              placeholder=\"请输入用户姓名\"\n              clearable\n              @keyup.enter.native=\"handleSearch\">\n            </el-input>\n          </el-form-item>\n          <el-form-item label=\"收藏时间\">\n            <el-date-picker\n              v-model=\"searchForm.dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              format=\"yyyy-MM-dd\"\n              value-format=\"yyyy-MM-dd\">\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleSearch\" icon=\"el-icon-search\">搜索</el-button>\n            <el-button @click=\"handleReset\" icon=\"el-icon-refresh\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-card>\n    </div>\n\n    <!-- 操作栏 -->\n    <div class=\"action-bar\">\n      <div class=\"action-left\">\n        <el-button\n          type=\"danger\"\n          :disabled=\"selectedCollections.length === 0\"\n          @click=\"handleBatchDelete\"\n          icon=\"el-icon-delete\">\n          批量删除 ({{ selectedCollections.length }})\n        </el-button>\n        <el-button\n          type=\"success\"\n          @click=\"exportCollections\"\n          icon=\"el-icon-download\">\n          导出数据\n        </el-button>\n        <el-button\n          type=\"primary\"\n          @click=\"syncToServer\"\n          :loading=\"syncing\"\n          icon=\"el-icon-upload2\">\n          同步到服务器\n        </el-button>\n        <el-button\n          type=\"info\"\n          @click=\"pullFromServer\"\n          :loading=\"pulling\"\n          icon=\"el-icon-download\">\n          从服务器拉取\n        </el-button>\n      </div>\n      <div class=\"action-right\">\n        <el-tag v-if=\"syncStatus.status !== 'never'\" :type=\"getSyncStatusType()\">\n          {{ getSyncStatusText() }}\n        </el-tag>\n        <el-button @click=\"loadCollections\" icon=\"el-icon-refresh\">刷新</el-button>\n      </div>\n    </div>\n\n    <!-- 收藏列表 -->\n    <div class=\"collection-table\">\n      <el-table\n        v-loading=\"loading\"\n        :data=\"collectionList\"\n        @selection-change=\"handleSelectionChange\"\n        stripe\n        style=\"width: 100%\">\n        \n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        \n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\"></el-table-column>\n        \n        <el-table-column label=\"场地信息\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"venue-info\">\n              <img \n                v-if=\"scope.row.changdiPhoto\" \n                :src=\"scope.row.changdiPhoto.split(',')[0]\" \n                class=\"venue-image\"\n                @error=\"handleImageError\">\n              <div class=\"venue-details\">\n                <h4>{{ scope.row.changdiName }}</h4>\n                <p class=\"venue-type\">{{ scope.row.changdiValue }}</p>\n                <p class=\"venue-price\">¥{{ scope.row.changdiNewMoney }}</p>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"用户信息\" min-width=\"150\">\n          <template slot-scope=\"scope\">\n            <div class=\"user-info\">\n              <p class=\"user-name\">{{ scope.row.yonghuName }}</p>\n              <p class=\"user-phone\">{{ scope.row.yonghuPhone }}</p>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"changdiCollectionValue\" label=\"收藏类型\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getCollectionTypeTag(scope.row.changdiCollectionTypes)\">\n              {{ scope.row.changdiCollectionValue }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"insertTime\" label=\"收藏时间\" width=\"180\">\n          <template slot-scope=\"scope\">\n            {{ formatDate(scope.row.insertTime) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"viewDetails(scope.row)\"\n              icon=\"el-icon-view\">\n              查看\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"danger\"\n              @click=\"handleDelete(scope.row)\"\n              icon=\"el-icon-delete\">\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-wrapper\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50, 100]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 详情对话框 -->\n    <el-dialog\n      title=\"收藏详情\"\n      :visible.sync=\"detailDialogVisible\"\n      width=\"600px\">\n      <div v-if=\"currentCollection\" class=\"collection-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"收藏ID\">{{ currentCollection.id }}</el-descriptions-item>\n          <el-descriptions-item label=\"场地名称\">{{ currentCollection.changdiName }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户姓名\">{{ currentCollection.yonghuName }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户电话\">{{ currentCollection.yonghuPhone }}</el-descriptions-item>\n          <el-descriptions-item label=\"收藏类型\">{{ currentCollection.changdiCollectionValue }}</el-descriptions-item>\n          <el-descriptions-item label=\"收藏时间\">{{ formatDate(currentCollection.insertTime) }}</el-descriptions-item>\n        </el-descriptions>\n        \n        <div class=\"venue-preview\" v-if=\"currentCollection.changdiPhoto\">\n          <h4>场地图片</h4>\n          <div class=\"image-gallery\">\n            <img \n              v-for=\"(image, index) in currentCollection.changdiPhoto.split(',')\" \n              :key=\"index\"\n              :src=\"image\" \n              class=\"preview-image\"\n              @error=\"handleImageError\">\n          </div>\n        </div>\n      </div>\n      \n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport collectionStorage from '@/utils/collection-storage'\n\nexport default {\n  name: 'CollectionManagement',\n  data() {\n    return {\n      loading: false,\n      collectionList: [],\n      selectedCollections: [],\n      currentPage: 1,\n      pageSize: 20,\n      totalCount: 0,\n      \n      // 统计数据\n      totalCollections: 0,\n      todayCollections: 0,\n      activeUsers: 0,\n      popularVenues: 0,\n      \n      // 搜索表单\n      searchForm: {\n        changdiName: '',\n        yonghuName: '',\n        dateRange: []\n      },\n      \n      // 详情对话框\n      detailDialogVisible: false,\n      currentCollection: null,\n\n      // 同步状态\n      syncing: false,\n      pulling: false,\n      syncStatus: { status: 'never', lastSync: null }\n    }\n  },\n  \n  mounted() {\n    this.loadCollections()\n    this.loadStats()\n    this.loadLocalCollections()\n    this.syncStatus = collectionStorage.getSyncStatus()\n  },\n  \n  methods: {\n    // 加载收藏列表\n    loadCollections() {\n      this.loading = true\n      \n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize\n      }\n      \n      // 添加搜索条件\n      if (this.searchForm.changdiName) {\n        params.changdiName = this.searchForm.changdiName\n      }\n      if (this.searchForm.yonghuName) {\n        params.yonghuName = this.searchForm.yonghuName\n      }\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.insertTimeStart = this.searchForm.dateRange[0]\n        params.insertTimeEnd = this.searchForm.dateRange[1]\n      }\n      \n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        this.loading = false\n        if (data && data.code === 0) {\n          this.collectionList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        } else {\n          this.$message.error(data.msg || '获取收藏列表失败')\n        }\n      }).catch((error) => {\n        this.loading = false\n        console.error('加载收藏列表失败:', error)\n        // 加载本地收藏数据作为备用\n        this.loadLocalCollections()\n      })\n    },\n\n    // 加载本地收藏数据\n    loadLocalCollections() {\n      try {\n        const localCollections = collectionStorage.getAllCollections()\n        if (localCollections.length > 0 && this.collectionList.length === 0) {\n          this.collectionList = localCollections\n          this.totalCount = localCollections.length\n          this.$message.info(`已加载本地收藏数据 (${localCollections.length} 条)`)\n        }\n      } catch (error) {\n        console.error('加载本地收藏数据失败:', error)\n      }\n    },\n\n    // 同步到服务器\n    async syncToServer() {\n      this.syncing = true\n      try {\n        const result = await collectionStorage.syncToServer(this.$http)\n        if (result.success) {\n          this.$message.success(result.message)\n          this.loadCollections() // 重新加载数据\n        } else {\n          this.$message.error(result.message)\n        }\n        this.syncStatus = collectionStorage.getSyncStatus()\n      } catch (error) {\n        console.error('同步失败:', error)\n        this.$message.error('同步失败: ' + error.message)\n      } finally {\n        this.syncing = false\n      }\n    },\n\n    // 从服务器拉取数据\n    async pullFromServer() {\n      this.pulling = true\n      try {\n        const result = await collectionStorage.pullFromServer(this.$http)\n        if (result.success) {\n          this.$message.success(result.message)\n          this.loadLocalCollections() // 重新加载本地数据\n        } else {\n          this.$message.error(result.message)\n        }\n        this.syncStatus = collectionStorage.getSyncStatus()\n      } catch (error) {\n        console.error('拉取数据失败:', error)\n        this.$message.error('拉取数据失败: ' + error.message)\n      } finally {\n        this.pulling = false\n      }\n    },\n\n    // 获取同步状态类型\n    getSyncStatusType() {\n      switch (this.syncStatus.status) {\n        case 'pending': return 'warning'\n        case 'completed': return 'success'\n        case 'failed': return 'danger'\n        default: return 'info'\n      }\n    },\n\n    // 获取同步状态文本\n    getSyncStatusText() {\n      switch (this.syncStatus.status) {\n        case 'pending': return '待同步'\n        case 'completed': return '已同步'\n        case 'failed': return '同步失败'\n        case 'never': return '从未同步'\n        default: return '未知状态'\n      }\n    },\n\n    // 加载统计数据\n    loadStats() {\n      // 模拟统计数据，实际应该从后端获取\n      this.totalCollections = 156\n      this.todayCollections = 12\n      this.activeUsers = 89\n      this.popularVenues = 23\n    },\n\n    // 搜索\n    handleSearch() {\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        changdiName: '',\n        yonghuName: '',\n        dateRange: []\n      }\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 选择变化\n    handleSelectionChange(selection) {\n      this.selectedCollections = selection\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedCollections.length === 0) {\n        this.$message.warning('请选择要删除的收藏记录')\n        return\n      }\n\n      this.$confirm(`确定要删除选中的 ${this.selectedCollections.length} 条收藏记录吗？`, '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const ids = this.selectedCollections.map(item => item.id)\n        this.deleteCollections(ids)\n      })\n    },\n\n    // 单个删除\n    handleDelete(row) {\n      this.$confirm('确定要删除这条收藏记录吗？', '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.deleteCollections([row.id])\n      })\n    },\n\n    // 删除收藏\n    deleteCollections(ids) {\n      this.$http({\n        url: 'changdiCollection/delete',\n        method: 'post',\n        data: ids\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.$message.success('删除成功')\n          this.loadCollections()\n          this.selectedCollections = []\n        } else {\n          this.$message.error(data.msg || '删除失败')\n        }\n      }).catch((error) => {\n        console.error('删除失败:', error)\n        this.$message.error('删除失败，请稍后重试')\n      })\n    },\n\n    // 查看详情\n    viewDetails(row) {\n      this.currentCollection = row\n      this.detailDialogVisible = true\n    },\n\n    // 导出数据\n    exportCollections() {\n      this.$message.info('导出功能开发中...')\n    },\n\n    // 分页大小变化\n    handleSizeChange(size) {\n      this.pageSize = size\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 当前页变化\n    handleCurrentChange(page) {\n      this.currentPage = page\n      this.loadCollections()\n    },\n\n    // 获取收藏类型标签\n    getCollectionTypeTag(type) {\n      switch (type) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 3: return 'danger'\n        default: return 'info'\n      }\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '-'\n      return new Date(date).toLocaleString('zh-CN')\n    },\n\n    // 图片错误处理\n    handleImageError(event) {\n      event.target.src = '/static/images/default-venue.png'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collection-management {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-title {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n}\n\n.page-title i {\n  margin-right: 12px;\n  color: #409eff;\n}\n\n.page-description {\n  color: #909399;\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  margin-bottom: 24px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  transition: transform 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.stat-icon i {\n  font-size: 24px;\n  color: white;\n}\n\n.stat-icon.total {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.stat-icon.today {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.stat-icon.users {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.stat-icon.venues {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.stat-content h3 {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.stat-content p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 搜索区域 */\n.search-section {\n  margin-bottom: 20px;\n}\n\n.search-form {\n  margin: 0;\n}\n\n/* 操作栏 */\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 16px 20px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* 表格区域 */\n.collection-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* 场地信息 */\n.venue-info {\n  display: flex;\n  align-items: center;\n}\n\n.venue-image {\n  width: 60px;\n  height: 60px;\n  border-radius: 8px;\n  object-fit: cover;\n  margin-right: 12px;\n  border: 1px solid #ebeef5;\n}\n\n.venue-details h4 {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.venue-type {\n  margin: 0 0 4px 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n.venue-price {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #f56c6c;\n}\n\n/* 用户信息 */\n.user-info .user-name {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.user-info .user-phone {\n  margin: 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 分页 */\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: center;\n}\n\n/* 详情对话框 */\n.collection-detail {\n  padding: 20px 0;\n}\n\n.venue-preview {\n  margin-top: 20px;\n}\n\n.venue-preview h4 {\n  margin: 0 0 12px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.image-gallery {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.preview-image {\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  object-fit: cover;\n  border: 1px solid #ebeef5;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.preview-image:hover {\n  transform: scale(1.05);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .collection-management {\n    padding: 12px;\n  }\n\n  .stats-cards .el-col {\n    margin-bottom: 12px;\n  }\n\n  .action-bar {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .venue-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .venue-image {\n    margin-bottom: 8px;\n  }\n}\n</style>\n"], "mappings": ";;;AAsQA,OAAAA,iBAAA;AAEA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,cAAA;MACAC,mBAAA;MACAC,WAAA;MACAC,QAAA;MACAC,UAAA;MAEA;MACAC,gBAAA;MACAC,gBAAA;MACAC,WAAA;MACAC,aAAA;MAEA;MACAC,UAAA;QACAC,WAAA;QACAC,UAAA;QACAC,SAAA;MACA;MAEA;MACAC,mBAAA;MACAC,iBAAA;MAEA;MACAC,OAAA;MACAC,OAAA;MACAC,UAAA;QAAAC,MAAA;QAAAC,QAAA;MAAA;IACA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAAC,eAAA;IACA,KAAAC,SAAA;IACA,KAAAC,oBAAA;IACA,KAAAN,UAAA,GAAArB,iBAAA,CAAA4B,aAAA;EACA;EAEAC,OAAA;IACA;IACAJ,eAAA,WAAAA,gBAAA;MAAA,IAAAK,KAAA;MACA,KAAA3B,OAAA;MAEA,IAAA4B,MAAA;QACAC,IAAA,OAAA1B,WAAA;QACA2B,KAAA,OAAA1B;MACA;;MAEA;MACA,SAAAM,UAAA,CAAAC,WAAA;QACAiB,MAAA,CAAAjB,WAAA,QAAAD,UAAA,CAAAC,WAAA;MACA;MACA,SAAAD,UAAA,CAAAE,UAAA;QACAgB,MAAA,CAAAhB,UAAA,QAAAF,UAAA,CAAAE,UAAA;MACA;MACA,SAAAF,UAAA,CAAAG,SAAA,SAAAH,UAAA,CAAAG,SAAA,CAAAkB,MAAA;QACAH,MAAA,CAAAI,eAAA,QAAAtB,UAAA,CAAAG,SAAA;QACAe,MAAA,CAAAK,aAAA,QAAAvB,UAAA,CAAAG,SAAA;MACA;MAEA,KAAAqB,KAAA;QACAC,GAAA;QACAC,MAAA;QACAR,MAAA,EAAAA;MACA,GAAAS,IAAA,WAAAC,IAAA;QAAA,IAAAvC,IAAA,GAAAuC,IAAA,CAAAvC,IAAA;QACA4B,KAAA,CAAA3B,OAAA;QACA,IAAAD,IAAA,IAAAA,IAAA,CAAAwC,IAAA;UACAZ,KAAA,CAAA1B,cAAA,GAAAF,IAAA,CAAAA,IAAA,CAAAyC,IAAA;UACAb,KAAA,CAAAtB,UAAA,GAAAN,IAAA,CAAAA,IAAA,CAAA0C,KAAA;QACA;UACAd,KAAA,CAAAe,QAAA,CAAAC,KAAA,CAAA5C,IAAA,CAAA6C,GAAA;QACA;MACA,GAAAC,KAAA,WAAAF,KAAA;QACAhB,KAAA,CAAA3B,OAAA;QACA8C,OAAA,CAAAH,KAAA,cAAAA,KAAA;QACA;QACAhB,KAAA,CAAAH,oBAAA;MACA;IACA;IAEA;IACAA,oBAAA,WAAAA,qBAAA;MACA;QACA,IAAAuB,gBAAA,GAAAlD,iBAAA,CAAAmD,iBAAA;QACA,IAAAD,gBAAA,CAAAhB,MAAA,aAAA9B,cAAA,CAAA8B,MAAA;UACA,KAAA9B,cAAA,GAAA8C,gBAAA;UACA,KAAA1C,UAAA,GAAA0C,gBAAA,CAAAhB,MAAA;UACA,KAAAW,QAAA,CAAAO,IAAA,4DAAAC,MAAA,CAAAH,gBAAA,CAAAhB,MAAA;QACA;MACA,SAAAY,KAAA;QACAG,OAAA,CAAAH,KAAA,gBAAAA,KAAA;MACA;IACA;IAEA;IACAQ,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MAAA,OAAAC,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAC,QAAA;QAAA,IAAAC,MAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAC,SAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,IAAA,GAAAD,QAAA,CAAAE,IAAA;YAAA;cACAV,MAAA,CAAApC,OAAA;cAAA4C,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAE,IAAA;cAAA,OAEAjE,iBAAA,CAAAsD,YAAA,CAAAC,MAAA,CAAAlB,KAAA;YAAA;cAAAuB,MAAA,GAAAG,QAAA,CAAAG,IAAA;cACA,IAAAN,MAAA,CAAAO,OAAA;gBACAZ,MAAA,CAAAV,QAAA,CAAAsB,OAAA,CAAAP,MAAA,CAAAQ,OAAA;gBACAb,MAAA,CAAA9B,eAAA;cACA;gBACA8B,MAAA,CAAAV,QAAA,CAAAC,KAAA,CAAAc,MAAA,CAAAQ,OAAA;cACA;cACAb,MAAA,CAAAlC,UAAA,GAAArB,iBAAA,CAAA4B,aAAA;cAAAmC,QAAA,CAAAE,IAAA;cAAA;YAAA;cAAAF,QAAA,CAAAC,IAAA;cAAAD,QAAA,CAAAM,EAAA,GAAAN,QAAA;cAEAd,OAAA,CAAAH,KAAA,UAAAiB,QAAA,CAAAM,EAAA;cACAd,MAAA,CAAAV,QAAA,CAAAC,KAAA,YAAAiB,QAAA,CAAAM,EAAA,CAAAD,OAAA;YAAA;cAAAL,QAAA,CAAAC,IAAA;cAEAT,MAAA,CAAApC,OAAA;cAAA,OAAA4C,QAAA,CAAAO,MAAA;YAAA;YAAA;cAAA,OAAAP,QAAA,CAAAQ,IAAA;UAAA;QAAA,GAAAZ,OAAA;MAAA;IAEA;IAEA;IACAa,cAAA,WAAAA,eAAA;MAAA,IAAAC,MAAA;MAAA,OAAAjB,iBAAA,eAAAC,mBAAA,GAAAC,IAAA,UAAAgB,SAAA;QAAA,IAAAd,MAAA;QAAA,OAAAH,mBAAA,GAAAI,IAAA,UAAAc,UAAAC,SAAA;UAAA,kBAAAA,SAAA,CAAAZ,IAAA,GAAAY,SAAA,CAAAX,IAAA;YAAA;cACAQ,MAAA,CAAArD,OAAA;cAAAwD,SAAA,CAAAZ,IAAA;cAAAY,SAAA,CAAAX,IAAA;cAAA,OAEAjE,iBAAA,CAAAwE,cAAA,CAAAC,MAAA,CAAApC,KAAA;YAAA;cAAAuB,MAAA,GAAAgB,SAAA,CAAAV,IAAA;cACA,IAAAN,MAAA,CAAAO,OAAA;gBACAM,MAAA,CAAA5B,QAAA,CAAAsB,OAAA,CAAAP,MAAA,CAAAQ,OAAA;gBACAK,MAAA,CAAA9C,oBAAA;cACA;gBACA8C,MAAA,CAAA5B,QAAA,CAAAC,KAAA,CAAAc,MAAA,CAAAQ,OAAA;cACA;cACAK,MAAA,CAAApD,UAAA,GAAArB,iBAAA,CAAA4B,aAAA;cAAAgD,SAAA,CAAAX,IAAA;cAAA;YAAA;cAAAW,SAAA,CAAAZ,IAAA;cAAAY,SAAA,CAAAP,EAAA,GAAAO,SAAA;cAEA3B,OAAA,CAAAH,KAAA,YAAA8B,SAAA,CAAAP,EAAA;cACAI,MAAA,CAAA5B,QAAA,CAAAC,KAAA,cAAA8B,SAAA,CAAAP,EAAA,CAAAD,OAAA;YAAA;cAAAQ,SAAA,CAAAZ,IAAA;cAEAS,MAAA,CAAArD,OAAA;cAAA,OAAAwD,SAAA,CAAAN,MAAA;YAAA;YAAA;cAAA,OAAAM,SAAA,CAAAL,IAAA;UAAA;QAAA,GAAAG,QAAA;MAAA;IAEA;IAEA;IACAG,iBAAA,WAAAA,kBAAA;MACA,aAAAxD,UAAA,CAAAC,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAwD,iBAAA,WAAAA,kBAAA;MACA,aAAAzD,UAAA,CAAAC,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAI,SAAA,WAAAA,UAAA;MACA;MACA,KAAAjB,gBAAA;MACA,KAAAC,gBAAA;MACA,KAAAC,WAAA;MACA,KAAAC,aAAA;IACA;IAEA;IACAmE,YAAA,WAAAA,aAAA;MACA,KAAAzE,WAAA;MACA,KAAAmB,eAAA;IACA;IAEA;IACAuD,WAAA,WAAAA,YAAA;MACA,KAAAnE,UAAA;QACAC,WAAA;QACAC,UAAA;QACAC,SAAA;MACA;MACA,KAAAV,WAAA;MACA,KAAAmB,eAAA;IACA;IAEA;IACAwD,qBAAA,WAAAA,sBAAAC,SAAA;MACA,KAAA7E,mBAAA,GAAA6E,SAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,MAAA;MACA,SAAA/E,mBAAA,CAAA6B,MAAA;QACA,KAAAW,QAAA,CAAAwC,OAAA;QACA;MACA;MAEA,KAAAC,QAAA,qDAAAjC,MAAA,MAAAhD,mBAAA,CAAA6B,MAAA;QACAqD,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAjD,IAAA;QACA,IAAAkD,GAAA,GAAAN,MAAA,CAAA/E,mBAAA,CAAAsF,GAAA,WAAAC,IAAA;UAAA,OAAAA,IAAA,CAAAC,EAAA;QAAA;QACAT,MAAA,CAAAU,iBAAA,CAAAJ,GAAA;MACA;IACA;IAEA;IACAK,YAAA,WAAAA,aAAAC,GAAA;MAAA,IAAAC,MAAA;MACA,KAAAX,QAAA;QACAC,iBAAA;QACAC,gBAAA;QACAC,IAAA;MACA,GAAAjD,IAAA;QACAyD,MAAA,CAAAH,iBAAA,EAAAE,GAAA,CAAAH,EAAA;MACA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAAJ,GAAA;MAAA,IAAAQ,MAAA;MACA,KAAA7D,KAAA;QACAC,GAAA;QACAC,MAAA;QACArC,IAAA,EAAAwF;MACA,GAAAlD,IAAA,WAAA2D,KAAA;QAAA,IAAAjG,IAAA,GAAAiG,KAAA,CAAAjG,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAwC,IAAA;UACAwD,MAAA,CAAArD,QAAA,CAAAsB,OAAA;UACA+B,MAAA,CAAAzE,eAAA;UACAyE,MAAA,CAAA7F,mBAAA;QACA;UACA6F,MAAA,CAAArD,QAAA,CAAAC,KAAA,CAAA5C,IAAA,CAAA6C,GAAA;QACA;MACA,GAAAC,KAAA,WAAAF,KAAA;QACAG,OAAA,CAAAH,KAAA,UAAAA,KAAA;QACAoD,MAAA,CAAArD,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAsD,WAAA,WAAAA,YAAAJ,GAAA;MACA,KAAA9E,iBAAA,GAAA8E,GAAA;MACA,KAAA/E,mBAAA;IACA;IAEA;IACAoF,iBAAA,WAAAA,kBAAA;MACA,KAAAxD,QAAA,CAAAO,IAAA;IACA;IAEA;IACAkD,gBAAA,WAAAA,iBAAAC,IAAA;MACA,KAAAhG,QAAA,GAAAgG,IAAA;MACA,KAAAjG,WAAA;MACA,KAAAmB,eAAA;IACA;IAEA;IACA+E,mBAAA,WAAAA,oBAAAxE,IAAA;MACA,KAAA1B,WAAA,GAAA0B,IAAA;MACA,KAAAP,eAAA;IACA;IAEA;IACAgF,oBAAA,WAAAA,qBAAAhB,IAAA;MACA,QAAAA,IAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IAEA;IACAiB,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,WAAAC,IAAA,CAAAD,IAAA,EAAAE,cAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACAA,KAAA,CAAAC,MAAA,CAAAC,GAAA;IACA;EACA;AACA", "ignoreList": []}]}