{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue", "mtime": 1750589903927}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQpleHBvcnQgZGVmYXVsdCB7DQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIGFjdGlvbnM6IFsNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5Zy65Zyw566h55CGJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+euoeeQhuS9k+iCsuWcuuWcsOS/oeaBrycsDQogICAgICAgICAgaWNvbjogJ2VsLWljb24tb2ZmaWNlLWJ1aWxkaW5nJywNCiAgICAgICAgICByb3V0ZTogJy9jaGFuZ2RpJw0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgdGl0bGU6ICfpooTnuqbnrqHnkIYnLA0KICAgICAgICAgIGRlc2NyaXB0aW9uOiAn5p+l55yL5ZKM566h55CG6aKE57qm6K6i5Y2VJywNCiAgICAgICAgICBpY29uOiAnZWwtaWNvbi1kYXRlJywNCiAgICAgICAgICByb3V0ZTogJy9jaGFuZ2RpT3JkZXInDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICB0aXRsZTogJ+eUqOaIt+euoeeQhicsDQogICAgICAgICAgZGVzY3JpcHRpb246ICfnrqHnkIbns7vnu5/nlKjmiLcnLA0KICAgICAgICAgIGljb246ICdlbC1pY29uLXVzZXInLA0KICAgICAgICAgIHJvdXRlOiAnL3lvbmdodScNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn5YWs5ZGK566h55CGJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+WPkeW4g+WSjOeuoeeQhuWFrOWRiicsDQogICAgICAgICAgaWNvbjogJ2VsLWljb24tYmVsbCcsDQogICAgICAgICAgcm91dGU6ICcvZ29uZ2dhbycNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIHRpdGxlOiAn6K665Z2b566h55CGJywNCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+euoeeQhuiuuuWdm+WGheWuuScsDQogICAgICAgICAgaWNvbjogJ2VsLWljb24tY2hhdC1kb3Qtcm91bmQnLA0KICAgICAgICAgIHJvdXRlOiAnL2ZvcnVtJw0KICAgICAgICB9DQogICAgICBdDQogICAgfQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgaGFuZGxlQWN0aW9uKGFjdGlvbikgew0KICAgICAgdGhpcy4kcm91dGVyLnB1c2goYWN0aW9uLnJvdXRlKQ0KICAgIH0NCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["HomeCard.vue"], "names": [], "mappings": ";AAiBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HomeCard.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\r\n  <div class=\"quick-actions\">\r\n    <div class=\"action-item\" v-for=\"(action, index) in actions\" :key=\"index\" @click=\"handleAction(action)\">\r\n      <div class=\"action-icon\">\r\n        <i :class=\"action.icon\"></i>\r\n      </div>\r\n      <div class=\"action-content\">\r\n        <div class=\"action-title\">{{ action.title }}</div>\r\n        <div class=\"action-desc\">{{ action.description }}</div>\r\n      </div>\r\n      <div class=\"action-arrow\">\r\n        <i class=\"el-icon-arrow-right\"></i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      actions: [\r\n        {\r\n          title: '场地管理',\r\n          description: '管理体育场地信息',\r\n          icon: 'el-icon-office-building',\r\n          route: '/changdi'\r\n        },\r\n        {\r\n          title: '预约管理',\r\n          description: '查看和管理预约订单',\r\n          icon: 'el-icon-date',\r\n          route: '/changdiOrder'\r\n        },\r\n        {\r\n          title: '用户管理',\r\n          description: '管理系统用户',\r\n          icon: 'el-icon-user',\r\n          route: '/yonghu'\r\n        },\r\n        {\r\n          title: '公告管理',\r\n          description: '发布和管理公告',\r\n          icon: 'el-icon-bell',\r\n          route: '/gonggao'\r\n        },\r\n        {\r\n          title: '论坛管理',\r\n          description: '管理论坛内容',\r\n          icon: 'el-icon-chat-dot-round',\r\n          route: '/forum'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    handleAction(action) {\r\n      this.$router.push(action.route)\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.box-card {\r\n  margin-right: 10px;\r\n  .header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n  .content {\r\n    font-size: 30px;\r\n    font-weight: bold;\r\n    color: #666;\r\n    text-align: center;\r\n    .unit {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n  .bottom {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}