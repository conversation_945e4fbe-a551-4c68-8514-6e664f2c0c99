<!DOCTYPE html>
<html>
<head lang="en">
    <meta charset="utf-8">
    <title>首页</title>
    <meta name="description" content=""/>
    <meta name="keywords" content=""/>
    <meta name="author" content="order by mobanxiu.cn"/>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <link href="https://cdn.bootcdn.net/ajax/libs/Swiper/5.4.5/css/swiper.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../../xznstatic/css/common.css"/>
    <link rel="stylesheet" href="../../xznstatic/css/style.css"/>
    <script src="../../xznstatic/js/jquery-1.11.3.min.js"></script>
    <script src="../../xznstatic/js/jquery.SuperSlide.2.1.1.js"></script>

    <link rel="stylesheet" href="../../css/theme.css"/>
</head>
<style>
    html::after {
        position: fixed;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        content: '';
        display: block;
        background-attachment: fixed;
        background-size: cover;
        background-position: center;
    }

    #test1 {
        overflow: hidden;
    }

    #test1 .layui-carousel-ind li {
        width: 20px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0, 0, 0, .3);
        border-radius: 6px;
        background-color: #f7f7f7;
        box-shadow: 0 0 6px rgba(255, 0, 0, .8);
    }

    #test1 .layui-carousel-ind li.layui-this {
        width: 30px;
        height: 10px;
        border-width: 0;
        border-style: solid;
        border-color: rgba(0, 0, 0, .3);
        border-radius: 6px;
    }

    .recommend {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }

    .recommend .box {
        width: 1014px;
    }

    .recommend .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .recommend .box .title span {
        padding: 0 10px;
        line-height: 1.4;
    }

    .recommend .box .list {
        display: flex;
        flex-wrap: wrap;
    }

    .index-pv1 .box .list .list-item {
        flex: 0 0 ${var1}%;
        padding: 0 5px;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item-body {
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 5px;
        box-sizing: border-box;
        cursor: pointer;
    }

    .recommend .box .list img {
        width: 100%;
        height: 100px;
        display: block;
        margin: 0 auto;
        object-fit: cover;
        max-width: 100%;
    }

    .recommend .box .list .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        text-align: center;
        box-sizing: border-box;
    }

    .recommend .box .list .list-item1 {
        flex: 0 0 100%;
    }

    .recommend .box .list .list-item2 {
        flex: 0 0 50%;
    }

    .recommend .box .list .list-item3 {
        flex: 0 0 33.33%;
    }

    .recommend .box .list .list-item4 {
        flex: 0 0 25%;
    }

    .recommend .box .list .list-item5 {
        flex: 0 0 20%;
    }

    /* 商品推荐-样式4-开始 */
    .recommend .list-4 {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .recommend .list-4 .list-4-body {
        display: flex;
        flex-direction: column;
    }

    .recommend .list-4 .list-4-item {
        position: relative;
        z-index: 1;
    }

    .recommend .list-4 .list-4-item.item-1 {
        width: 400px;
        height: 400px;
        margin-right: 20px;
    }

    .recommend .list-4 .list-4-item.item-2 {
        width: 220px;
        height: 190px;
        margin-right: 20px;
        margin-bottom: 20px;
    }

    .recommend .list-4 .list-4-item.item-3 {
        width: 220px;
        height: 190px;
        margin-right: 20px;
        margin-bottom: 0;
    }

    .recommend .list-4 .list-4-item.item-4 {
        width: 190px;
        height: 190px;
        margin-right: 0;
        margin-bottom: 20px;
    }

    .recommend .list-4 .list-4-item.item-5 {
        width: 190px;
        height: 190px;
        margin-right: 0;
        margin-bottom: 0;
    }

    .recommend .list-4 .list-4-item img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        display: block;
    }

    .recommend .list-4 .list-4-item .list-4-item-center {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: auto;
        display: flex;
        flex-wrap: wrap;
        background-color: rgba(0, 0, 0, .3);
    }

    .recommend .list-4 .list-4-item .list-4-item-center .list-4-item-title {
        width: 50%;
        text-align: left;
        line-height: 44px;
        color: #fff;
        font-size: 14px;
        padding: 0 6px;
    }

    .recommend .list-4 .list-4-item .list-4-item-center .list-4-item-price {
        width: 50%;
        text-align: right;
        line-height: 44px;
        color: #fff;
        font-size: 14px;
        padding: 0 6px;
    }

    /* 商品推荐-样式4-结束 */
    /* 商品推荐-样式5-开始 */
    .recommend #recommend-five-swiper.swiper-container-horizontal > .swiper-pagination-bullets {
        line-height: 1;
    }

    .recommend #recommend-five-swiper .swiper-slide.swiper-slide-prev {
        z-index: 5;
    }

    .recommend #recommend-five-swiper .swiper-slide.swiper-slide-next {
        z-index: 5;
    }

    .recommend #recommend-five-swiper .swiper-slide.swiper-slide-active {
        z-index: 9;
    }

    .recommend #lists-five-swiper.swiper-container-horizontal > .swiper-pagination-bullets {
        line-height: 1;
    }

    .recommend #lists-five-swiper .swiper-slide.swiper-slide-prev {
        z-index: 5;
    }

    .recommend #lists-five-swiper .swiper-slide.swiper-slide-next {
        z-index: 5;
    }

    .recommend #lists-five-swiper .swiper-slide.swiper-slide-active {
        z-index: 9;
    }

    /* 商品推荐-样式5-结束 */

    .news {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
        width: 100%;
    }

    .news .box {
        width: 1014px;
    }

    .news .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .news .box .title span {
        padding: 0 10px;
        line-height: 1.4;
    }

    .news .box .list {
        display: flex;
        flex-wrap: wrap;
    }

    .index-pv2 .box .list .list-item {
        flex: 0 0 33%;
        padding: 0 10px;
        box-sizing: border-box;
    }

    .news .box .list .list-item .list-item-body {
        border: 1px solid rgba(0, 0, 0, 3);
        padding: 10px;
        box-sizing: border-box;
        display: flex;
        cursor: pointer;
    }

    .news .box .list .list-item .list-item-body img {
        width: 120px;
        height: 100%;
        display: block;
        margin: 0 auto;
        object-fit: cover;
        max-width: 100%;
    }

    .news .box .list .list-item .list-item-body .item-info {
        flex: 1;
        display: flex;
        justify-content: space-between;
        flex-direction: column;
        padding-left: 10px;
        box-sizing: border-box;
    }

    .news .box .list .list-item .list-item-body .item-info .name {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .news .box .list .list-item .list-item-body .item-info .time {
        padding-top: 5px;
        color: red;
        font-size: 14px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }

    .news .box .list .list-item1 {
        flex: 0 0 100%;
    }

    .news .box .list .list-item2 {
        flex: 0 0 50%;
    }

    .news .box .list .list-item3 {
        flex: 0 0 33.33%;
    }

    .lists {
        padding: 10px 0;
        display: flex;
        justify-content: center;
        background-repeat: no-repeat;
        background-position: center center;
        background-size: cover;
    }

    .lists .box {
        width: 1014px;
        overflow: hidden;
    }

    .lists .box .title {
        padding: 10px 5px;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
    }

    .lists .box .title span {
        padding: 0 10px;
        line-height: 1.4;
    }

    .lists .box .swiper-slide {
        box-sizing: border-box;
        cursor: pointer;
    }

    .lists .box .swiper-slide .img-box {
        width: 100%;
        overflow: hidden;
    }

    .lists .box .swiper-slide .img-box img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        max-width: 100%;
    }

    .index-pv1 .animation-box:hover {
        transform: perspective(10px) translate3d(-10px, -10px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
        transition: all 0.3s;
        z-index: 2;
        position: relative;
    }

    .index-pv2 .animation-box:hover {
        transform: perspective(10px) translate3d(-10px, -10px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
        transition: all 0.3s;
        z-index: 2;
        position: relative;
    }

    .index-pv3 .animation-box:hover {
        transform: perspective(10px) translate3d(-10px, -10px, 0px) scale(1) rotate(0deg) skew(0deg, 0deg);
        transition: all 0.3s;
        z-index: 2;
        position: relative;
    }

    #new-list-6 .swiper-wrapper {
        -webkit-transition-timing-function: linear;
        -moz-transition-timing-function: linear;
        -ms-transition-timing-function: linear;
        -o-transition-timing-function: linear;
        transition-timing-function: linear;
    }
</style>
<body>
<div id="app">
    <div class="banner">
        <div class="layui-carousel" id="test1" style="width: 100%; height: 450px;">
            <div carousel-item>
                <div v-for="(item,index) in swiperList" :key="index">
                    <img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" :alt="item.name"/>
                </div>
            </div>
        </div>
    </div>


    <div id="content">
        <div class="recommend index-pv3"
             :style='{"padding":"10px 0 10px 0","boxShadow":"0 0 0px rgba(255,0,0,.8)","margin":"10px 0 0 0","borderColor":"rgba(0,0,0,.3)","backgroundColor":"rgba(255, 0, 0, 0)","borderRadius":"4px","borderWidth":"0","borderStyle":"solid"}'>
            <div class="box" style='width:80%'>
                <div class="title main_backgroundColor"
                     :style='{"padding":"10px 0 10px 10px","boxShadow":"0 0 5px rgba(2, 93, 172, 1)","margin":"10px 0","color":"rgba(255, 255, 255, 1)","borderRadius":"20px","alignItems":"center","borderWidth":"0","fontSize":"16px","borderStyle":"solid"}'>
                    <span>DATA SHOW</span>
                    <span>场地展示</span>
                </div>

                <div class="list">
                    <div v-for="(item,index) in changdiList" :key="index"
                         @click="jump('../changdi/detail.html?id='+item.id)" class="list-item"
                         :class="4=='3'?'list-item3':4=='5'?'list-item5':''">
                        <div class="list-item-body animation-box"
                             :style='{"padding":"0px 0 6px 0","boxShadow":"","margin":"20px 4px","borderColor":"rgba(0,0,0,.3)","backgroundColor":"#fff","borderRadius":"4px","borderWidth":"0","borderStyle":"solid"}'>
                            <img class="sub_borderColor"
                                 :style='{"borderColor":"rgba(0,0,0,.3)","borderRadius":"100%","borderWidth":"0","width":"220px","borderStyle":"solid","height":"220px"}'
                                 :src="item.changdiPhoto?item.changdiPhoto.split(',')[0]:''" alt=""/>
                            <div v-if='true'
                                 :style='{"isshow":true,"padding":"2px","margin":"8px 0 0 0","backgroundColor":"rgba(255, 255, 255, 0.2)","color":"rgba(51, 51, 51, 1)","borderRadius":"0 0 6px 6px","textAlign":"center","fontSize":"20px"}'
                                 class="name">{{item.changdiName}}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="clear"></div>
                <div style="text-align: center;">
                    <button @click="jump('../changdi/list.html')" style="display: block;cursor: pointer;" type="button"
                            :style='{"padding":"0 15px","boxShadow":"0 0 6px rgba(255,0,0,0)","margin":"4px auto","borderColor":"#ccc","backgroundColor":"#fff","color":"rgba(160, 67, 26, 1)","borderRadius":"6px","borderWidth":"0","width":"auto","fontSize":"14px","borderStyle":"solid","height":"34px"}'>
                        查看更多<i v-if="true"
                                   :style='{"isshow":true,"padding":"0 0 0 1px","fontSize":"14px","color":"rgba(160, 67, 26, 1)"}'
                                   class="layui-icon layui-icon-next"></i></button>
                </div>
            </div>
        </div>


    </div>
</div>

<script src="../../layui/layui.js"></script>
<script src="https://cdn.bootcdn.net/ajax/libs/Swiper/5.4.5/js/swiper.min.js"></script>
<script src="../../js/vue.js"></script>
<!-- 引入element组件库 -->
<script src="../../xznstatic/js/element.min.js"></script>
<!-- 引入element样式 -->
<link rel="stylesheet" href="../../xznstatic/css/element.min.css">

<!-- 轮播图自定义样式 -->
<style>
    .banner {
        position: relative;
        width: 100%;
        height: 450px;
        overflow: hidden;
    }

    .carousel-item {
        position: relative;
        width: 100%;
        height: 100%;
    }

    .carousel-item img {
        opacity: 1;
        transition: opacity 0.3s ease;
    }

    .carousel-title {
        position: absolute;
        bottom: 20px;
        left: 20px;
        background: rgba(0, 0, 0, 0.6);
        color: white;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: 16px;
        font-weight: bold;
    }

    .carousel-loading {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 450px;
        background: #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
    }

    .loading-content {
        text-align: center;
        color: #666;
    }

    .loading-content i {
        font-size: 32px;
        margin-bottom: 10px;
        display: block;
    }

    .loading-content p {
        margin: 0;
        font-size: 14px;
    }

    /* 轮播图指示器样式优化 */
    .layui-carousel[lay-filter="test1"] .layui-carousel-ind {
        background: rgba(255, 255, 255, 0.5);
    }

    .layui-carousel[lay-filter="test1"] .layui-carousel-ind.layui-this {
        background: #fff;
    }

    /* 轮播图箭头样式优化 */
    .layui-carousel[lay-filter="test1"] .layui-carousel-arrow {
        background: rgba(0, 0, 0, 0.3);
        color: #fff;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        line-height: 40px;
        font-size: 18px;
    }

    .layui-carousel[lay-filter="test1"] .layui-carousel-arrow:hover {
        background: rgba(0, 0, 0, 0.6);
    }
</style>
<script src="../../js/config.js"></script>
<script src="../../modules/config.js"></script>
<script src="../../js/utils.js"></script>
<script type="text/javascript">
    var vue = new Vue({
        el: '#app',
        data: {
            swiperList: [{
                img: '../../xznstatic/img/1.jpg',
                name: '轮播图1'
            }, {
                img: '../../xznstatic/img/2.jpg',
                name: '轮播图2'
            }, {
                img: '../../xznstatic/img/3.jpg',
                name: '轮播图3'
            }],
            dianyingRecommend: [],
            changdiList: [],
            gonggaoList: [],
        },
        filters: {
            newsDesc: function (val) {
                if (val) {
                    val = val.replace(/<[^<>]+>/g, '').replace(/undefined/g, '');
                    if (val.length > 60) {
                        val = val.substring(0, 60);
                    }

                    return val;
                }
                return '';
            }
        },
        methods: {
            jump(url) {
                jump(url)
            },

            // 处理图片加载错误
            handleImageError(event) {
                console.log('图片加载失败:', event.target.src);
                // 设置默认图片
                event.target.src = '../../xznstatic/img/1.jpg';
                event.target.onerror = null; // 防止无限循环
            },

            // 处理图片加载成功
            handleImageLoad(event) {
                console.log('图片加载成功:', event.target.src);
                event.target.style.opacity = '1';
                event.target.style.display = 'block';
            },

            // 刷新轮播图
            refreshCarousel() {
                this.loadCarouselData();
            },

            // 加载轮播图数据
            loadCarouselData() {
                // 这个方法会在下面的layui.use中定义
                if (window.loadCarouselData) {
                    window.loadCarouselData();
                }
            }
        },

        mounted() {
            // 初始化轮播图
            this.$nextTick(() => {
                layui.use('carousel', function() {
                    var carousel = layui.carousel;
                    carousel.render({
                        elem: '#test1',
                        width: '100%',
                        height: '450px',
                        arrow: 'hover',
                        anim: 'default',
                        autoplay: true,
                        interval: 3000,
                        indicator: 'inside'
                    });
                });
            });
        }
    });

    layui.use(['layer', 'form', 'element', 'carousel', 'http', 'jquery'], function () {
        var layer = layui.layer;
        var element = layui.element;
        var form = layui.form;
        var carousel = layui.carousel;
        var http = layui.http;
        var jquery = layui.jquery;

        // 获取轮播图数据 - 使用fetch绕过登录验证
        fetch('http://localhost:8080/tiyuguan/config/list?page=1&limit=10', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(res => {
            console.log('轮播图API响应:', res);

            if (res && res.data && res.data.list && res.data.list.length > 0) {
                let swiperList = [];
                res.data.list.forEach(element => {
                    if (element.value != null && element.value.trim() !== '') {
                        swiperList.push({
                            img: element.value,
                            name: element.name || '轮播图'
                        });
                    }
                });

                console.log('轮播图列表:', swiperList);
                vue.swiperList = swiperList;

                // 等待DOM更新后初始化轮播
                setTimeout(() => {
                    carousel.render({
                        elem: '#test1',
                        width: '100%',
                        height: '450px',
                        arrow: 'hover',
                        anim: 'default',
                        autoplay: true,
                        interval: 3000,
                        indicator: 'inside'
                    });
                    console.log('轮播图初始化完成');
                }, 100);
            } else {
                console.log('没有轮播图数据，使用默认图片');
                // 默认图片已在Vue data中设置
                setTimeout(() => {
                    carousel.render({
                        elem: '#test1',
                        width: '100%',
                        height: '450px',
                        arrow: 'hover',
                        anim: 'default',
                        autoplay: true,
                        interval: 3000,
                        indicator: 'inside'
                    });
                }, 100);
            }
        })
        .catch(error => {
            console.error('获取轮播图数据失败:', error);
            // 使用默认图片（已在Vue data中设置）
            setTimeout(() => {
                carousel.render({
                    elem: '#test1',
                    width: '100%',
                    height: '450px',
                    arrow: 'hover',
                    anim: 'default',
                    autoplay: true,
                    interval: 3000,
                    indicator: 'inside'
                });
            }, 100);
        });

        http.request('changdi/list', 'get', {
            page: 1,
            limit: 8,
            shangxiaTypes: 1,
            changdiDelete: 1,
        }, function (res) {
            vue.changdiList = res.data.list;

        });
        http.request('gonggao/list', 'get', {
            page: 1,
            limit: 8,
        }, function (res) {
            vue.gonggaoList = res.data.list;

        });


    });

    window.xznSlide = function () {
        // jQuery(".banner").slide({mainCell:".bd ul",autoPlay:true,interTime:5000});
        jQuery("#ifocus").slide({
            titCell: "#ifocus_btn li",
            mainCell: "#ifocus_piclist ul",
            effect: "leftLoop",
            delayTime: 200,
            autoPlay: true,
            triggerTime: 0
        });
        jQuery("#ifocus").slide({titCell: "#ifocus_btn li", mainCell: "#ifocus_tx ul", delayTime: 0, autoPlay: true});
        jQuery(".product_list").slide({
            mainCell: ".bd ul",
            autoPage: true,
            effect: "leftLoop",
            autoPlay: true,
            vis: 5,
            trigger: "click",
            interTime: 4000
        });
    };
</script>
<script src="../../xznstatic/js/index.js"></script>
</body>
</html>
