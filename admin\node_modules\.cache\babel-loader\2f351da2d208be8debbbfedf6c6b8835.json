{"remainingRequest":"D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue?vue&type=script&lang=js","dependencies":[{"path":"D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue","mtime":1750602713137},{"path":"D:\\1\\tiyuguan\\admin\\babel.config.js","mtime":1642386765000},{"path":"D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js","mtime":499162500000},{"path":"D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js","mtime":456789000000},{"path":"D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js","mtime":499162500000},{"path":"D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js","mtime":499162500000}],"contextDependencies":[],"result":[{"type":"Buffer","data":"base64: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"},{"version":3,"names":["name","data","totalCollections","todayIncrease","popularVenues","activeCollectors","avgCollectionsPerUser","trendPeriod","topVenues","id","type","photo","collections","timeDistribution","time","percentage","detailData","venueName","venueType","todayCollections","weekCollections","conversionRate","avgRating","mounted","initCharts","loadAnalyticsData","methods","_this","$nextTick","initTrendChart","initTypeChart","console","log","loadTrendData","getRankingClass","index","exportData","$message","info","refreshData","success","handleImageError","event","target","src"],"sources":["src/views/collection-analytics.vue"],"sourcesContent":["<template>\n  <div class=\"collection-analytics\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">\n        <i class=\"el-icon-data-analysis\"></i>\n        收藏数据分析\n      </h1>\n      <p class=\"page-description\">分析用户收藏行为，了解热门场地和收藏趋势</p>\n    </div>\n\n    <!-- 核心指标卡片 -->\n    <div class=\"metrics-section\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"metric-card primary\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ totalCollections }}</h3>\n              <p>总收藏数</p>\n              <span class=\"metric-trend up\">\n                <i class=\"el-icon-top\"></i>\n                +{{ todayIncrease }}\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card success\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ popularVenues }}</h3>\n              <p>热门场地数</p>\n              <span class=\"metric-trend\">\n                收藏>10次\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card warning\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ activeCollectors }}</h3>\n              <p>活跃收藏用户</p>\n              <span class=\"metric-trend\">\n                本月活跃\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card info\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-pie-chart\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ avgCollectionsPerUser.toFixed(1) }}</h3>\n              <p>人均收藏数</p>\n              <span class=\"metric-trend\">\n                平均值\n              </span>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 图表区域 -->\n    <div class=\"charts-section\">\n      <el-row :gutter=\"20\">\n        <!-- 收藏趋势图 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>收藏趋势</span>\n              <el-select v-model=\"trendPeriod\" size=\"small\" @change=\"loadTrendData\">\n                <el-option label=\"最近7天\" value=\"7days\"></el-option>\n                <el-option label=\"最近30天\" value=\"30days\"></el-option>\n                <el-option label=\"最近3个月\" value=\"3months\"></el-option>\n              </el-select>\n            </div>\n            <div id=\"trendChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n        \n        <!-- 场地类型分布 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>场地类型收藏分布</span>\n            </div>\n            <div id=\"typeChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n        <!-- 热门场地排行 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>热门场地排行</span>\n            </div>\n            <div class=\"ranking-list\">\n              <div \n                v-for=\"(venue, index) in topVenues\" \n                :key=\"venue.id\"\n                class=\"ranking-item\"\n                :class=\"{ 'top-three': index < 3 }\">\n                <div class=\"ranking-number\" :class=\"getRankingClass(index)\">\n                  {{ index + 1 }}\n                </div>\n                <div class=\"venue-info\">\n                  <img \n                    v-if=\"venue.photo\" \n                    :src=\"venue.photo.split(',')[0]\" \n                    class=\"venue-thumb\"\n                    @error=\"handleImageError\">\n                  <div class=\"venue-details\">\n                    <h4>{{ venue.name }}</h4>\n                    <p>{{ venue.type }}</p>\n                  </div>\n                </div>\n                <div class=\"collection-count\">\n                  <span class=\"count\">{{ venue.collections }}</span>\n                  <span class=\"label\">收藏</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <!-- 用户收藏行为分析 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>用户收藏行为</span>\n            </div>\n            <div class=\"behavior-stats\">\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏高峰时段</div>\n                <div class=\"behavior-value\">14:00 - 16:00</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">平均收藏间隔</div>\n                <div class=\"behavior-value\">2.3天</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">取消收藏率</div>\n                <div class=\"behavior-value\">12.5%</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏转预约率</div>\n                <div class=\"behavior-value\">35.8%</div>\n              </div>\n            </div>\n            \n            <div class=\"time-distribution\">\n              <h4>收藏时间分布</h4>\n              <div class=\"time-bars\">\n                <div \n                  v-for=\"hour in timeDistribution\" \n                  :key=\"hour.time\"\n                  class=\"time-bar\">\n                  <div class=\"bar\" :style=\"{ height: hour.percentage + '%' }\"></div>\n                  <span class=\"time-label\">{{ hour.time }}</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 详细数据表格 -->\n    <div class=\"data-table-section\">\n      <el-card>\n        <div slot=\"header\" class=\"table-header\">\n          <span>收藏详细数据</span>\n          <div class=\"table-actions\">\n            <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\n              导出数据\n            </el-button>\n            <el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n        \n        <el-table :data=\"detailData\" stripe style=\"width: 100%\">\n          <el-table-column prop=\"venueName\" label=\"场地名称\" min-width=\"150\"></el-table-column>\n          <el-table-column prop=\"venueType\" label=\"场地类型\" width=\"120\"></el-table-column>\n          <el-table-column prop=\"totalCollections\" label=\"总收藏数\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"todayCollections\" label=\"今日新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"weekCollections\" label=\"本周新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"conversionRate\" label=\"转预约率\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              {{ scope.row.conversionRate }}%\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"avgRating\" label=\"平均评分\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              <el-rate \n                v-model=\"scope.row.avgRating\" \n                disabled \n                show-score \n                text-color=\"#ff9900\"\n                score-template=\"{value}\">\n              </el-rate>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CollectionAnalytics',\n  data() {\n    return {\n      // 核心指标\n      totalCollections: 1256,\n      todayIncrease: 23,\n      popularVenues: 15,\n      activeCollectors: 89,\n      avgCollectionsPerUser: 4.2,\n      \n      // 图表配置\n      trendPeriod: '7days',\n      \n      // 热门场地数据\n      topVenues: [\n        {\n          id: 1,\n          name: '中央体育馆篮球场',\n          type: '篮球场',\n          photo: '/static/images/basketball.jpg',\n          collections: 156\n        },\n        {\n          id: 2,\n          name: '游泳馆标准池',\n          type: '游泳池',\n          photo: '/static/images/swimming.jpg',\n          collections: 134\n        },\n        {\n          id: 3,\n          name: '网球场A区',\n          type: '网球场',\n          photo: '/static/images/tennis.jpg',\n          collections: 98\n        },\n        {\n          id: 4,\n          name: '羽毛球馆1号场',\n          type: '羽毛球场',\n          photo: '/static/images/badminton.jpg',\n          collections: 87\n        },\n        {\n          id: 5,\n          name: '足球场草坪',\n          type: '足球场',\n          photo: '/static/images/football.jpg',\n          collections: 76\n        }\n      ],\n      \n      // 时间分布数据\n      timeDistribution: [\n        { time: '6:00', percentage: 5 },\n        { time: '8:00', percentage: 15 },\n        { time: '10:00', percentage: 25 },\n        { time: '12:00', percentage: 35 },\n        { time: '14:00', percentage: 85 },\n        { time: '16:00', percentage: 90 },\n        { time: '18:00', percentage: 70 },\n        { time: '20:00', percentage: 45 },\n        { time: '22:00', percentage: 20 }\n      ],\n      \n      // 详细数据\n      detailData: [\n        {\n          venueName: '中央体育馆篮球场',\n          venueType: '篮球场',\n          totalCollections: 156,\n          todayCollections: 8,\n          weekCollections: 23,\n          conversionRate: 42.3,\n          avgRating: 4.5\n        },\n        {\n          venueName: '游泳馆标准池',\n          venueType: '游泳池',\n          totalCollections: 134,\n          todayCollections: 5,\n          weekCollections: 18,\n          conversionRate: 38.7,\n          avgRating: 4.3\n        },\n        {\n          venueName: '网球场A区',\n          venueType: '网球场',\n          totalCollections: 98,\n          todayCollections: 3,\n          weekCollections: 12,\n          conversionRate: 35.2,\n          avgRating: 4.2\n        }\n      ]\n    }\n  },\n  \n  mounted() {\n    this.initCharts()\n    this.loadAnalyticsData()\n  },\n  \n  methods: {\n    // 初始化图表\n    initCharts() {\n      this.$nextTick(() => {\n        this.initTrendChart()\n        this.initTypeChart()\n      })\n    },\n    \n    // 初始化趋势图\n    initTrendChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化收藏趋势图')\n    },\n    \n    // 初始化类型分布图\n    initTypeChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化场地类型分布图')\n    },\n    \n    // 加载趋势数据\n    loadTrendData() {\n      console.log('加载趋势数据:', this.trendPeriod)\n    },\n    \n    // 加载分析数据\n    loadAnalyticsData() {\n      // 模拟数据加载\n      console.log('加载收藏分析数据')\n    },\n    \n    // 获取排名样式\n    getRankingClass(index) {\n      if (index === 0) return 'gold'\n      if (index === 1) return 'silver'\n      if (index === 2) return 'bronze'\n      return 'normal'\n    },\n    \n    // 导出数据\n    exportData() {\n      this.$message.info('导出功能开发中...')\n    },\n    \n    // 刷新数据\n    refreshData() {\n      this.loadAnalyticsData()\n      this.$message.success('数据已刷新')\n    },\n    \n    // 图片错误处理\n    handleImageError(event) {\n      event.target.src = '/static/images/default-venue.png'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collection-analytics {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-title {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n}\n\n.page-title i {\n  margin-right: 12px;\n  color: #409eff;\n}\n\n.page-description {\n  color: #909399;\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 核心指标卡片 */\n.metrics-section {\n  margin-bottom: 24px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.metric-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.metric-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n}\n\n.metric-card.primary::before {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success::before {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning::before {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info::before {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.metric-card.primary .metric-icon {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success .metric-icon {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning .metric-icon {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info .metric-icon {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon i {\n  font-size: 24px;\n  color: white;\n}\n\n.metric-content h3 {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.metric-content p {\n  margin: 0 0 8px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.metric-trend {\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 12px;\n  background: #f0f9ff;\n  color: #0369a1;\n}\n\n.metric-trend.up {\n  background: #f0fdf4;\n  color: #166534;\n}\n\n.metric-trend i {\n  margin-right: 2px;\n}\n\n/* 图表区域 */\n.charts-section {\n  margin-bottom: 24px;\n}\n\n.chart-card {\n  height: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border-radius: 12px;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n.chart-container {\n  height: 320px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 排行榜 */\n.ranking-list {\n  max-height: 320px;\n  overflow-y: auto;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n  transition: background-color 0.3s ease;\n}\n\n.ranking-item:hover {\n  background-color: #f8f9fa;\n}\n\n.ranking-item.top-three {\n  background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);\n  border-radius: 8px;\n  margin-bottom: 8px;\n  padding: 16px 12px;\n}\n\n.ranking-number {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  margin-right: 12px;\n  color: white;\n  font-size: 14px;\n}\n\n.ranking-number.gold {\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\n}\n\n.ranking-number.silver {\n  background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);\n}\n\n.ranking-number.bronze {\n  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);\n}\n\n.ranking-number.normal {\n  background: #e5e7eb;\n  color: #6b7280;\n}\n\n.venue-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.venue-thumb {\n  width: 48px;\n  height: 48px;\n  border-radius: 8px;\n  object-fit: cover;\n  margin-right: 12px;\n  border: 1px solid #ebeef5;\n}\n\n.venue-details h4 {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.venue-details p {\n  margin: 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n.collection-count {\n  text-align: center;\n}\n\n.collection-count .count {\n  display: block;\n  font-size: 18px;\n  font-weight: 700;\n  color: #f56c6c;\n}\n\n.collection-count .label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 用户行为分析 */\n.behavior-stats {\n  margin-bottom: 24px;\n}\n\n.behavior-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.behavior-label {\n  color: #606266;\n  font-size: 14px;\n}\n\n.behavior-value {\n  font-weight: 600;\n  color: #303133;\n  font-size: 16px;\n}\n\n.time-distribution h4 {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.time-bars {\n  display: flex;\n  align-items: end;\n  justify-content: space-between;\n  height: 120px;\n  padding: 0 8px;\n}\n\n.time-bar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.bar {\n  width: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2px 2px 0 0;\n  margin-bottom: 8px;\n  min-height: 4px;\n  transition: all 0.3s ease;\n}\n\n.time-bar:hover .bar {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.time-label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 数据表格 */\n.data-table-section {\n  margin-bottom: 24px;\n}\n\n.table-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .collection-analytics {\n    padding: 12px;\n  }\n\n  .metrics-section .el-col {\n    margin-bottom: 12px;\n  }\n\n  .charts-section .el-col {\n    margin-bottom: 20px;\n  }\n\n  .metric-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .metric-icon {\n    margin-right: 0;\n    margin-bottom: 12px;\n  }\n\n  .venue-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .venue-thumb {\n    margin-bottom: 8px;\n  }\n}\n</style>\n"],"mappings":"AAiOA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,qBAAA;MAEA;MACAC,WAAA;MAEA;MACAC,SAAA,GACA;QACAC,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,EACA;MAEA;MACAC,gBAAA,GACA;QAAAC,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,EACA;MAEA;MACAC,UAAA,GACA;QACAC,SAAA;QACAC,SAAA;QACAhB,gBAAA;QACAiB,gBAAA;QACAC,eAAA;QACAC,cAAA;QACAC,SAAA;MACA,GACA;QACAL,SAAA;QACAC,SAAA;QACAhB,gBAAA;QACAiB,gBAAA;QACAC,eAAA;QACAC,cAAA;QACAC,SAAA;MACA,GACA;QACAL,SAAA;QACAC,SAAA;QACAhB,gBAAA;QACAiB,gBAAA;QACAC,eAAA;QACAC,cAAA;QACAC,SAAA;MACA;IAEA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,iBAAA;EACA;EAEAC,OAAA;IACA;IACAF,UAAA,WAAAA,WAAA;MAAA,IAAAG,KAAA;MACA,KAAAC,SAAA;QACAD,KAAA,CAAAE,cAAA;QACAF,KAAA,CAAAG,aAAA;MACA;IACA;IAEA;IACAD,cAAA,WAAAA,eAAA;MACA;MACAE,OAAA,CAAAC,GAAA;IACA;IAEA;IACAF,aAAA,WAAAA,cAAA;MACA;MACAC,OAAA,CAAAC,GAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACAF,OAAA,CAAAC,GAAA,iBAAAzB,WAAA;IACA;IAEA;IACAkB,iBAAA,WAAAA,kBAAA;MACA;MACAM,OAAA,CAAAC,GAAA;IACA;IAEA;IACAE,eAAA,WAAAA,gBAAAC,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAd,iBAAA;MACA,KAAAY,QAAA,CAAAG,OAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACAA,KAAA,CAAAC,MAAA,CAAAC,GAAA;IACA;EACA;AACA","ignoreList":[]}]}A;IACAkB,iBAAA,WAAAA,kBAAA;MACA;MACAM,OAAA,CAAAC,GAAA;IACA;IAEA;IACAE,eAAA,WAAAA,gBAAAC,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAAd,iBAAA;MACA,KAAAY,QAAA,CAAAG,OAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACAA,KAAA,CAAAC,MAAA,CAAAC,GAAA;IACA;EACA;AACA","ignoreList":[]}]}