{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue", "mtime": 1750603257910}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZm9yLWVhY2guanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5hcnJheS5zbGljZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmFycmF5LnNvcnQuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5kYXRlLnRvLWlzby1zdHJpbmcuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LnZhbHVlcy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL3dlYi5kb20tY29sbGVjdGlvbnMuZm9yLWVhY2guanMiOwpleHBvcnQgZGVmYXVsdCB7CiAgbmFtZTogJ0NvbGxlY3Rpb25BbmFseXRpY3MnLAogIGRhdGE6IGZ1bmN0aW9uIGRhdGEoKSB7CiAgICByZXR1cm4gewogICAgICAvLyDmoLjlv4PmjIfmoIcKICAgICAgdG90YWxDb2xsZWN0aW9uczogMTI1NiwKICAgICAgdG9kYXlJbmNyZWFzZTogMjMsCiAgICAgIHBvcHVsYXJWZW51ZXM6IDE1LAogICAgICBhY3RpdmVDb2xsZWN0b3JzOiA4OSwKICAgICAgYXZnQ29sbGVjdGlvbnNQZXJVc2VyOiA0LjIsCiAgICAgIC8vIOWbvuihqOmFjee9rgogICAgICB0cmVuZFBlcmlvZDogJzdkYXlzJywKICAgICAgLy8g54Ot6Zeo5Zy65Zyw5pWw5o2uCiAgICAgIHRvcFZlbnVlczogW3sKICAgICAgICBpZDogMSwKICAgICAgICBuYW1lOiAn5Lit5aSu5L2T6IKy6aaG56+u55CD5Zy6JywKICAgICAgICB0eXBlOiAn56+u55CD5Zy6JywKICAgICAgICBwaG90bzogJy9zdGF0aWMvaW1hZ2VzL2Jhc2tldGJhbGwuanBnJywKICAgICAgICBjb2xsZWN0aW9uczogMTU2CiAgICAgIH0sIHsKICAgICAgICBpZDogMiwKICAgICAgICBuYW1lOiAn5ri45rOz6aaG5qCH5YeG5rGgJywKICAgICAgICB0eXBlOiAn5ri45rOz5rGgJywKICAgICAgICBwaG90bzogJy9zdGF0aWMvaW1hZ2VzL3N3aW1taW5nLmpwZycsCiAgICAgICAgY29sbGVjdGlvbnM6IDEzNAogICAgICB9LCB7CiAgICAgICAgaWQ6IDMsCiAgICAgICAgbmFtZTogJ+e9keeQg+WcukHljLonLAogICAgICAgIHR5cGU6ICfnvZHnkIPlnLonLAogICAgICAgIHBob3RvOiAnL3N0YXRpYy9pbWFnZXMvdGVubmlzLmpwZycsCiAgICAgICAgY29sbGVjdGlvbnM6IDk4CiAgICAgIH0sIHsKICAgICAgICBpZDogNCwKICAgICAgICBuYW1lOiAn57695q+b55CD6aaGMeWPt+WcuicsCiAgICAgICAgdHlwZTogJ+e+veavm+eQg+WcuicsCiAgICAgICAgcGhvdG86ICcvc3RhdGljL2ltYWdlcy9iYWRtaW50b24uanBnJywKICAgICAgICBjb2xsZWN0aW9uczogODcKICAgICAgfSwgewogICAgICAgIGlkOiA1LAogICAgICAgIG5hbWU6ICfotrPnkIPlnLrojYnlnaonLAogICAgICAgIHR5cGU6ICfotrPnkIPlnLonLAogICAgICAgIHBob3RvOiAnL3N0YXRpYy9pbWFnZXMvZm9vdGJhbGwuanBnJywKICAgICAgICBjb2xsZWN0aW9uczogNzYKICAgICAgfV0sCiAgICAgIC8vIOaXtumXtOWIhuW4g+aVsOaNrgogICAgICB0aW1lRGlzdHJpYnV0aW9uOiBbewogICAgICAgIHRpbWU6ICc2OjAwJywKICAgICAgICBwZXJjZW50YWdlOiA1CiAgICAgIH0sIHsKICAgICAgICB0aW1lOiAnODowMCcsCiAgICAgICAgcGVyY2VudGFnZTogMTUKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxMDowMCcsCiAgICAgICAgcGVyY2VudGFnZTogMjUKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxMjowMCcsCiAgICAgICAgcGVyY2VudGFnZTogMzUKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxNDowMCcsCiAgICAgICAgcGVyY2VudGFnZTogODUKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxNjowMCcsCiAgICAgICAgcGVyY2VudGFnZTogOTAKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcxODowMCcsCiAgICAgICAgcGVyY2VudGFnZTogNzAKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcyMDowMCcsCiAgICAgICAgcGVyY2VudGFnZTogNDUKICAgICAgfSwgewogICAgICAgIHRpbWU6ICcyMjowMCcsCiAgICAgICAgcGVyY2VudGFnZTogMjAKICAgICAgfV0sCiAgICAgIC8vIOivpue7huaVsOaNrgogICAgICBkZXRhaWxEYXRhOiBbewogICAgICAgIHZlbnVlTmFtZTogJ+S4reWkruS9k+iCsummhuevrueQg+WcuicsCiAgICAgICAgdmVudWVUeXBlOiAn56+u55CD5Zy6JywKICAgICAgICB0b3RhbENvbGxlY3Rpb25zOiAxNTYsCiAgICAgICAgdG9kYXlDb2xsZWN0aW9uczogOCwKICAgICAgICB3ZWVrQ29sbGVjdGlvbnM6IDIzLAogICAgICAgIGNvbnZlcnNpb25SYXRlOiA0Mi4zLAogICAgICAgIGF2Z1JhdGluZzogNC41CiAgICAgIH0sIHsKICAgICAgICB2ZW51ZU5hbWU6ICfmuLjms7PppobmoIflh4bmsaAnLAogICAgICAgIHZlbnVlVHlwZTogJ+a4uOazs+axoCcsCiAgICAgICAgdG90YWxDb2xsZWN0aW9uczogMTM0LAogICAgICAgIHRvZGF5Q29sbGVjdGlvbnM6IDUsCiAgICAgICAgd2Vla0NvbGxlY3Rpb25zOiAxOCwKICAgICAgICBjb252ZXJzaW9uUmF0ZTogMzguNywKICAgICAgICBhdmdSYXRpbmc6IDQuMwogICAgICB9LCB7CiAgICAgICAgdmVudWVOYW1lOiAn572R55CD5Zy6QeWMuicsCiAgICAgICAgdmVudWVUeXBlOiAn572R55CD5Zy6JywKICAgICAgICB0b3RhbENvbGxlY3Rpb25zOiA5OCwKICAgICAgICB0b2RheUNvbGxlY3Rpb25zOiAzLAogICAgICAgIHdlZWtDb2xsZWN0aW9uczogMTIsCiAgICAgICAgY29udmVyc2lvblJhdGU6IDM1LjIsCiAgICAgICAgYXZnUmF0aW5nOiA0LjIKICAgICAgfV0KICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgdGhpcy5pbml0Q2hhcnRzKCk7CiAgICB0aGlzLmxvYWRBbmFseXRpY3NEYXRhKCk7CiAgfSwKICBtZXRob2RzOiB7CiAgICAvLyDliJ3lp4vljJblm77ooagKICAgIGluaXRDaGFydHM6IGZ1bmN0aW9uIGluaXRDaGFydHMoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHRoaXMuJG5leHRUaWNrKGZ1bmN0aW9uICgpIHsKICAgICAgICBfdGhpcy5pbml0VHJlbmRDaGFydCgpOwogICAgICAgIF90aGlzLmluaXRUeXBlQ2hhcnQoKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g5Yid5aeL5YyW6LaL5Yq/5Zu+CiAgICBpbml0VHJlbmRDaGFydDogZnVuY3Rpb24gaW5pdFRyZW5kQ2hhcnQoKSB7CiAgICAgIC8vIOi/memHjOW6lOivpeS9v+eUqOecn+WunueahOWbvuihqOW6k+WmgkVDaGFydHMKICAgICAgY29uc29sZS5sb2coJ+WIneWni+WMluaUtuiXj+i2i+WKv+WbvicpOwogICAgfSwKICAgIC8vIOWIneWni+WMluexu+Wei+WIhuW4g+WbvgogICAgaW5pdFR5cGVDaGFydDogZnVuY3Rpb24gaW5pdFR5cGVDaGFydCgpIHsKICAgICAgLy8g6L+Z6YeM5bqU6K+l5L2/55So55yf5a6e55qE5Zu+6KGo5bqT5aaCRUNoYXJ0cwogICAgICBjb25zb2xlLmxvZygn5Yid5aeL5YyW5Zy65Zyw57G75Z6L5YiG5biD5Zu+Jyk7CiAgICB9LAogICAgLy8g5Yqg6L296LaL5Yq/5pWw5o2uCiAgICBsb2FkVHJlbmREYXRhOiBmdW5jdGlvbiBsb2FkVHJlbmREYXRhKCkgewogICAgICBjb25zb2xlLmxvZygn5Yqg6L296LaL5Yq/5pWw5o2uOicsIHRoaXMudHJlbmRQZXJpb2QpOwogICAgfSwKICAgIC8vIOWKoOi9veWIhuaekOaVsOaNrgogICAgbG9hZEFuYWx5dGljc0RhdGE6IGZ1bmN0aW9uIGxvYWRBbmFseXRpY3NEYXRhKCkgewogICAgICB0aGlzLmxvYWRDb2xsZWN0aW9uU3RhdHMoKTsKICAgICAgdGhpcy5sb2FkVG9wVmVudWVzKCk7CiAgICAgIHRoaXMubG9hZERldGFpbERhdGEoKTsKICAgIH0sCiAgICAvLyDliqDovb3mlLbol4/nu5/orqEKICAgIGxvYWRDb2xsZWN0aW9uU3RhdHM6IGZ1bmN0aW9uIGxvYWRDb2xsZWN0aW9uU3RhdHMoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICB0aGlzLiRodHRwKHsKICAgICAgICB1cmw6ICdjaGFuZ2RpQ29sbGVjdGlvbi9wYWdlJywKICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgIHBhcmFtczogewogICAgICAgICAgcGFnZTogMSwKICAgICAgICAgIGxpbWl0OiAxCiAgICAgICAgfQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChfcmVmKSB7CiAgICAgICAgdmFyIGRhdGEgPSBfcmVmLmRhdGE7CiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgICBfdGhpczIudG90YWxDb2xsZWN0aW9ucyA9IGRhdGEuZGF0YS50b3RhbCB8fCAwOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bmlLbol4/nu5/orqHlpLHotKUnKTsKICAgICAgfSk7CgogICAgICAvLyDojrflj5bku4rml6XmlLbol4/mlbAKICAgICAgdmFyIHRvZGF5ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07CiAgICAgIHRoaXMuJGh0dHAoewogICAgICAgIHVybDogJ2NoYW5nZGlDb2xsZWN0aW9uL3BhZ2UnLAogICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICBwYWdlOiAxLAogICAgICAgICAgbGltaXQ6IDEwMDAsCiAgICAgICAgICBpbnNlcnRUaW1lU3RhcnQ6IHRvZGF5LAogICAgICAgICAgaW5zZXJ0VGltZUVuZDogdG9kYXkKICAgICAgICB9CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKF9yZWYyKSB7CiAgICAgICAgdmFyIGRhdGEgPSBfcmVmMi5kYXRhOwogICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgewogICAgICAgICAgX3RoaXMyLnRvZGF5SW5jcmVhc2UgPSBkYXRhLmRhdGEudG90YWwgfHwgMDsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBjb25zb2xlLmxvZygn6I635Y+W5LuK5pel5pS26JeP5pWw5aSx6LSlJyk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWKoOi9veeDremXqOWcuuWcsAogICAgbG9hZFRvcFZlbnVlczogZnVuY3Rpb24gbG9hZFRvcFZlbnVlcygpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJGh0dHAoewogICAgICAgIHVybDogJ2NoYW5nZGlDb2xsZWN0aW9uL3BhZ2UnLAogICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICBwYWdlOiAxLAogICAgICAgICAgbGltaXQ6IDEwMAogICAgICAgIH0KICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZjMpIHsKICAgICAgICB2YXIgZGF0YSA9IF9yZWYzLmRhdGE7CiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgICB2YXIgY29sbGVjdGlvbnMgPSBkYXRhLmRhdGEubGlzdCB8fCBbXTsKCiAgICAgICAgICAvLyDmjInlnLrlnLBJROWIhue7hOe7n+iuoQogICAgICAgICAgdmFyIHZlbnVlU3RhdHMgPSB7fTsKICAgICAgICAgIGNvbGxlY3Rpb25zLmZvckVhY2goZnVuY3Rpb24gKGl0ZW0pIHsKICAgICAgICAgICAgdmFyIHZlbnVlSWQgPSBpdGVtLmNoYW5nZGlJZDsKICAgICAgICAgICAgaWYgKCF2ZW51ZVN0YXRzW3ZlbnVlSWRdKSB7CiAgICAgICAgICAgICAgdmVudWVTdGF0c1t2ZW51ZUlkXSA9IHsKICAgICAgICAgICAgICAgIGlkOiB2ZW51ZUlkLAogICAgICAgICAgICAgICAgbmFtZTogaXRlbS5jaGFuZ2RpTmFtZSB8fCAn5pyq55+l5Zy65ZywJywKICAgICAgICAgICAgICAgIHR5cGU6IGl0ZW0uY2hhbmdkaVZhbHVlIHx8ICfmnKrnn6XnsbvlnosnLAogICAgICAgICAgICAgICAgcGhvdG86IGl0ZW0uY2hhbmdkaVBob3RvIHx8ICcnLAogICAgICAgICAgICAgICAgY29sbGVjdGlvbnM6IDAKICAgICAgICAgICAgICB9OwogICAgICAgICAgICB9CiAgICAgICAgICAgIHZlbnVlU3RhdHNbdmVudWVJZF0uY29sbGVjdGlvbnMrKzsKICAgICAgICAgIH0pOwoKICAgICAgICAgIC8vIOi9rOaNouS4uuaVsOe7hOW5tuaOkuW6jwogICAgICAgICAgX3RoaXMzLnRvcFZlbnVlcyA9IE9iamVjdC52YWx1ZXModmVudWVTdGF0cykuc29ydChmdW5jdGlvbiAoYSwgYikgewogICAgICAgICAgICByZXR1cm4gYi5jb2xsZWN0aW9ucyAtIGEuY29sbGVjdGlvbnM7CiAgICAgICAgICB9KS5zbGljZSgwLCA1KTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsKICAgICAgICBjb25zb2xlLmxvZygn6I635Y+W54Ot6Zeo5Zy65Zyw5aSx6LSl77yM5L2/55So6buY6K6k5pWw5o2uJyk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWKoOi9veivpue7huaVsOaNrgogICAgbG9hZERldGFpbERhdGE6IGZ1bmN0aW9uIGxvYWREZXRhaWxEYXRhKCkgewogICAgICB2YXIgX3RoaXM0ID0gdGhpczsKICAgICAgdGhpcy4kaHR0cCh7CiAgICAgICAgdXJsOiAnY2hhbmdkaUNvbGxlY3Rpb24vcGFnZScsCiAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgIHBhZ2U6IDEsCiAgICAgICAgICBsaW1pdDogNTAKICAgICAgICB9CiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKF9yZWY0KSB7CiAgICAgICAgdmFyIGRhdGEgPSBfcmVmNC5kYXRhOwogICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgewogICAgICAgICAgdmFyIGNvbGxlY3Rpb25zID0gZGF0YS5kYXRhLmxpc3QgfHwgW107CgogICAgICAgICAgLy8g5oyJ5Zy65Zyw5YiG57uE57uf6K6hCiAgICAgICAgICB2YXIgdmVudWVTdGF0cyA9IHt9OwogICAgICAgICAgY29sbGVjdGlvbnMuZm9yRWFjaChmdW5jdGlvbiAoaXRlbSkgewogICAgICAgICAgICB2YXIgdmVudWVJZCA9IGl0ZW0uY2hhbmdkaUlkOwogICAgICAgICAgICBpZiAoIXZlbnVlU3RhdHNbdmVudWVJZF0pIHsKICAgICAgICAgICAgICB2ZW51ZVN0YXRzW3ZlbnVlSWRdID0gewogICAgICAgICAgICAgICAgdmVudWVOYW1lOiBpdGVtLmNoYW5nZGlOYW1lIHx8ICfmnKrnn6XlnLrlnLAnLAogICAgICAgICAgICAgICAgdmVudWVUeXBlOiBpdGVtLmNoYW5nZGlWYWx1ZSB8fCAn5pyq55+l57G75Z6LJywKICAgICAgICAgICAgICAgIHRvdGFsQ29sbGVjdGlvbnM6IDAsCiAgICAgICAgICAgICAgICB0b2RheUNvbGxlY3Rpb25zOiAwLAogICAgICAgICAgICAgICAgd2Vla0NvbGxlY3Rpb25zOiAwLAogICAgICAgICAgICAgICAgY29udmVyc2lvblJhdGU6IE1hdGgucmFuZG9tKCkgKiA1MCArIDIwLAogICAgICAgICAgICAgICAgLy8g5qih5ouf6L2s5YyW546HCiAgICAgICAgICAgICAgICBhdmdSYXRpbmc6IE1hdGgucmFuZG9tKCkgKiAyICsgMyAvLyDmqKHmi5/or4TliIYKICAgICAgICAgICAgICB9OwogICAgICAgICAgICB9CiAgICAgICAgICAgIHZlbnVlU3RhdHNbdmVudWVJZF0udG90YWxDb2xsZWN0aW9ucysrOwoKICAgICAgICAgICAgLy8g57uf6K6h5LuK5pel5ZKM5pys5ZGo5pS26JePCiAgICAgICAgICAgIHZhciBjb2xsZWN0VGltZSA9IG5ldyBEYXRlKGl0ZW0uaW5zZXJ0VGltZSk7CiAgICAgICAgICAgIHZhciB0b2RheSA9IG5ldyBEYXRlKCk7CiAgICAgICAgICAgIHZhciB3ZWVrQWdvID0gbmV3IERhdGUodG9kYXkuZ2V0VGltZSgpIC0gNyAqIDI0ICogNjAgKiA2MCAqIDEwMDApOwogICAgICAgICAgICBpZiAoY29sbGVjdFRpbWUudG9EYXRlU3RyaW5nKCkgPT09IHRvZGF5LnRvRGF0ZVN0cmluZygpKSB7CiAgICAgICAgICAgICAgdmVudWVTdGF0c1t2ZW51ZUlkXS50b2RheUNvbGxlY3Rpb25zKys7CiAgICAgICAgICAgIH0KICAgICAgICAgICAgaWYgKGNvbGxlY3RUaW1lID49IHdlZWtBZ28pIHsKICAgICAgICAgICAgICB2ZW51ZVN0YXRzW3ZlbnVlSWRdLndlZWtDb2xsZWN0aW9ucysrOwogICAgICAgICAgICB9CiAgICAgICAgICB9KTsKICAgICAgICAgIF90aGlzNC5kZXRhaWxEYXRhID0gT2JqZWN0LnZhbHVlcyh2ZW51ZVN0YXRzKS5zb3J0KGZ1bmN0aW9uIChhLCBiKSB7CiAgICAgICAgICAgIHJldHVybiBiLnRvdGFsQ29sbGVjdGlvbnMgLSBhLnRvdGFsQ29sbGVjdGlvbnM7CiAgICAgICAgICB9KS5zbGljZSgwLCAxMCk7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPluivpue7huaVsOaNruWksei0pe+8jOS9v+eUqOm7mOiupOaVsOaNricpOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDojrflj5bmjpLlkI3moLflvI8KICAgIGdldFJhbmtpbmdDbGFzczogZnVuY3Rpb24gZ2V0UmFua2luZ0NsYXNzKGluZGV4KSB7CiAgICAgIGlmIChpbmRleCA9PT0gMCkgcmV0dXJuICdnb2xkJzsKICAgICAgaWYgKGluZGV4ID09PSAxKSByZXR1cm4gJ3NpbHZlcic7CiAgICAgIGlmIChpbmRleCA9PT0gMikgcmV0dXJuICdicm9uemUnOwogICAgICByZXR1cm4gJ25vcm1hbCc7CiAgICB9LAogICAgLy8g5a+85Ye65pWw5o2uCiAgICBleHBvcnREYXRhOiBmdW5jdGlvbiBleHBvcnREYXRhKCkgewogICAgICB0aGlzLiRtZXNzYWdlLmluZm8oJ+WvvOWHuuWKn+iDveW8gOWPkeS4rS4uLicpOwogICAgfSwKICAgIC8vIOWIt+aWsOaVsOaNrgogICAgcmVmcmVzaERhdGE6IGZ1bmN0aW9uIHJlZnJlc2hEYXRhKCkgewogICAgICB0aGlzLmxvYWRBbmFseXRpY3NEYXRhKCk7CiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5pWw5o2u5bey5Yi35pawJyk7CiAgICB9LAogICAgLy8g5Zu+54mH6ZSZ6K+v5aSE55CGCiAgICBoYW5kbGVJbWFnZUVycm9yOiBmdW5jdGlvbiBoYW5kbGVJbWFnZUVycm9yKGV2ZW50KSB7CiAgICAgIGV2ZW50LnRhcmdldC5zcmMgPSAnL3N0YXRpYy9pbWFnZXMvZGVmYXVsdC12ZW51ZS5wbmcnOwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["name", "data", "totalCollections", "todayIncrease", "popularVenues", "activeCollectors", "avgCollectionsPerUser", "trendPeriod", "topVenues", "id", "type", "photo", "collections", "timeDistribution", "time", "percentage", "detailData", "venueName", "venueType", "todayCollections", "weekCollections", "conversionRate", "avgRating", "mounted", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "loadAnalyticsData", "methods", "_this", "$nextTick", "initTrendChart", "initTypeChart", "console", "log", "loadTrendData", "loadCollectionStats", "loadTopVenues", "loadDetailData", "_this2", "$http", "url", "method", "params", "page", "limit", "then", "_ref", "code", "total", "catch", "today", "Date", "toISOString", "split", "insertTimeStart", "insertTimeEnd", "_ref2", "_this3", "_ref3", "list", "venueStats", "for<PERSON>ach", "item", "venueId", "changdiId", "changdiName", "changdiValue", "changdiPhoto", "Object", "values", "sort", "a", "b", "slice", "_this4", "_ref4", "Math", "random", "collectTime", "insertTime", "weekAgo", "getTime", "toDateString", "getRankingClass", "index", "exportData", "$message", "info", "refreshData", "success", "handleImageError", "event", "target", "src"], "sources": ["src/views/collection-analytics.vue"], "sourcesContent": ["<template>\n  <div class=\"collection-analytics\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">\n        <i class=\"el-icon-data-analysis\"></i>\n        收藏数据分析\n      </h1>\n      <p class=\"page-description\">分析用户收藏行为，了解热门场地和收藏趋势</p>\n    </div>\n\n    <!-- 核心指标卡片 -->\n    <div class=\"metrics-section\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"metric-card primary\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ totalCollections }}</h3>\n              <p>总收藏数</p>\n              <span class=\"metric-trend up\">\n                <i class=\"el-icon-top\"></i>\n                +{{ todayIncrease }}\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card success\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ popularVenues }}</h3>\n              <p>热门场地数</p>\n              <span class=\"metric-trend\">\n                收藏>10次\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card warning\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ activeCollectors }}</h3>\n              <p>活跃收藏用户</p>\n              <span class=\"metric-trend\">\n                本月活跃\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card info\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-pie-chart\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ avgCollectionsPerUser.toFixed(1) }}</h3>\n              <p>人均收藏数</p>\n              <span class=\"metric-trend\">\n                平均值\n              </span>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 图表区域 -->\n    <div class=\"charts-section\">\n      <el-row :gutter=\"20\">\n        <!-- 收藏趋势图 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>收藏趋势</span>\n              <el-select v-model=\"trendPeriod\" size=\"small\" @change=\"loadTrendData\">\n                <el-option label=\"最近7天\" value=\"7days\"></el-option>\n                <el-option label=\"最近30天\" value=\"30days\"></el-option>\n                <el-option label=\"最近3个月\" value=\"3months\"></el-option>\n              </el-select>\n            </div>\n            <div id=\"trendChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n        \n        <!-- 场地类型分布 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>场地类型收藏分布</span>\n            </div>\n            <div id=\"typeChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n        <!-- 热门场地排行 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>热门场地排行</span>\n            </div>\n            <div class=\"ranking-list\">\n              <div \n                v-for=\"(venue, index) in topVenues\" \n                :key=\"venue.id\"\n                class=\"ranking-item\"\n                :class=\"{ 'top-three': index < 3 }\">\n                <div class=\"ranking-number\" :class=\"getRankingClass(index)\">\n                  {{ index + 1 }}\n                </div>\n                <div class=\"venue-info\">\n                  <img \n                    v-if=\"venue.photo\" \n                    :src=\"venue.photo.split(',')[0]\" \n                    class=\"venue-thumb\"\n                    @error=\"handleImageError\">\n                  <div class=\"venue-details\">\n                    <h4>{{ venue.name }}</h4>\n                    <p>{{ venue.type }}</p>\n                  </div>\n                </div>\n                <div class=\"collection-count\">\n                  <span class=\"count\">{{ venue.collections }}</span>\n                  <span class=\"label\">收藏</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <!-- 用户收藏行为分析 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>用户收藏行为</span>\n            </div>\n            <div class=\"behavior-stats\">\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏高峰时段</div>\n                <div class=\"behavior-value\">14:00 - 16:00</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">平均收藏间隔</div>\n                <div class=\"behavior-value\">2.3天</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">取消收藏率</div>\n                <div class=\"behavior-value\">12.5%</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏转预约率</div>\n                <div class=\"behavior-value\">35.8%</div>\n              </div>\n            </div>\n            \n            <div class=\"time-distribution\">\n              <h4>收藏时间分布</h4>\n              <div class=\"time-bars\">\n                <div \n                  v-for=\"hour in timeDistribution\" \n                  :key=\"hour.time\"\n                  class=\"time-bar\">\n                  <div class=\"bar\" :style=\"{ height: hour.percentage + '%' }\"></div>\n                  <span class=\"time-label\">{{ hour.time }}</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 详细数据表格 -->\n    <div class=\"data-table-section\">\n      <el-card>\n        <div slot=\"header\" class=\"table-header\">\n          <span>收藏详细数据</span>\n          <div class=\"table-actions\">\n            <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\n              导出数据\n            </el-button>\n            <el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n        \n        <el-table :data=\"detailData\" stripe style=\"width: 100%\">\n          <el-table-column prop=\"venueName\" label=\"场地名称\" min-width=\"150\"></el-table-column>\n          <el-table-column prop=\"venueType\" label=\"场地类型\" width=\"120\"></el-table-column>\n          <el-table-column prop=\"totalCollections\" label=\"总收藏数\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"todayCollections\" label=\"今日新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"weekCollections\" label=\"本周新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"conversionRate\" label=\"转预约率\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              {{ scope.row.conversionRate }}%\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"avgRating\" label=\"平均评分\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              <el-rate \n                v-model=\"scope.row.avgRating\" \n                disabled \n                show-score \n                text-color=\"#ff9900\"\n                score-template=\"{value}\">\n              </el-rate>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CollectionAnalytics',\n  data() {\n    return {\n      // 核心指标\n      totalCollections: 1256,\n      todayIncrease: 23,\n      popularVenues: 15,\n      activeCollectors: 89,\n      avgCollectionsPerUser: 4.2,\n      \n      // 图表配置\n      trendPeriod: '7days',\n      \n      // 热门场地数据\n      topVenues: [\n        {\n          id: 1,\n          name: '中央体育馆篮球场',\n          type: '篮球场',\n          photo: '/static/images/basketball.jpg',\n          collections: 156\n        },\n        {\n          id: 2,\n          name: '游泳馆标准池',\n          type: '游泳池',\n          photo: '/static/images/swimming.jpg',\n          collections: 134\n        },\n        {\n          id: 3,\n          name: '网球场A区',\n          type: '网球场',\n          photo: '/static/images/tennis.jpg',\n          collections: 98\n        },\n        {\n          id: 4,\n          name: '羽毛球馆1号场',\n          type: '羽毛球场',\n          photo: '/static/images/badminton.jpg',\n          collections: 87\n        },\n        {\n          id: 5,\n          name: '足球场草坪',\n          type: '足球场',\n          photo: '/static/images/football.jpg',\n          collections: 76\n        }\n      ],\n      \n      // 时间分布数据\n      timeDistribution: [\n        { time: '6:00', percentage: 5 },\n        { time: '8:00', percentage: 15 },\n        { time: '10:00', percentage: 25 },\n        { time: '12:00', percentage: 35 },\n        { time: '14:00', percentage: 85 },\n        { time: '16:00', percentage: 90 },\n        { time: '18:00', percentage: 70 },\n        { time: '20:00', percentage: 45 },\n        { time: '22:00', percentage: 20 }\n      ],\n      \n      // 详细数据\n      detailData: [\n        {\n          venueName: '中央体育馆篮球场',\n          venueType: '篮球场',\n          totalCollections: 156,\n          todayCollections: 8,\n          weekCollections: 23,\n          conversionRate: 42.3,\n          avgRating: 4.5\n        },\n        {\n          venueName: '游泳馆标准池',\n          venueType: '游泳池',\n          totalCollections: 134,\n          todayCollections: 5,\n          weekCollections: 18,\n          conversionRate: 38.7,\n          avgRating: 4.3\n        },\n        {\n          venueName: '网球场A区',\n          venueType: '网球场',\n          totalCollections: 98,\n          todayCollections: 3,\n          weekCollections: 12,\n          conversionRate: 35.2,\n          avgRating: 4.2\n        }\n      ]\n    }\n  },\n  \n  mounted() {\n    this.initCharts()\n    this.loadAnalyticsData()\n  },\n  \n  methods: {\n    // 初始化图表\n    initCharts() {\n      this.$nextTick(() => {\n        this.initTrendChart()\n        this.initTypeChart()\n      })\n    },\n    \n    // 初始化趋势图\n    initTrendChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化收藏趋势图')\n    },\n    \n    // 初始化类型分布图\n    initTypeChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化场地类型分布图')\n    },\n    \n    // 加载趋势数据\n    loadTrendData() {\n      console.log('加载趋势数据:', this.trendPeriod)\n    },\n    \n    // 加载分析数据\n    loadAnalyticsData() {\n      this.loadCollectionStats()\n      this.loadTopVenues()\n      this.loadDetailData()\n    },\n\n    // 加载收藏统计\n    loadCollectionStats() {\n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalCollections = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取收藏统计失败')\n      })\n\n      // 获取今日收藏数\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 1000,\n          insertTimeStart: today,\n          insertTimeEnd: today\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.todayIncrease = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取今日收藏数失败')\n      })\n    },\n\n    // 加载热门场地\n    loadTopVenues() {\n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params: { page: 1, limit: 100 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          const collections = data.data.list || []\n\n          // 按场地ID分组统计\n          const venueStats = {}\n          collections.forEach(item => {\n            const venueId = item.changdiId\n            if (!venueStats[venueId]) {\n              venueStats[venueId] = {\n                id: venueId,\n                name: item.changdiName || '未知场地',\n                type: item.changdiValue || '未知类型',\n                photo: item.changdiPhoto || '',\n                collections: 0\n              }\n            }\n            venueStats[venueId].collections++\n          })\n\n          // 转换为数组并排序\n          this.topVenues = Object.values(venueStats)\n            .sort((a, b) => b.collections - a.collections)\n            .slice(0, 5)\n        }\n      }).catch(() => {\n        console.log('获取热门场地失败，使用默认数据')\n      })\n    },\n\n    // 加载详细数据\n    loadDetailData() {\n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params: { page: 1, limit: 50 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          const collections = data.data.list || []\n\n          // 按场地分组统计\n          const venueStats = {}\n          collections.forEach(item => {\n            const venueId = item.changdiId\n            if (!venueStats[venueId]) {\n              venueStats[venueId] = {\n                venueName: item.changdiName || '未知场地',\n                venueType: item.changdiValue || '未知类型',\n                totalCollections: 0,\n                todayCollections: 0,\n                weekCollections: 0,\n                conversionRate: Math.random() * 50 + 20, // 模拟转化率\n                avgRating: Math.random() * 2 + 3 // 模拟评分\n              }\n            }\n            venueStats[venueId].totalCollections++\n\n            // 统计今日和本周收藏\n            const collectTime = new Date(item.insertTime)\n            const today = new Date()\n            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)\n\n            if (collectTime.toDateString() === today.toDateString()) {\n              venueStats[venueId].todayCollections++\n            }\n            if (collectTime >= weekAgo) {\n              venueStats[venueId].weekCollections++\n            }\n          })\n\n          this.detailData = Object.values(venueStats)\n            .sort((a, b) => b.totalCollections - a.totalCollections)\n            .slice(0, 10)\n        }\n      }).catch(() => {\n        console.log('获取详细数据失败，使用默认数据')\n      })\n    },\n    \n    // 获取排名样式\n    getRankingClass(index) {\n      if (index === 0) return 'gold'\n      if (index === 1) return 'silver'\n      if (index === 2) return 'bronze'\n      return 'normal'\n    },\n    \n    // 导出数据\n    exportData() {\n      this.$message.info('导出功能开发中...')\n    },\n    \n    // 刷新数据\n    refreshData() {\n      this.loadAnalyticsData()\n      this.$message.success('数据已刷新')\n    },\n    \n    // 图片错误处理\n    handleImageError(event) {\n      event.target.src = '/static/images/default-venue.png'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collection-analytics {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-title {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n}\n\n.page-title i {\n  margin-right: 12px;\n  color: #409eff;\n}\n\n.page-description {\n  color: #909399;\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 核心指标卡片 */\n.metrics-section {\n  margin-bottom: 24px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.metric-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.metric-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n}\n\n.metric-card.primary::before {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success::before {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning::before {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info::before {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.metric-card.primary .metric-icon {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success .metric-icon {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning .metric-icon {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info .metric-icon {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon i {\n  font-size: 24px;\n  color: white;\n}\n\n.metric-content h3 {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.metric-content p {\n  margin: 0 0 8px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.metric-trend {\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 12px;\n  background: #f0f9ff;\n  color: #0369a1;\n}\n\n.metric-trend.up {\n  background: #f0fdf4;\n  color: #166534;\n}\n\n.metric-trend i {\n  margin-right: 2px;\n}\n\n/* 图表区域 */\n.charts-section {\n  margin-bottom: 24px;\n}\n\n.chart-card {\n  height: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border-radius: 12px;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n.chart-container {\n  height: 320px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 排行榜 */\n.ranking-list {\n  max-height: 320px;\n  overflow-y: auto;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n  transition: background-color 0.3s ease;\n}\n\n.ranking-item:hover {\n  background-color: #f8f9fa;\n}\n\n.ranking-item.top-three {\n  background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);\n  border-radius: 8px;\n  margin-bottom: 8px;\n  padding: 16px 12px;\n}\n\n.ranking-number {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  margin-right: 12px;\n  color: white;\n  font-size: 14px;\n}\n\n.ranking-number.gold {\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\n}\n\n.ranking-number.silver {\n  background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);\n}\n\n.ranking-number.bronze {\n  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);\n}\n\n.ranking-number.normal {\n  background: #e5e7eb;\n  color: #6b7280;\n}\n\n.venue-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.venue-thumb {\n  width: 48px;\n  height: 48px;\n  border-radius: 8px;\n  object-fit: cover;\n  margin-right: 12px;\n  border: 1px solid #ebeef5;\n}\n\n.venue-details h4 {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.venue-details p {\n  margin: 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n.collection-count {\n  text-align: center;\n}\n\n.collection-count .count {\n  display: block;\n  font-size: 18px;\n  font-weight: 700;\n  color: #f56c6c;\n}\n\n.collection-count .label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 用户行为分析 */\n.behavior-stats {\n  margin-bottom: 24px;\n}\n\n.behavior-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.behavior-label {\n  color: #606266;\n  font-size: 14px;\n}\n\n.behavior-value {\n  font-weight: 600;\n  color: #303133;\n  font-size: 16px;\n}\n\n.time-distribution h4 {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.time-bars {\n  display: flex;\n  align-items: end;\n  justify-content: space-between;\n  height: 120px;\n  padding: 0 8px;\n}\n\n.time-bar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.bar {\n  width: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2px 2px 0 0;\n  margin-bottom: 8px;\n  min-height: 4px;\n  transition: all 0.3s ease;\n}\n\n.time-bar:hover .bar {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.time-label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 数据表格 */\n.data-table-section {\n  margin-bottom: 24px;\n}\n\n.table-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .collection-analytics {\n    padding: 12px;\n  }\n\n  .metrics-section .el-col {\n    margin-bottom: 12px;\n  }\n\n  .charts-section .el-col {\n    margin-bottom: 20px;\n  }\n\n  .metric-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .metric-icon {\n    margin-right: 0;\n    margin-bottom: 12px;\n  }\n\n  .venue-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .venue-thumb {\n    margin-bottom: 8px;\n  }\n}\n</style>\n"], "mappings": ";;;;;;;AAiOA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,gBAAA;MACAC,aAAA;MACAC,aAAA;MACAC,gBAAA;MACAC,qBAAA;MAEA;MACAC,WAAA;MAEA;MACAC,SAAA,GACA;QACAC,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,GACA;QACAH,EAAA;QACAT,IAAA;QACAU,IAAA;QACAC,KAAA;QACAC,WAAA;MACA,EACA;MAEA;MACAC,gBAAA,GACA;QAAAC,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,UAAA;MAAA,EACA;MAEA;MACAC,UAAA,GACA;QACAC,SAAA;QACAC,SAAA;QACAhB,gBAAA;QACAiB,gBAAA;QACAC,eAAA;QACAC,cAAA;QACAC,SAAA;MACA,GACA;QACAL,SAAA;QACAC,SAAA;QACAhB,gBAAA;QACAiB,gBAAA;QACAC,eAAA;QACAC,cAAA;QACAC,SAAA;MACA,GACA;QACAL,SAAA;QACAC,SAAA;QACAhB,gBAAA;QACAiB,gBAAA;QACAC,eAAA;QACAC,cAAA;QACAC,SAAA;MACA;IAEA;EACA;EAEAC,OAAA,WAAAA,QAAA;IACA,KAAAC,UAAA;IACA,KAAAC,iBAAA;EACA;EAEAC,OAAA;IACA;IACAF,UAAA,WAAAA,WAAA;MAAA,IAAAG,KAAA;MACA,KAAAC,SAAA;QACAD,KAAA,CAAAE,cAAA;QACAF,KAAA,CAAAG,aAAA;MACA;IACA;IAEA;IACAD,cAAA,WAAAA,eAAA;MACA;MACAE,OAAA,CAAAC,GAAA;IACA;IAEA;IACAF,aAAA,WAAAA,cAAA;MACA;MACAC,OAAA,CAAAC,GAAA;IACA;IAEA;IACAC,aAAA,WAAAA,cAAA;MACAF,OAAA,CAAAC,GAAA,iBAAAzB,WAAA;IACA;IAEA;IACAkB,iBAAA,WAAAA,kBAAA;MACA,KAAAS,mBAAA;MACA,KAAAC,aAAA;MACA,KAAAC,cAAA;IACA;IAEA;IACAF,mBAAA,WAAAA,oBAAA;MAAA,IAAAG,MAAA;MACA,KAAAC,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAA5C,IAAA,GAAA4C,IAAA,CAAA5C,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA6C,IAAA;UACAT,MAAA,CAAAnC,gBAAA,GAAAD,IAAA,CAAAA,IAAA,CAAA8C,KAAA;QACA;MACA,GAAAC,KAAA;QACAjB,OAAA,CAAAC,GAAA;MACA;;MAEA;MACA,IAAAiB,KAAA,OAAAC,IAAA,GAAAC,WAAA,GAAAC,KAAA;MACA,KAAAd,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UACAC,IAAA;UACAC,KAAA;UACAU,eAAA,EAAAJ,KAAA;UACAK,aAAA,EAAAL;QACA;MACA,GAAAL,IAAA,WAAAW,KAAA;QAAA,IAAAtD,IAAA,GAAAsD,KAAA,CAAAtD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA6C,IAAA;UACAT,MAAA,CAAAlC,aAAA,GAAAF,IAAA,CAAAA,IAAA,CAAA8C,KAAA;QACA;MACA,GAAAC,KAAA;QACAjB,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAG,aAAA,WAAAA,cAAA;MAAA,IAAAqB,MAAA;MACA,KAAAlB,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAAa,KAAA;QAAA,IAAAxD,IAAA,GAAAwD,KAAA,CAAAxD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA6C,IAAA;UACA,IAAAlC,WAAA,GAAAX,IAAA,CAAAA,IAAA,CAAAyD,IAAA;;UAEA;UACA,IAAAC,UAAA;UACA/C,WAAA,CAAAgD,OAAA,WAAAC,IAAA;YACA,IAAAC,OAAA,GAAAD,IAAA,CAAAE,SAAA;YACA,KAAAJ,UAAA,CAAAG,OAAA;cACAH,UAAA,CAAAG,OAAA;gBACArD,EAAA,EAAAqD,OAAA;gBACA9D,IAAA,EAAA6D,IAAA,CAAAG,WAAA;gBACAtD,IAAA,EAAAmD,IAAA,CAAAI,YAAA;gBACAtD,KAAA,EAAAkD,IAAA,CAAAK,YAAA;gBACAtD,WAAA;cACA;YACA;YACA+C,UAAA,CAAAG,OAAA,EAAAlD,WAAA;UACA;;UAEA;UACA4C,MAAA,CAAAhD,SAAA,GAAA2D,MAAA,CAAAC,MAAA,CAAAT,UAAA,EACAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAA3D,WAAA,GAAA0D,CAAA,CAAA1D,WAAA;UAAA,GACA4D,KAAA;QACA;MACA,GAAAxB,KAAA;QACAjB,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAI,cAAA,WAAAA,eAAA;MAAA,IAAAqC,MAAA;MACA,KAAAnC,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAA8B,KAAA;QAAA,IAAAzE,IAAA,GAAAyE,KAAA,CAAAzE,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA6C,IAAA;UACA,IAAAlC,WAAA,GAAAX,IAAA,CAAAA,IAAA,CAAAyD,IAAA;;UAEA;UACA,IAAAC,UAAA;UACA/C,WAAA,CAAAgD,OAAA,WAAAC,IAAA;YACA,IAAAC,OAAA,GAAAD,IAAA,CAAAE,SAAA;YACA,KAAAJ,UAAA,CAAAG,OAAA;cACAH,UAAA,CAAAG,OAAA;gBACA7C,SAAA,EAAA4C,IAAA,CAAAG,WAAA;gBACA9C,SAAA,EAAA2C,IAAA,CAAAI,YAAA;gBACA/D,gBAAA;gBACAiB,gBAAA;gBACAC,eAAA;gBACAC,cAAA,EAAAsD,IAAA,CAAAC,MAAA;gBAAA;gBACAtD,SAAA,EAAAqD,IAAA,CAAAC,MAAA;cACA;YACA;YACAjB,UAAA,CAAAG,OAAA,EAAA5D,gBAAA;;YAEA;YACA,IAAA2E,WAAA,OAAA3B,IAAA,CAAAW,IAAA,CAAAiB,UAAA;YACA,IAAA7B,KAAA,OAAAC,IAAA;YACA,IAAA6B,OAAA,OAAA7B,IAAA,CAAAD,KAAA,CAAA+B,OAAA;YAEA,IAAAH,WAAA,CAAAI,YAAA,OAAAhC,KAAA,CAAAgC,YAAA;cACAtB,UAAA,CAAAG,OAAA,EAAA3C,gBAAA;YACA;YACA,IAAA0D,WAAA,IAAAE,OAAA;cACApB,UAAA,CAAAG,OAAA,EAAA1C,eAAA;YACA;UACA;UAEAqD,MAAA,CAAAzD,UAAA,GAAAmD,MAAA,CAAAC,MAAA,CAAAT,UAAA,EACAU,IAAA,WAAAC,CAAA,EAAAC,CAAA;YAAA,OAAAA,CAAA,CAAArE,gBAAA,GAAAoE,CAAA,CAAApE,gBAAA;UAAA,GACAsE,KAAA;QACA;MACA,GAAAxB,KAAA;QACAjB,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAkD,eAAA,WAAAA,gBAAAC,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA,IAAAA,KAAA;MACA;IACA;IAEA;IACAC,UAAA,WAAAA,WAAA;MACA,KAAAC,QAAA,CAAAC,IAAA;IACA;IAEA;IACAC,WAAA,WAAAA,YAAA;MACA,KAAA9D,iBAAA;MACA,KAAA4D,QAAA,CAAAG,OAAA;IACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAC,KAAA;MACAA,KAAA,CAAAC,MAAA,CAAAC,GAAA;IACA;EACA;AACA", "ignoreList": []}]}