{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\test-input.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\test-input.vue", "mtime": 1750609938955}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["test-input.vue"], "names": [], "mappings": ";AA6DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "test-input.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"test-input-container\">\n    <h2>输入框测试页面</h2>\n    \n    <div class=\"test-section\">\n      <h3>1. 基础输入框测试</h3>\n      <el-form>\n        <el-form-item label=\"用户名\">\n          <el-input v-model=\"testForm.username\" placeholder=\"请输入用户名\" clearable></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"密码\">\n          <el-input v-model=\"testForm.password\" type=\"password\" placeholder=\"请输入密码\" show-password clearable></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"邮箱\">\n          <el-input v-model=\"testForm.email\" placeholder=\"请输入邮箱\" clearable></el-input>\n        </el-form-item>\n      </el-form>\n    </div>\n    \n    <div class=\"test-section\">\n      <h3>2. 登录样式输入框测试</h3>\n      <div class=\"login-style-form\">\n        <div class=\"form-item\">\n          <div class=\"input-wrapper\">\n            <i class=\"el-icon-user input-icon\"></i>\n            <el-input v-model=\"loginForm.username\" placeholder=\"请输入用户名\" size=\"large\" clearable></el-input>\n          </div>\n        </div>\n        \n        <div class=\"form-item\">\n          <div class=\"input-wrapper\">\n            <i class=\"el-icon-lock input-icon\"></i>\n            <el-input v-model=\"loginForm.password\" type=\"password\" placeholder=\"请输入密码\" size=\"large\" show-password clearable></el-input>\n          </div>\n        </div>\n      </div>\n    </div>\n    \n    <div class=\"test-section\">\n      <h3>3. 测试结果</h3>\n      <div class=\"test-results\">\n        <p><strong>基础表单数据:</strong></p>\n        <pre>{{ JSON.stringify(testForm, null, 2) }}</pre>\n        \n        <p><strong>登录表单数据:</strong></p>\n        <pre>{{ JSON.stringify(loginForm, null, 2) }}</pre>\n      </div>\n    </div>\n    \n    <div class=\"test-section\">\n      <h3>4. 操作按钮</h3>\n      <el-button @click=\"clearAll\">清空所有</el-button>\n      <el-button @click=\"fillTest\" type=\"primary\">填充测试数据</el-button>\n      <el-button @click=\"checkInputs\" type=\"success\">检查输入框状态</el-button>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TestInput',\n  data() {\n    return {\n      testForm: {\n        username: '',\n        password: '',\n        email: ''\n      },\n      loginForm: {\n        username: '',\n        password: ''\n      }\n    }\n  },\n  mounted() {\n    this.enableAllInputs();\n  },\n  methods: {\n    enableAllInputs() {\n      this.$nextTick(() => {\n        // 确保所有输入框都可以正常使用\n        document.querySelectorAll('.el-input__inner').forEach(el => {\n          el.removeAttribute('readonly');\n          el.removeAttribute('disabled');\n          el.style.pointerEvents = 'auto';\n          el.style.userSelect = 'text';\n          el.style.cursor = 'text';\n          el.style.backgroundColor = 'white';\n          el.style.color = '#333';\n        });\n      });\n    },\n    \n    clearAll() {\n      this.testForm = {\n        username: '',\n        password: '',\n        email: ''\n      };\n      this.loginForm = {\n        username: '',\n        password: ''\n      };\n    },\n    \n    fillTest() {\n      this.testForm = {\n        username: 'testuser',\n        password: 'testpass',\n        email: '<EMAIL>'\n      };\n      this.loginForm = {\n        username: 'admin',\n        password: 'admin123'\n      };\n    },\n    \n    checkInputs() {\n      const inputs = document.querySelectorAll('.el-input__inner');\n      let report = [];\n      \n      inputs.forEach((input, index) => {\n        report.push({\n          index: index,\n          readonly: input.hasAttribute('readonly'),\n          disabled: input.hasAttribute('disabled'),\n          pointerEvents: input.style.pointerEvents,\n          userSelect: input.style.userSelect,\n          cursor: input.style.cursor,\n          value: input.value\n        });\n      });\n      \n      console.log('输入框状态检查:', report);\n      this.$message.success('输入框状态已输出到控制台');\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.test-input-container {\n  padding: 20px;\n  max-width: 800px;\n  margin: 0 auto;\n}\n\n.test-section {\n  margin: 30px 0;\n  padding: 20px;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  background: #f9f9f9;\n}\n\n.login-style-form {\n  .form-item {\n    margin-bottom: 20px;\n    \n    .input-wrapper {\n      position: relative;\n      \n      .input-icon {\n        position: absolute;\n        left: 15px;\n        top: 50%;\n        transform: translateY(-50%);\n        color: #00c292;\n        font-size: 18px;\n        z-index: 2;\n      }\n      \n      ::v-deep .el-input__inner {\n        height: 50px;\n        padding-left: 50px;\n        border: 2px solid #e8f4f8;\n        border-radius: 12px;\n        font-size: 16px;\n        transition: all 0.3s ease;\n        background: white !important;\n        color: #333 !important;\n        pointer-events: auto !important;\n        user-select: text !important;\n        cursor: text !important;\n\n        &:focus {\n          border-color: #00c292;\n          box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);\n          background: white !important;\n          color: #333 !important;\n          outline: none;\n        }\n\n        &::placeholder {\n          color: #999 !important;\n        }\n\n        &:hover {\n          border-color: #00c292;\n        }\n      }\n    }\n  }\n}\n\n.test-results {\n  pre {\n    background: #f5f5f5;\n    padding: 10px;\n    border-radius: 4px;\n    font-size: 12px;\n    overflow-x: auto;\n  }\n}\n</style>\n"]}]}