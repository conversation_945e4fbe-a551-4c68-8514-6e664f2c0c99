{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\changdi-booking.vue?vue&type=style&index=0&id=20b3bc08&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\changdi-booking.vue", "mtime": 1750596006761}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["changdi-booking.vue"], "names": [], "mappings": ";AAsdA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "changdi-booking.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"booking-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <div class=\"header-content\">\n        <div class=\"title-section\">\n          <h1><i class=\"el-icon-date\"></i> 场地预约</h1>\n          <p>选择您喜欢的场地，享受运动的乐趣</p>\n        </div>\n        <div class=\"stats-cards\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ totalVenues }}</h3>\n              <p>可用场地</p>\n            </div>\n          </div>\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\">\n              <i class=\"el-icon-time\"></i>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ availableSlots }}</h3>\n              <p>可预约时段</p>\n            </div>\n          </div>\n          <div class=\"stat-card\">\n            <div class=\"stat-icon\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"stat-info\">\n              <h3>{{ myBookings }}</h3>\n              <p>我的预约</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 搜索筛选区域 -->\n    <div class=\"search-section\">\n      <el-card class=\"search-card\">\n        <div class=\"search-form\">\n          <el-row :gutter=\"20\">\n            <el-col :span=\"6\">\n              <div class=\"search-item\">\n                <label>场地类型</label>\n                <el-select v-model=\"searchForm.changdiTypes\" placeholder=\"请选择场地类型\" clearable>\n                  <el-option label=\"全部类型\" value=\"\"></el-option>\n                  <el-option\n                    v-for=\"item in changdiTypesOptions\"\n                    :key=\"item.codeIndex\"\n                    :label=\"item.indexName\"\n                    :value=\"item.codeIndex\">\n                  </el-option>\n                </el-select>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"search-item\">\n                <label>预约日期</label>\n                <el-date-picker\n                  v-model=\"searchForm.bookingDate\"\n                  type=\"date\"\n                  placeholder=\"选择预约日期\"\n                  :picker-options=\"datePickerOptions\"\n                  clearable>\n                </el-date-picker>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"search-item\">\n                <label>价格范围</label>\n                <el-select v-model=\"searchForm.priceRange\" placeholder=\"请选择价格范围\" clearable>\n                  <el-option label=\"全部价格\" value=\"\"></el-option>\n                  <el-option label=\"0-50元\" value=\"0-50\"></el-option>\n                  <el-option label=\"50-100元\" value=\"50-100\"></el-option>\n                  <el-option label=\"100-200元\" value=\"100-200\"></el-option>\n                  <el-option label=\"200元以上\" value=\"200+\"></el-option>\n                </el-select>\n              </div>\n            </el-col>\n            <el-col :span=\"6\">\n              <div class=\"search-actions\">\n                <el-button type=\"primary\" @click=\"searchVenues\" :loading=\"searchLoading\">\n                  <i class=\"el-icon-search\"></i>\n                  搜索场地\n                </el-button>\n                <el-button @click=\"resetSearch\">\n                  <i class=\"el-icon-refresh\"></i>\n                  重置\n                </el-button>\n              </div>\n            </el-col>\n          </el-row>\n        </div>\n      </el-card>\n    </div>\n\n    <!-- 场地列表 -->\n    <div class=\"venues-section\">\n      <div class=\"section-header\">\n        <h2>可预约场地</h2>\n        <div class=\"view-toggle\">\n          <el-radio-group v-model=\"viewMode\" size=\"small\">\n            <el-radio-button label=\"grid\">\n              <i class=\"el-icon-menu\"></i> 网格视图\n            </el-radio-button>\n            <el-radio-button label=\"list\">\n              <i class=\"el-icon-tickets\"></i> 列表视图\n            </el-radio-button>\n          </el-radio-group>\n        </div>\n      </div>\n\n      <!-- 网格视图 -->\n      <div v-if=\"viewMode === 'grid'\" class=\"venues-grid\" v-loading=\"dataListLoading\">\n        <div v-if=\"dataList.length === 0\" class=\"empty-state\">\n          <i class=\"el-icon-basketball\"></i>\n          <h3>暂无可预约场地</h3>\n          <p>请尝试调整搜索条件</p>\n        </div>\n        <div v-else class=\"venue-cards\">\n          <div v-for=\"venue in dataList\" :key=\"venue.id\" class=\"venue-card\">\n            <div class=\"venue-image\">\n              <img :src=\"venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" :alt=\"venue.changdiName\">\n              <div class=\"venue-status\" :class=\"venue.shangxiaTypes === 1 ? 'available' : 'unavailable'\">\n                {{ venue.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}\n              </div>\n            </div>\n            <div class=\"venue-info\">\n              <h3>{{ venue.changdiName }}</h3>\n              <div class=\"venue-details\">\n                <div class=\"detail-item\">\n                  <i class=\"el-icon-location\"></i>\n                  <span>{{ venue.changdiValue }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"el-icon-time\"></i>\n                  <span>{{ venue.shijianduan }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <i class=\"el-icon-star-on\"></i>\n                  <span>{{ venue.banquanValue }}</span>\n                </div>\n              </div>\n              <div class=\"venue-price\">\n                <span class=\"current-price\">¥{{ venue.changdiNewMoney }}</span>\n                <span v-if=\"venue.changdiOldMoney !== venue.changdiNewMoney\" class=\"original-price\">\n                  ¥{{ venue.changdiOldMoney }}\n                </span>\n              </div>\n              <div class=\"venue-actions\">\n                <el-button type=\"primary\" @click=\"bookVenue(venue)\" :disabled=\"venue.shangxiaTypes !== 1\">\n                  <i class=\"el-icon-date\"></i>\n                  立即预约\n                </el-button>\n                <el-button type=\"text\" @click=\"viewVenueDetails(venue)\">\n                  <i class=\"el-icon-view\"></i>\n                  查看详情\n                </el-button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 列表视图 -->\n      <div v-if=\"viewMode === 'list'\" class=\"venues-list\" v-loading=\"dataListLoading\">\n        <el-table :data=\"dataList\" style=\"width: 100%\">\n          <el-table-column prop=\"changdiPhoto\" label=\"场地图片\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <img :src=\"scope.row.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" \n                   style=\"width: 80px; height: 60px; object-fit: cover; border-radius: 4px;\">\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"changdiName\" label=\"场地名称\" min-width=\"150\"></el-table-column>\n          <el-table-column prop=\"changdiValue\" label=\"场地类型\" width=\"120\"></el-table-column>\n          <el-table-column prop=\"shijianduan\" label=\"时间段\" width=\"150\"></el-table-column>\n          <el-table-column prop=\"banquanValue\" label=\"半全场\" width=\"100\"></el-table-column>\n          <el-table-column label=\"价格\" width=\"120\">\n            <template slot-scope=\"scope\">\n              <div class=\"price-cell\">\n                <span class=\"current-price\">¥{{ scope.row.changdiNewMoney }}</span>\n                <span v-if=\"scope.row.changdiOldMoney !== scope.row.changdiNewMoney\" class=\"original-price\">\n                  ¥{{ scope.row.changdiOldMoney }}\n                </span>\n              </div>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"状态\" width=\"100\">\n            <template slot-scope=\"scope\">\n              <el-tag :type=\"scope.row.shangxiaTypes === 1 ? 'success' : 'danger'\">\n                {{ scope.row.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}\n              </el-tag>\n            </template>\n          </el-table-column>\n          <el-table-column label=\"操作\" width=\"200\">\n            <template slot-scope=\"scope\">\n              <el-button type=\"primary\" size=\"mini\" @click=\"bookVenue(scope.row)\" \n                         :disabled=\"scope.row.shangxiaTypes !== 1\">\n                立即预约\n              </el-button>\n              <el-button type=\"text\" size=\"mini\" @click=\"viewVenueDetails(scope.row)\">\n                查看详情\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n      </div>\n\n      <!-- 分页 -->\n      <div class=\"pagination-wrapper\">\n        <el-pagination\n          @size-change=\"sizeChangeHandle\"\n          @current-change=\"currentChangeHandle\"\n          :current-page=\"pageIndex\"\n          :page-sizes=\"[12, 24, 48]\"\n          :page-size=\"pageSize\"\n          :total=\"totalPage\"\n          layout=\"total, sizes, prev, pager, next, jumper\">\n        </el-pagination>\n      </div>\n    </div>\n\n    <!-- 预约对话框 -->\n    <booking-dialog\n      v-if=\"bookingDialogVisible\"\n      :visible.sync=\"bookingDialogVisible\"\n      :venue=\"selectedVenue\"\n      @booking-success=\"onBookingSuccess\">\n    </booking-dialog>\n\n    <!-- 场地详情对话框 -->\n    <venue-details-dialog\n      v-if=\"detailsDialogVisible\"\n      :visible.sync=\"detailsDialogVisible\"\n      :venue=\"selectedVenue\"\n      @book-venue=\"bookVenue\">\n    </venue-details-dialog>\n  </div>\n</template>\n\n<script>\nimport BookingDialog from './components/BookingDialog.vue'\nimport VenueDetailsDialog from './components/VenueDetailsDialog.vue'\n\nexport default {\n  name: 'ChangdiBooking',\n  components: {\n    BookingDialog,\n    VenueDetailsDialog\n  },\n  data() {\n    return {\n      // 统计数据\n      totalVenues: 0,\n      availableSlots: 0,\n      myBookings: 0,\n      \n      // 搜索表单\n      searchForm: {\n        changdiTypes: '',\n        bookingDate: '',\n        priceRange: ''\n      },\n      \n      // 视图模式\n      viewMode: 'grid',\n      \n      // 数据列表\n      dataList: [],\n      pageIndex: 1,\n      pageSize: 12,\n      totalPage: 0,\n      dataListLoading: false,\n      searchLoading: false,\n      \n      // 选项数据\n      changdiTypesOptions: [],\n      \n      // 对话框状态\n      bookingDialogVisible: false,\n      detailsDialogVisible: false,\n      selectedVenue: null,\n      \n      // 日期选择器配置\n      datePickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期\n        }\n      }\n    }\n  },\n  \n  created() {\n    this.init()\n    this.getDataList()\n    this.getStats()\n  },\n  \n  methods: {\n    // 初始化\n    init() {\n      this.getChangdiTypesOptions()\n    },\n    \n    // 获取场地类型选项\n    getChangdiTypesOptions() {\n      this.$http({\n        url: 'dictionary/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 100,\n          dicCode: 'changdi_types'\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.changdiTypesOptions = data.data.list || []\n        }\n      })\n    },\n    \n    // 获取统计数据\n    getStats() {\n      // 获取总场地数\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalVenues = data.data.total || 0\n        }\n      })\n      \n      // 获取我的预约数（如果是用户登录）\n      const role = this.$storage.get('role')\n      if (role === '用户') {\n        this.$http({\n          url: 'changdiOrder/page',\n          method: 'get',\n          params: { page: 1, limit: 1 }\n        }).then(({ data }) => {\n          if (data && data.code === 0) {\n            this.myBookings = data.data.total || 0\n          }\n        })\n      }\n    },\n\n    // 获取场地列表\n    getDataList() {\n      this.dataListLoading = true\n      const params = {\n        page: this.pageIndex,\n        limit: this.pageSize,\n        shangxiaTypes: 1 // 只获取上架的场地\n      }\n\n      // 添加搜索条件\n      if (this.searchForm.changdiTypes) {\n        params.changdiTypes = this.searchForm.changdiTypes\n      }\n\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: params\n      }).then(({ data }) => {\n        this.dataListLoading = false\n        if (data && data.code === 0) {\n          this.dataList = data.data.list || []\n          this.totalPage = data.data.total || 0\n          this.availableSlots = this.dataList.length * 8 // 假设每个场地有8个时段\n        } else {\n          this.$message.error(data.msg || '获取场地列表失败')\n        }\n      }).catch(() => {\n        this.dataListLoading = false\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n\n    // 搜索场地\n    searchVenues() {\n      this.searchLoading = true\n      this.pageIndex = 1\n      setTimeout(() => {\n        this.getDataList()\n        this.searchLoading = false\n      }, 500)\n    },\n\n    // 重置搜索\n    resetSearch() {\n      this.searchForm = {\n        changdiTypes: '',\n        bookingDate: '',\n        priceRange: ''\n      }\n      this.pageIndex = 1\n      this.getDataList()\n    },\n\n    // 预约场地\n    bookVenue(venue) {\n      const role = this.$storage.get('role')\n      if (!role || role !== '用户') {\n        this.$message.warning('请先登录用户账户')\n        this.$router.push('/login')\n        return\n      }\n\n      // 暂时使用简单的预约逻辑\n      this.$confirm(`确定要预约场地\"${venue.changdiName}\"吗？`, '预约确认', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        // 这里可以调用预约API\n        this.$message.success('预约功能开发中，敬请期待！')\n      }).catch(() => {\n        this.$message.info('已取消预约')\n      })\n    },\n\n    // 查看场地详情\n    viewVenueDetails(venue) {\n      this.$alert(`\n        <div style=\"text-align: left;\">\n          <h3>${venue.changdiName}</h3>\n          <p><strong>场地编号：</strong>${venue.changdiUuidNumber}</p>\n          <p><strong>场地类型：</strong>${venue.changdiValue}</p>\n          <p><strong>价格：</strong>¥${venue.changdiNewMoney}</p>\n          <p><strong>时间段：</strong>${venue.shijianduan}</p>\n          <p><strong>半全场：</strong>${venue.banquanValue}</p>\n          <p><strong>推荐信息：</strong>${venue.tuijian || '暂无'}</p>\n        </div>\n      `, '场地详情', {\n        dangerouslyUseHTMLString: true,\n        confirmButtonText: '关闭'\n      })\n    },\n\n    // 预约成功回调\n    onBookingSuccess() {\n      this.$message.success('预约成功！')\n      this.getStats() // 刷新统计数据\n    },\n\n    // 分页处理\n    sizeChangeHandle(val) {\n      this.pageSize = val\n      this.pageIndex = 1\n      this.getDataList()\n    },\n\n    currentChangeHandle(val) {\n      this.pageIndex = val\n      this.getDataList()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.booking-container {\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: calc(100vh - 84px);\n\n  .page-header {\n    margin-bottom: 30px;\n\n    .header-content {\n      .title-section {\n        text-align: center;\n        margin-bottom: 30px;\n\n        h1 {\n          font-size: 32px;\n          color: #2c3e50;\n          margin: 0 0 10px 0;\n\n          i {\n            color: #00c292;\n            margin-right: 15px;\n          }\n        }\n\n        p {\n          color: #909399;\n          font-size: 18px;\n          margin: 0;\n        }\n      }\n\n      .stats-cards {\n        display: flex;\n        justify-content: center;\n        gap: 30px;\n\n        .stat-card {\n          background: white;\n          border-radius: 12px;\n          padding: 25px;\n          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n          display: flex;\n          align-items: center;\n          gap: 20px;\n          min-width: 200px;\n          transition: all 0.3s ease;\n\n          &:hover {\n            transform: translateY(-5px);\n            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n          }\n\n          .stat-icon {\n            width: 60px;\n            height: 60px;\n            border-radius: 50%;\n            background: linear-gradient(45deg, #00c292, #00a085);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n\n            i {\n              font-size: 24px;\n              color: white;\n            }\n          }\n\n          .stat-info {\n            h3 {\n              font-size: 28px;\n              font-weight: 700;\n              color: #2c3e50;\n              margin: 0 0 5px 0;\n            }\n\n            p {\n              color: #909399;\n              margin: 0;\n              font-size: 14px;\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .search-section {\n    margin-bottom: 30px;\n\n    .search-card {\n      border-radius: 12px;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n\n      .search-form {\n        .search-item {\n          label {\n            display: block;\n            margin-bottom: 8px;\n            font-weight: 600;\n            color: #2c3e50;\n            font-size: 14px;\n          }\n\n          ::v-deep .el-input__inner,\n          ::v-deep .el-select .el-input__inner {\n            border-radius: 8px;\n            border: 2px solid #e8f4f8;\n            transition: all 0.3s ease;\n\n            &:focus {\n              border-color: #00c292;\n              box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.1);\n            }\n          }\n        }\n\n        .search-actions {\n          display: flex;\n          gap: 10px;\n          align-items: end;\n          height: 100%;\n          padding-top: 22px;\n\n          .el-button {\n            border-radius: 8px;\n            font-weight: 600;\n\n            &.el-button--primary {\n              background: linear-gradient(45deg, #00c292, #00a085);\n              border: none;\n\n              &:hover {\n                background: linear-gradient(45deg, #00a085, #008f75);\n              }\n            }\n          }\n        }\n      }\n    }\n  }\n\n  .venues-section {\n    .section-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 25px;\n\n      h2 {\n        font-size: 24px;\n        color: #2c3e50;\n        margin: 0;\n      }\n\n      .view-toggle {\n        ::v-deep .el-radio-group {\n          .el-radio-button__inner {\n            border-radius: 8px;\n            border: 2px solid #e8f4f8;\n            background: white;\n            color: #666;\n\n            &:hover {\n              color: #00c292;\n              border-color: #00c292;\n            }\n          }\n\n          .el-radio-button__orig-radio:checked + .el-radio-button__inner {\n            background: #00c292;\n            border-color: #00c292;\n            color: white;\n          }\n        }\n      }\n    }\n\n    .venues-grid {\n      .empty-state {\n        text-align: center;\n        padding: 80px 20px;\n        color: #909399;\n\n        i {\n          font-size: 64px;\n          margin-bottom: 20px;\n          color: #ddd;\n        }\n\n        h3 {\n          font-size: 20px;\n          margin: 0 0 10px 0;\n        }\n\n        p {\n          margin: 0;\n          font-size: 14px;\n        }\n      }\n\n      .venue-cards {\n        display: grid;\n        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));\n        gap: 25px;\n\n        .venue-card {\n          background: white;\n          border-radius: 16px;\n          overflow: hidden;\n          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n          transition: all 0.3s ease;\n\n          &:hover {\n            transform: translateY(-8px);\n            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n          }\n\n          .venue-image {\n            position: relative;\n            height: 200px;\n            overflow: hidden;\n\n            img {\n              width: 100%;\n              height: 100%;\n              object-fit: cover;\n              transition: transform 0.3s ease;\n            }\n\n            &:hover img {\n              transform: scale(1.05);\n            }\n\n            .venue-status {\n              position: absolute;\n              top: 15px;\n              right: 15px;\n              padding: 6px 12px;\n              border-radius: 20px;\n              font-size: 12px;\n              font-weight: 600;\n\n              &.available {\n                background: rgba(0, 194, 146, 0.9);\n                color: white;\n              }\n\n              &.unavailable {\n                background: rgba(245, 108, 108, 0.9);\n                color: white;\n              }\n            }\n          }\n\n          .venue-info {\n            padding: 25px;\n\n            h3 {\n              font-size: 20px;\n              font-weight: 600;\n              color: #2c3e50;\n              margin: 0 0 15px 0;\n            }\n\n            .venue-details {\n              margin-bottom: 20px;\n\n              .detail-item {\n                display: flex;\n                align-items: center;\n                margin-bottom: 8px;\n                color: #666;\n                font-size: 14px;\n\n                i {\n                  color: #00c292;\n                  margin-right: 8px;\n                  width: 16px;\n                }\n              }\n            }\n\n            .venue-price {\n              margin-bottom: 20px;\n\n              .current-price {\n                font-size: 24px;\n                font-weight: 700;\n                color: #00c292;\n              }\n\n              .original-price {\n                font-size: 16px;\n                color: #999;\n                text-decoration: line-through;\n                margin-left: 10px;\n              }\n            }\n\n            .venue-actions {\n              display: flex;\n              gap: 10px;\n\n              .el-button {\n                border-radius: 8px;\n                font-weight: 600;\n\n                &.el-button--primary {\n                  background: linear-gradient(45deg, #00c292, #00a085);\n                  border: none;\n                  flex: 1;\n\n                  &:hover:not(:disabled) {\n                    background: linear-gradient(45deg, #00a085, #008f75);\n                  }\n\n                  &:disabled {\n                    background: #ddd;\n                    color: #999;\n                  }\n                }\n\n                &.el-button--text {\n                  color: #00c292;\n\n                  &:hover {\n                    color: #00a085;\n                  }\n                }\n              }\n            }\n          }\n        }\n      }\n    }\n\n    .venues-list {\n      background: white;\n      border-radius: 12px;\n      overflow: hidden;\n      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n\n      ::v-deep .el-table {\n        .price-cell {\n          .current-price {\n            font-size: 16px;\n            font-weight: 600;\n            color: #00c292;\n          }\n\n          .original-price {\n            font-size: 12px;\n            color: #999;\n            text-decoration: line-through;\n            display: block;\n          }\n        }\n\n        .el-button {\n          border-radius: 6px;\n          font-weight: 600;\n\n          &.el-button--primary {\n            background: linear-gradient(45deg, #00c292, #00a085);\n            border: none;\n\n            &:hover:not(:disabled) {\n              background: linear-gradient(45deg, #00a085, #008f75);\n            }\n          }\n        }\n      }\n    }\n\n    .pagination-wrapper {\n      margin-top: 30px;\n      text-align: center;\n\n      ::v-deep .el-pagination {\n        .el-pager li {\n          border-radius: 8px;\n          margin: 0 4px;\n\n          &.active {\n            background: #00c292;\n            border-color: #00c292;\n          }\n        }\n\n        .btn-prev,\n        .btn-next {\n          border-radius: 8px;\n        }\n      }\n    }\n  }\n}\n\n// 响应式设计\n@media (max-width: 1200px) {\n  .booking-container {\n    .page-header .header-content .stats-cards {\n      flex-direction: column;\n      align-items: center;\n      gap: 20px;\n    }\n\n    .venues-section .venue-cards {\n      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));\n    }\n  }\n}\n\n@media (max-width: 768px) {\n  .booking-container {\n    padding: 15px;\n\n    .search-section .search-form {\n      .el-row {\n        flex-direction: column;\n\n        .el-col {\n          width: 100%;\n          margin-bottom: 15px;\n        }\n      }\n    }\n\n    .venues-section {\n      .section-header {\n        flex-direction: column;\n        gap: 15px;\n        text-align: center;\n      }\n\n      .venue-cards {\n        grid-template-columns: 1fr;\n      }\n    }\n  }\n}\n</style>\n"]}]}