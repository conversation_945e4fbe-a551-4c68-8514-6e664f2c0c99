<template>
  <div class="home-container">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <el-alert :closable="false" title="欢迎使用体育馆管理平台" type="success">
        <template slot>
          <div>
            <p>您好，欢迎使用体育馆管理平台！今天是 {{ currentDate }}</p>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 轮播图 -->
    <HomeCarousel />

    <!-- 统计卡片 -->
    <HomeStats />

    <!-- 图表和卡片 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <div class="chart-container">
          <h3>数据统计</h3>
          <HomeChart />
        </div>
      </el-col>
      <el-col :span="8">
        <div class="card-container">
          <h3>快捷操作</h3>
          <HomeCard />
        </div>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import router from '@/router/router-static'
import HomeCarousel from '@/components/home/<USER>'
import HomeStats from '@/components/home/<USER>'
import HomeChart from '@/components/home/<USER>'
import HomeCard from '@/components/home/<USER>'

export default {
  components: {
    HomeCarousel,
    HomeStats,
    HomeChart,
    HomeCard
  },
  data() {
    return {
      currentDate: ''
    }
  },
  mounted(){
    this.init();
    this.getCurrentDate();
  },
  methods:{
    init(){
        if(this.$storage.get('Token')){
        this.$http({
            url: `${this.$storage.get('sessionTable')}/session`,
            method: "get"
        }).then(({ data }) => {
            if (data && data.code != 0) {
            router.push({ name: 'login' })
            }
        });
        }else{
            router.push({ name: 'login' })
        }
    },
    getCurrentDate() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      const weekday = weekdays[now.getDay()]
      this.currentDate = `${year}年${month}月${day}日 ${weekday}`
    }
  }
};
</script>

<style lang="scss" scoped>
.home-container {
  padding: 20px;

  .welcome-banner {
    margin-bottom: 20px;

    ::v-deep .el-alert {
      border-radius: 8px;
      border: none;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .el-alert__title {
        font-size: 18px;
        font-weight: bold;
      }

      .el-alert__content {
        font-size: 14px;
        margin-top: 8px;
      }
    }
  }

  .chart-container, .card-container {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: bold;
      color: #2c3e50;
      border-bottom: 2px solid #00c292;
      padding-bottom: 10px;
    }
  }
}
</style>
