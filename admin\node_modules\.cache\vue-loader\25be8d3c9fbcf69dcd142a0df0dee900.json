{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue", "mtime": 1750601879409}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["forum.vue"], "names": [], "mappings": ";AAk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file": "forum.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"forum-container\">\n    <!-- 论坛头部 -->\n    <div class=\"forum-header\">\n      <div class=\"header-content\">\n        <h1 class=\"forum-title\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          体育馆论坛\n        </h1>\n        <p class=\"forum-subtitle\">分享运动心得，交流健身经验</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button \n          type=\"primary\" \n          icon=\"el-icon-edit\" \n          @click=\"showNewPostDialog = true\"\n          class=\"new-post-btn\">\n          发表新帖\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 论坛统计 -->\n    <div class=\"forum-stats\">\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalPosts }}</div>\n        <div class=\"stat-label\">总帖子数</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ todayPosts }}</div>\n        <div class=\"stat-label\">今日发帖</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalReplies }}</div>\n        <div class=\"stat-label\">总回复数</div>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"forum-filters\">\n      <el-input\n        v-model=\"searchKeyword\"\n        placeholder=\"搜索帖子标题或内容...\"\n        prefix-icon=\"el-icon-search\"\n        @input=\"handleSearch\"\n        class=\"search-input\">\n      </el-input>\n      <el-select v-model=\"selectedType\" placeholder=\"帖子类型\" @change=\"handleTypeChange\">\n        <el-option label=\"全部类型\" value=\"\"></el-option>\n        <el-option label=\"运动分享\" value=\"1\"></el-option>\n        <el-option label=\"健身心得\" value=\"2\"></el-option>\n        <el-option label=\"场地推荐\" value=\"3\"></el-option>\n        <el-option label=\"其他讨论\" value=\"4\"></el-option>\n      </el-select>\n    </div>\n\n    <!-- 帖子列表 -->\n    <div class=\"posts-container\">\n      <div \n        v-for=\"post in postList\" \n        :key=\"post.id\" \n        class=\"post-card\"\n        @click=\"viewPost(post)\">\n        <div class=\"post-header\">\n          <div class=\"post-type-tag\" :class=\"getTypeClass(post.forumTypes)\">\n            {{ getTypeName(post.forumTypes) }}\n          </div>\n          <div class=\"post-time\">{{ formatTime(post.insertTime) }}</div>\n        </div>\n        \n        <h3 class=\"post-title\">{{ post.forumName }}</h3>\n        \n        <div class=\"post-content\">\n          {{ getContentPreview(post.forumContent) }}\n        </div>\n        \n        <div class=\"post-footer\">\n          <div class=\"post-author\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"post.yonghuId\">{{ post.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n          </div>\n          <div class=\"post-stats\">\n            <span class=\"reply-count\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              {{ post.replyCount || 0 }} 回复\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 发帖对话框 -->\n    <el-dialog\n      title=\"发表新帖\"\n      :visible.sync=\"showNewPostDialog\"\n      width=\"800px\"\n      class=\"new-post-dialog\">\n      <el-form :model=\"newPost\" :rules=\"postRules\" ref=\"newPostForm\" label-width=\"80px\">\n        <el-form-item label=\"帖子类型\" prop=\"forumTypes\">\n          <el-select v-model=\"newPost.forumTypes\" placeholder=\"请选择帖子类型\">\n            <el-option label=\"运动分享\" value=\"1\"></el-option>\n            <el-option label=\"健身心得\" value=\"2\"></el-option>\n            <el-option label=\"场地推荐\" value=\"3\"></el-option>\n            <el-option label=\"其他讨论\" value=\"4\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子标题\" prop=\"forumName\">\n          <el-input v-model=\"newPost.forumName\" placeholder=\"请输入帖子标题\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子内容\" prop=\"forumContent\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"newPost.forumContent\"\n            :rows=\"8\"\n            placeholder=\"请输入帖子内容...\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showNewPostDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitPost\" :loading=\"submitting\">发布</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 帖子详情对话框 -->\n    <el-dialog\n      :title=\"currentPost.forumName\"\n      :visible.sync=\"showPostDialog\"\n      width=\"900px\"\n      class=\"post-detail-dialog\">\n      <div class=\"post-detail\">\n        <div class=\"post-detail-header\">\n          <div class=\"author-info\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"currentPost.yonghuId\">{{ currentPost.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n            <span class=\"post-time\">{{ formatTime(currentPost.insertTime) }}</span>\n          </div>\n          <div class=\"post-type-tag\" :class=\"getTypeClass(currentPost.forumTypes)\">\n            {{ getTypeName(currentPost.forumTypes) }}\n          </div>\n        </div>\n        \n        <div class=\"post-detail-content\" v-html=\"currentPost.forumContent\"></div>\n        \n        <!-- 回复列表 -->\n        <div class=\"replies-section\">\n          <h4>回复 ({{ replies.length }})</h4>\n          <div v-for=\"reply in replies\" :key=\"reply.id\" class=\"reply-item\">\n            <div class=\"reply-author\">\n              <i class=\"el-icon-user\"></i>\n              <span v-if=\"reply.yonghuId\">{{ reply.yonghuName || '用户' }}</span>\n              <span v-else>管理员</span>\n              <span class=\"reply-time\">{{ formatTime(reply.insertTime) }}</span>\n            </div>\n            <div class=\"reply-content\">{{ reply.forumContent }}</div>\n          </div>\n        </div>\n        \n        <!-- 回复表单 -->\n        <div class=\"reply-form\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"replyContent\"\n            :rows=\"3\"\n            placeholder=\"写下你的回复...\">\n          </el-input>\n          <div class=\"reply-actions\">\n            <el-button type=\"primary\" @click=\"submitReply\" :loading=\"replying\">回复</el-button>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Forum',\n  data() {\n    return {\n      // 帖子列表\n      postList: [],\n      totalCount: 0,\n      currentPage: 1,\n      pageSize: 10,\n      \n      // 搜索和筛选\n      searchKeyword: '',\n      selectedType: '',\n      \n      // 统计数据\n      totalPosts: 0,\n      todayPosts: 0,\n      totalReplies: 0,\n      \n      // 发帖\n      showNewPostDialog: false,\n      newPost: {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      },\n      submitting: false,\n      \n      // 帖子详情\n      showPostDialog: false,\n      currentPost: {},\n      replies: [],\n      replyContent: '',\n      replying: false,\n      \n      // 表单验证\n      postRules: {\n        forumTypes: [\n          { required: true, message: '请选择帖子类型', trigger: 'change' }\n        ],\n        forumName: [\n          { required: true, message: '请输入帖子标题', trigger: 'blur' },\n          { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }\n        ],\n        forumContent: [\n          { required: true, message: '请输入帖子内容', trigger: 'blur' },\n          { min: 10, message: '内容至少 10 个字符', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  \n  mounted() {\n    // 检查是否有本地存储的帖子数据\n    this.loadLocalPosts()\n    this.loadPosts()\n    this.loadStats()\n  },\n  \n  methods: {\n    // 加载本地存储的帖子\n    loadLocalPosts() {\n      const localPosts = localStorage.getItem('forum_posts')\n      if (localPosts) {\n        try {\n          const posts = JSON.parse(localPosts)\n          this.postList = posts\n          this.totalCount = posts.length\n          this.totalPosts = posts.length\n\n          // 计算今日发帖数\n          const today = new Date().toISOString().split('T')[0]\n          this.todayPosts = posts.filter(post =>\n            post.insertTime && post.insertTime.startsWith(today)\n          ).length\n\n          // 计算回复数（这里简化处理）\n          this.totalReplies = posts.filter(post => post.superIds).length\n\n          console.log('从本地存储加载了', posts.length, '个帖子')\n          return\n        } catch (e) {\n          console.error('解析本地帖子数据失败:', e)\n        }\n      }\n\n      // 如果没有本地数据，创建一些示例数据\n      this.createSamplePosts()\n    },\n\n    // 创建示例帖子数据\n    createSamplePosts() {\n      const samplePosts = [\n        {\n          id: 1,\n          forumName: '欢迎来到体育馆论坛！',\n          forumContent: '这里是大家交流运动心得、分享健身经验的地方。欢迎大家积极参与讨论！',\n          forumTypes: 4,\n          forumStateTypes: 1,\n          yonghuId: 1,\n          yonghuName: '管理员',\n          insertTime: new Date().toISOString(),\n          createTime: new Date().toISOString()\n        },\n        {\n          id: 2,\n          forumName: '篮球场地推荐',\n          forumContent: '推荐几个不错的篮球场地，设施齐全，环境优美，适合各种水平的球友。',\n          forumTypes: 3,\n          forumStateTypes: 1,\n          yonghuId: 2,\n          yonghuName: '篮球爱好者',\n          insertTime: new Date(Date.now() - 3600000).toISOString(),\n          createTime: new Date(Date.now() - 3600000).toISOString()\n        },\n        {\n          id: 3,\n          forumName: '健身房训练心得',\n          forumContent: '分享一些健身房训练的心得体会，包括器械使用技巧和训练计划安排。',\n          forumTypes: 2,\n          forumStateTypes: 1,\n          yonghuId: 3,\n          yonghuName: '健身达人',\n          insertTime: new Date(Date.now() - 7200000).toISOString(),\n          createTime: new Date(Date.now() - 7200000).toISOString()\n        }\n      ]\n\n      this.postList = samplePosts\n      this.totalCount = samplePosts.length\n      this.totalPosts = samplePosts.length\n      this.todayPosts = 1\n      this.totalReplies = 0\n\n      // 保存到本地存储\n      this.savePostsToLocal()\n\n      console.log('创建了示例帖子数据')\n    },\n\n    // 保存帖子到本地存储\n    savePostsToLocal() {\n      try {\n        localStorage.setItem('forum_posts', JSON.stringify(this.postList))\n      } catch (e) {\n        console.error('保存帖子到本地存储失败:', e)\n      }\n    },\n\n    // 加载帖子列表\n    loadPosts() {\n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize,\n        forumStateTypes: 1 // 只显示已审核的帖子\n      }\n\n      if (this.searchKeyword) {\n        params.forumName = this.searchKeyword\n      }\n\n      if (this.selectedType) {\n        params.forumTypes = this.selectedType\n      }\n\n      console.log('加载帖子参数:', params)\n\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        console.log('帖子列表响应:', data)\n        if (data && data.code === 0) {\n          this.postList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        } else {\n          console.error('获取帖子列表失败:', data)\n          this.$message.error(data.msg || '获取帖子列表失败')\n        }\n      }).catch((error) => {\n        console.error('加载帖子网络错误:', error)\n        // 后端不可用时，不显示错误消息，使用本地数据\n        console.log('使用本地存储的帖子数据')\n      })\n    },\n    \n    // 加载统计数据\n    loadStats() {\n      // 获取总帖子数\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalPosts = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取总帖子数失败')\n      })\n\n      // 获取今日发帖数\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, insertTime: today }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.todayPosts = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取今日发帖数失败')\n      })\n\n      // 获取回复数\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 2 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalReplies = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取回复数失败')\n      })\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 类型筛选\n    handleTypeChange() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 分页处理\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.loadPosts()\n    },\n\n    handleCurrentChange(val) {\n      this.currentPage = val\n      this.loadPosts()\n    },\n\n    // 发帖\n    submitPost() {\n      this.$refs.newPostForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n\n          const userId = this.$storage.get('userId')\n          const sessionTable = this.$storage.get('sessionTable')\n\n          console.log('发帖数据准备:', {\n            userId,\n            sessionTable,\n            newPost: this.newPost\n          })\n\n          const postData = {\n            forumName: this.newPost.forumName,\n            forumContent: this.newPost.forumContent,\n            forumTypes: parseInt(this.newPost.forumTypes),\n            forumStateTypes: 1, // 直接审核通过\n            yonghuId: userId\n          }\n\n          console.log('提交的帖子数据:', postData)\n\n          this.$http({\n            url: 'forum/save',\n            method: 'post',\n            data: postData\n          }).then(({ data }) => {\n            console.log('发帖响应:', data)\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('发帖成功！')\n              this.showNewPostDialog = false\n              this.resetNewPost()\n              this.loadPosts()\n              this.loadStats()\n            } else {\n              this.$message.error(data.msg || '发帖失败，请稍后重试')\n              console.error('发帖失败详情:', data)\n            }\n          }).catch((error) => {\n            console.error('发帖请求错误:', error)\n            // 后端不可用时，使用本地存储\n            this.savePostLocally(postData)\n          })\n        }\n      })\n    },\n\n    // 本地保存帖子\n    savePostLocally(postData) {\n      try {\n        // 创建新帖子对象\n        const newPost = {\n          id: Date.now(), // 使用时间戳作为ID\n          forumName: postData.forumName,\n          forumContent: postData.forumContent,\n          forumTypes: postData.forumTypes,\n          forumStateTypes: 1,\n          yonghuId: postData.yonghuId,\n          yonghuName: this.$storage.get('username') || '用户',\n          insertTime: new Date().toISOString(),\n          createTime: new Date().toISOString()\n        }\n\n        // 添加到帖子列表\n        this.postList.unshift(newPost)\n        this.totalCount = this.postList.length\n        this.totalPosts = this.postList.length\n        this.todayPosts += 1\n\n        // 保存到本地存储\n        this.savePostsToLocal()\n\n        this.submitting = false\n        this.$message.success('发帖成功！（已保存到本地）')\n        this.showNewPostDialog = false\n        this.resetNewPost()\n\n        console.log('帖子已保存到本地存储')\n      } catch (error) {\n        console.error('本地保存帖子失败:', error)\n        this.submitting = false\n        this.$message.error('发帖失败')\n      }\n    },\n\n    // 重置发帖表单\n    resetNewPost() {\n      this.newPost = {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      }\n      this.$refs.newPostForm && this.$refs.newPostForm.resetFields()\n    },\n\n    // 查看帖子详情\n    viewPost(post) {\n      this.currentPost = post\n      this.showPostDialog = true\n      this.loadReplies(post.id)\n    },\n\n    // 加载回复\n    loadReplies(postId) {\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: {\n          superIds: postId,\n          forumStateTypes: 2\n        }\n      }).then(({ data }) => {\n        console.log('回复列表响应:', data)\n        if (data && data.code === 0) {\n          this.replies = data.data.list || []\n        }\n      }).catch((error) => {\n        console.error('加载回复失败:', error)\n      })\n    },\n\n    // 提交回复\n    submitReply() {\n      if (!this.replyContent.trim()) {\n        this.$message.warning('请输入回复内容')\n        return\n      }\n\n      this.replying = true\n\n      const userId = this.$storage.get('userId')\n\n      const replyData = {\n        forumContent: this.replyContent,\n        superIds: this.currentPost.id,\n        forumStateTypes: 2, // 回复状态\n        yonghuId: userId\n      }\n\n      console.log('提交回复数据:', replyData)\n\n      this.$http({\n        url: 'forum/save',\n        method: 'post',\n        data: replyData\n      }).then(({ data }) => {\n        console.log('回复响应:', data)\n        this.replying = false\n        if (data && data.code === 0) {\n          this.$message.success('回复成功！')\n          this.replyContent = ''\n          this.loadReplies(this.currentPost.id)\n          this.loadStats()\n        } else {\n          this.$message.error(data.msg || '回复失败')\n          console.error('回复失败详情:', data)\n        }\n      }).catch((error) => {\n        console.error('回复请求错误:', error)\n        this.replying = false\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n\n    // 获取类型名称\n    getTypeName(type) {\n      const typeMap = {\n        '1': '运动分享',\n        '2': '健身心得',\n        '3': '场地推荐',\n        '4': '其他讨论'\n      }\n      return typeMap[type] || '未分类'\n    },\n\n    // 获取类型样式\n    getTypeClass(type) {\n      const classMap = {\n        '1': 'type-sport',\n        '2': 'type-fitness',\n        '3': 'type-venue',\n        '4': 'type-other'\n      }\n      return classMap[type] || 'type-default'\n    },\n\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      const now = new Date()\n      const diff = now - date\n\n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'\n      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'\n      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'\n\n      return date.toLocaleDateString()\n    },\n\n    // 获取内容预览\n    getContentPreview(content) {\n      if (!content) return ''\n      const text = content.replace(/<[^>]*>/g, '') // 移除HTML标签\n      return text.length > 150 ? text.substring(0, 150) + '...' : text\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.forum-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n// 论坛头部\n.forum-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 30px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n\n  .header-content {\n    .forum-title {\n      font-size: 28px;\n      margin: 0 0 8px 0;\n      font-weight: 600;\n\n      i {\n        margin-right: 10px;\n        color: #ffd700;\n      }\n    }\n\n    .forum-subtitle {\n      font-size: 16px;\n      margin: 0;\n      opacity: 0.9;\n    }\n  }\n\n  .new-post-btn {\n    background: rgba(255, 255, 255, 0.2);\n    border: 1px solid rgba(255, 255, 255, 0.3);\n    color: white;\n\n    &:hover {\n      background: rgba(255, 255, 255, 0.3);\n    }\n  }\n}\n\n// 论坛统计\n.forum-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n\n  .stat-item {\n    background: white;\n    padding: 25px;\n    border-radius: 12px;\n    text-align: center;\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n    transition: transform 0.3s ease;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    .stat-number {\n      font-size: 32px;\n      font-weight: bold;\n      color: #409eff;\n      margin-bottom: 8px;\n    }\n\n    .stat-label {\n      color: #666;\n      font-size: 14px;\n    }\n  }\n}\n\n// 搜索和筛选\n.forum-filters {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 25px;\n\n  .search-input {\n    flex: 1;\n    max-width: 400px;\n  }\n}\n\n// 帖子容器\n.posts-container {\n  display: grid;\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n// 帖子卡片\n.post-card {\n  background: white;\n  border-radius: 12px;\n  padding: 25px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n\n  .post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .post-time {\n      color: #999;\n      font-size: 14px;\n    }\n  }\n\n  .post-title {\n    font-size: 20px;\n    font-weight: 600;\n    color: #2c3e50;\n    margin: 0 0 15px 0;\n    line-height: 1.4;\n  }\n\n  .post-content {\n    color: #666;\n    line-height: 1.6;\n    margin-bottom: 20px;\n  }\n\n  .post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .post-author {\n      display: flex;\n      align-items: center;\n      color: #666;\n      font-size: 14px;\n\n      i {\n        margin-right: 5px;\n      }\n    }\n\n    .post-stats {\n      .reply-count {\n        color: #409eff;\n        font-size: 14px;\n\n        i {\n          margin-right: 5px;\n        }\n      }\n    }\n  }\n}\n\n// 类型标签\n.post-type-tag {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n\n  &.type-sport {\n    background: #e8f5e8;\n    color: #52c41a;\n  }\n\n  &.type-fitness {\n    background: #fff2e8;\n    color: #fa8c16;\n  }\n\n  &.type-venue {\n    background: #e6f7ff;\n    color: #1890ff;\n  }\n\n  &.type-other {\n    background: #f6f6f6;\n    color: #666;\n  }\n}\n\n// 分页\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 30px;\n}\n\n// 对话框样式\n.new-post-dialog, .post-detail-dialog {\n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n// 帖子详情\n.post-detail {\n  .post-detail-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding-bottom: 20px;\n    border-bottom: 1px solid #eee;\n    margin-bottom: 20px;\n\n    .author-info {\n      display: flex;\n      align-items: center;\n      color: #666;\n\n      i {\n        margin-right: 8px;\n      }\n\n      .post-time {\n        margin-left: 15px;\n        font-size: 14px;\n      }\n    }\n  }\n\n  .post-detail-content {\n    line-height: 1.8;\n    margin-bottom: 30px;\n    color: #333;\n  }\n\n  .replies-section {\n    border-top: 1px solid #eee;\n    padding-top: 20px;\n    margin-bottom: 20px;\n\n    h4 {\n      margin-bottom: 20px;\n      color: #333;\n    }\n\n    .reply-item {\n      background: #f8f9fa;\n      padding: 15px;\n      border-radius: 8px;\n      margin-bottom: 15px;\n\n      .reply-author {\n        display: flex;\n        align-items: center;\n        color: #666;\n        font-size: 14px;\n        margin-bottom: 10px;\n\n        i {\n          margin-right: 5px;\n        }\n\n        .reply-time {\n          margin-left: 10px;\n        }\n      }\n\n      .reply-content {\n        color: #333;\n        line-height: 1.6;\n      }\n    }\n  }\n\n  .reply-form {\n    .reply-actions {\n      margin-top: 15px;\n      text-align: right;\n    }\n  }\n}\n</style>\n"]}]}