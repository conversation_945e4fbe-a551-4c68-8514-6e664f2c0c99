{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue", "mtime": 1750599350828}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["forum.vue"], "names": [], "mappings": ";AAkMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "forum.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"forum-container\">\n    <!-- 论坛头部 -->\n    <div class=\"forum-header\">\n      <div class=\"header-content\">\n        <h1 class=\"forum-title\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          体育馆论坛\n        </h1>\n        <p class=\"forum-subtitle\">分享运动心得，交流健身经验</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button \n          type=\"primary\" \n          icon=\"el-icon-edit\" \n          @click=\"showNewPostDialog = true\"\n          class=\"new-post-btn\">\n          发表新帖\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 论坛统计 -->\n    <div class=\"forum-stats\">\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalPosts }}</div>\n        <div class=\"stat-label\">总帖子数</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ todayPosts }}</div>\n        <div class=\"stat-label\">今日发帖</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalReplies }}</div>\n        <div class=\"stat-label\">总回复数</div>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"forum-filters\">\n      <el-input\n        v-model=\"searchKeyword\"\n        placeholder=\"搜索帖子标题或内容...\"\n        prefix-icon=\"el-icon-search\"\n        @input=\"handleSearch\"\n        class=\"search-input\">\n      </el-input>\n      <el-select v-model=\"selectedType\" placeholder=\"帖子类型\" @change=\"handleTypeChange\">\n        <el-option label=\"全部类型\" value=\"\"></el-option>\n        <el-option label=\"运动分享\" value=\"1\"></el-option>\n        <el-option label=\"健身心得\" value=\"2\"></el-option>\n        <el-option label=\"场地推荐\" value=\"3\"></el-option>\n        <el-option label=\"其他讨论\" value=\"4\"></el-option>\n      </el-select>\n    </div>\n\n    <!-- 帖子列表 -->\n    <div class=\"posts-container\">\n      <div \n        v-for=\"post in postList\" \n        :key=\"post.id\" \n        class=\"post-card\"\n        @click=\"viewPost(post)\">\n        <div class=\"post-header\">\n          <div class=\"post-type-tag\" :class=\"getTypeClass(post.forumTypes)\">\n            {{ getTypeName(post.forumTypes) }}\n          </div>\n          <div class=\"post-time\">{{ formatTime(post.insertTime) }}</div>\n        </div>\n        \n        <h3 class=\"post-title\">{{ post.forumName }}</h3>\n        \n        <div class=\"post-content\">\n          {{ getContentPreview(post.forumContent) }}\n        </div>\n        \n        <div class=\"post-footer\">\n          <div class=\"post-author\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"post.yonghuId\">{{ post.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n          </div>\n          <div class=\"post-stats\">\n            <span class=\"reply-count\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              {{ post.replyCount || 0 }} 回复\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 发帖对话框 -->\n    <el-dialog\n      title=\"发表新帖\"\n      :visible.sync=\"showNewPostDialog\"\n      width=\"800px\"\n      class=\"new-post-dialog\">\n      <el-form :model=\"newPost\" :rules=\"postRules\" ref=\"newPostForm\" label-width=\"80px\">\n        <el-form-item label=\"帖子类型\" prop=\"forumTypes\">\n          <el-select v-model=\"newPost.forumTypes\" placeholder=\"请选择帖子类型\">\n            <el-option label=\"运动分享\" value=\"1\"></el-option>\n            <el-option label=\"健身心得\" value=\"2\"></el-option>\n            <el-option label=\"场地推荐\" value=\"3\"></el-option>\n            <el-option label=\"其他讨论\" value=\"4\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子标题\" prop=\"forumName\">\n          <el-input v-model=\"newPost.forumName\" placeholder=\"请输入帖子标题\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子内容\" prop=\"forumContent\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"newPost.forumContent\"\n            :rows=\"8\"\n            placeholder=\"请输入帖子内容...\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showNewPostDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitPost\" :loading=\"submitting\">发布</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 帖子详情对话框 -->\n    <el-dialog\n      :title=\"currentPost.forumName\"\n      :visible.sync=\"showPostDialog\"\n      width=\"900px\"\n      class=\"post-detail-dialog\">\n      <div class=\"post-detail\">\n        <div class=\"post-detail-header\">\n          <div class=\"author-info\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"currentPost.yonghuId\">{{ currentPost.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n            <span class=\"post-time\">{{ formatTime(currentPost.insertTime) }}</span>\n          </div>\n          <div class=\"post-type-tag\" :class=\"getTypeClass(currentPost.forumTypes)\">\n            {{ getTypeName(currentPost.forumTypes) }}\n          </div>\n        </div>\n        \n        <div class=\"post-detail-content\" v-html=\"currentPost.forumContent\"></div>\n        \n        <!-- 回复列表 -->\n        <div class=\"replies-section\">\n          <h4>回复 ({{ replies.length }})</h4>\n          <div v-for=\"reply in replies\" :key=\"reply.id\" class=\"reply-item\">\n            <div class=\"reply-author\">\n              <i class=\"el-icon-user\"></i>\n              <span v-if=\"reply.yonghuId\">{{ reply.yonghuName || '用户' }}</span>\n              <span v-else>管理员</span>\n              <span class=\"reply-time\">{{ formatTime(reply.insertTime) }}</span>\n            </div>\n            <div class=\"reply-content\">{{ reply.forumContent }}</div>\n          </div>\n        </div>\n        \n        <!-- 回复表单 -->\n        <div class=\"reply-form\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"replyContent\"\n            :rows=\"3\"\n            placeholder=\"写下你的回复...\">\n          </el-input>\n          <div class=\"reply-actions\">\n            <el-button type=\"primary\" @click=\"submitReply\" :loading=\"replying\">回复</el-button>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Forum',\n  data() {\n    return {\n      // 帖子列表\n      postList: [],\n      totalCount: 0,\n      currentPage: 1,\n      pageSize: 10,\n      \n      // 搜索和筛选\n      searchKeyword: '',\n      selectedType: '',\n      \n      // 统计数据\n      totalPosts: 0,\n      todayPosts: 0,\n      totalReplies: 0,\n      \n      // 发帖\n      showNewPostDialog: false,\n      newPost: {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      },\n      submitting: false,\n      \n      // 帖子详情\n      showPostDialog: false,\n      currentPost: {},\n      replies: [],\n      replyContent: '',\n      replying: false,\n      \n      // 表单验证\n      postRules: {\n        forumTypes: [\n          { required: true, message: '请选择帖子类型', trigger: 'change' }\n        ],\n        forumName: [\n          { required: true, message: '请输入帖子标题', trigger: 'blur' },\n          { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }\n        ],\n        forumContent: [\n          { required: true, message: '请输入帖子内容', trigger: 'blur' },\n          { min: 10, message: '内容至少 10 个字符', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  \n  mounted() {\n    this.loadPosts()\n    this.loadStats()\n  },\n  \n  methods: {\n    // 加载帖子列表\n    loadPosts() {\n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize,\n        forumStateTypes: 1 // 只显示已审核的帖子\n      }\n      \n      if (this.searchKeyword) {\n        params.forumName = this.searchKeyword\n      }\n      \n      if (this.selectedType) {\n        params.forumTypes = this.selectedType\n      }\n      \n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.postList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        }\n      }).catch(() => {\n        this.$message.error('加载帖子失败')\n      })\n    },\n    \n    // 加载统计数据\n    loadStats() {\n      // 获取总帖子数\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalPosts = data.data.total || 0\n        }\n      })\n      \n      // 获取今日发帖数\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: { page: 1, limit: 1, insertTime: today }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.todayPosts = data.data.total || 0\n        }\n      })\n      // 获取回复数\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 2 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalReplies = data.data.total || 0\n        }\n      })\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 类型筛选\n    handleTypeChange() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 分页处理\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.loadPosts()\n    },\n\n    handleCurrentChange(val) {\n      this.currentPage = val\n      this.loadPosts()\n    },\n\n    // 发帖\n    submitPost() {\n      this.$refs.newPostForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n\n          const userId = this.$storage.get('userId')\n          const sessionTable = this.$storage.get('sessionTable')\n\n          console.log('发帖数据准备:', {\n            userId,\n            sessionTable,\n            newPost: this.newPost\n          })\n\n          const postData = {\n            forumName: this.newPost.forumName,\n            forumContent: this.newPost.forumContent,\n            forumTypes: parseInt(this.newPost.forumTypes),\n            forumStateTypes: 1, // 直接审核通过\n            yonghuId: userId\n          }\n\n          console.log('提交的帖子数据:', postData)\n\n          this.$http({\n            url: 'forum/save',\n            method: 'post',\n            data: postData\n          }).then(({ data }) => {\n            console.log('发帖响应:', data)\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('发帖成功！')\n              this.showNewPostDialog = false\n              this.resetNewPost()\n              this.loadPosts()\n              this.loadStats()\n            } else {\n              this.$message.error(data.msg || '发帖失败，请稍后重试')\n              console.error('发帖失败详情:', data)\n            }\n          }).catch((error) => {\n            console.error('发帖请求错误:', error)\n            this.submitting = false\n            this.$message.error('网络错误，请稍后重试')\n          })\n        }\n      })\n    },\n\n    // 重置发帖表单\n    resetNewPost() {\n      this.newPost = {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      }\n      this.$refs.newPostForm && this.$refs.newPostForm.resetFields()\n    },\n\n    // 查看帖子详情\n    viewPost(post) {\n      this.currentPost = post\n      this.showPostDialog = true\n      this.loadReplies(post.id)\n    },\n\n    // 加载回复\n    loadReplies(postId) {\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: {\n          superIds: postId,\n          forumStateTypes: 2\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.replies = data.data.list || []\n        }\n      })\n    },\n\n    // 提交回复\n    submitReply() {\n      if (!this.replyContent.trim()) {\n        this.$message.warning('请输入回复内容')\n        return\n      }\n\n      this.replying = true\n\n      const userId = this.$storage.get('userId')\n\n      const replyData = {\n        forumContent: this.replyContent,\n        superIds: this.currentPost.id,\n        forumStateTypes: 2, // 回复状态\n        yonghuId: userId\n      }\n\n      console.log('提交回复数据:', replyData)\n\n      this.$http({\n        url: 'forum/save',\n        method: 'post',\n        data: replyData\n      }).then(({ data }) => {\n        console.log('回复响应:', data)\n        this.replying = false\n        if (data && data.code === 0) {\n          this.$message.success('回复成功！')\n          this.replyContent = ''\n          this.loadReplies(this.currentPost.id)\n          this.loadStats()\n        } else {\n          this.$message.error(data.msg || '回复失败')\n          console.error('回复失败详情:', data)\n        }\n      }).catch((error) => {\n        console.error('回复请求错误:', error)\n        this.replying = false\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n\n    // 获取类型名称\n    getTypeName(type) {\n      const typeMap = {\n        '1': '运动分享',\n        '2': '健身心得',\n        '3': '场地推荐',\n        '4': '其他讨论'\n      }\n      return typeMap[type] || '未分类'\n    },\n\n    // 获取类型样式\n    getTypeClass(type) {\n      const classMap = {\n        '1': 'type-sport',\n        '2': 'type-fitness',\n        '3': 'type-venue',\n        '4': 'type-other'\n      }\n      return classMap[type] || 'type-default'\n    },\n\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      const now = new Date()\n      const diff = now - date\n\n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'\n      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'\n      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'\n\n      return date.toLocaleDateString()\n    },\n\n    // 获取内容预览\n    getContentPreview(content) {\n      if (!content) return ''\n      const text = content.replace(/<[^>]*>/g, '') // 移除HTML标签\n      return text.length > 150 ? text.substring(0, 150) + '...' : text\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.forum-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n// 论坛头部\n.forum-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 30px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n\n  .header-content {\n    .forum-title {\n      font-size: 28px;\n      margin: 0 0 8px 0;\n      font-weight: 600;\n\n      i {\n        margin-right: 10px;\n        color: #ffd700;\n      }\n    }\n\n    .forum-subtitle {\n      font-size: 16px;\n      margin: 0;\n      opacity: 0.9;\n    }\n  }\n\n  .new-post-btn {\n    background: rgba(255, 255, 255, 0.2);\n    border: 1px solid rgba(255, 255, 255, 0.3);\n    color: white;\n\n    &:hover {\n      background: rgba(255, 255, 255, 0.3);\n    }\n  }\n}\n\n// 论坛统计\n.forum-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n\n  .stat-item {\n    background: white;\n    padding: 25px;\n    border-radius: 12px;\n    text-align: center;\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n    transition: transform 0.3s ease;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    .stat-number {\n      font-size: 32px;\n      font-weight: bold;\n      color: #409eff;\n      margin-bottom: 8px;\n    }\n\n    .stat-label {\n      color: #666;\n      font-size: 14px;\n    }\n  }\n}\n\n// 搜索和筛选\n.forum-filters {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 25px;\n\n  .search-input {\n    flex: 1;\n    max-width: 400px;\n  }\n}\n\n// 帖子容器\n.posts-container {\n  display: grid;\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n// 帖子卡片\n.post-card {\n  background: white;\n  border-radius: 12px;\n  padding: 25px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n\n  .post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .post-time {\n      color: #999;\n      font-size: 14px;\n    }\n  }\n\n  .post-title {\n    font-size: 20px;\n    font-weight: 600;\n    color: #2c3e50;\n    margin: 0 0 15px 0;\n    line-height: 1.4;\n  }\n\n  .post-content {\n    color: #666;\n    line-height: 1.6;\n    margin-bottom: 20px;\n  }\n\n  .post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .post-author {\n      display: flex;\n      align-items: center;\n      color: #666;\n      font-size: 14px;\n\n      i {\n        margin-right: 5px;\n      }\n    }\n\n    .post-stats {\n      .reply-count {\n        color: #409eff;\n        font-size: 14px;\n\n        i {\n          margin-right: 5px;\n        }\n      }\n    }\n  }\n}\n\n// 类型标签\n.post-type-tag {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n\n  &.type-sport {\n    background: #e8f5e8;\n    color: #52c41a;\n  }\n\n  &.type-fitness {\n    background: #fff2e8;\n    color: #fa8c16;\n  }\n\n  &.type-venue {\n    background: #e6f7ff;\n    color: #1890ff;\n  }\n\n  &.type-other {\n    background: #f6f6f6;\n    color: #666;\n  }\n}\n\n// 分页\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 30px;\n}\n\n// 对话框样式\n.new-post-dialog, .post-detail-dialog {\n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n// 帖子详情\n.post-detail {\n  .post-detail-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding-bottom: 20px;\n    border-bottom: 1px solid #eee;\n    margin-bottom: 20px;\n\n    .author-info {\n      display: flex;\n      align-items: center;\n      color: #666;\n\n      i {\n        margin-right: 8px;\n      }\n\n      .post-time {\n        margin-left: 15px;\n        font-size: 14px;\n      }\n    }\n  }\n\n  .post-detail-content {\n    line-height: 1.8;\n    margin-bottom: 30px;\n    color: #333;\n  }\n\n  .replies-section {\n    border-top: 1px solid #eee;\n    padding-top: 20px;\n    margin-bottom: 20px;\n\n    h4 {\n      margin-bottom: 20px;\n      color: #333;\n    }\n\n    .reply-item {\n      background: #f8f9fa;\n      padding: 15px;\n      border-radius: 8px;\n      margin-bottom: 15px;\n\n      .reply-author {\n        display: flex;\n        align-items: center;\n        color: #666;\n        font-size: 14px;\n        margin-bottom: 10px;\n\n        i {\n          margin-right: 5px;\n        }\n\n        .reply-time {\n          margin-left: 10px;\n        }\n      }\n\n      .reply-content {\n        color: #333;\n        line-height: 1.6;\n      }\n    }\n  }\n\n  .reply-form {\n    .reply-actions {\n      margin-top: 15px;\n      text-align: right;\n    }\n  }\n}\n</style>\n"]}]}