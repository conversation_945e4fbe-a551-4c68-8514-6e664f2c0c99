<template>
  <div class="center-container">
    <div class="center-header">
      <h2>个人信息管理</h2>
      <p>管理您的个人资料和账户信息</p>
    </div>

    <el-card class="center-card">
      <div slot="header">
        <span>基本信息</span>
      </div>

      <el-form
        class="center-form"
        ref="ruleForm"
        :model="ruleForm"
        :rules="rules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <!-- 用户信息字段 -->
          <el-col :span="12" v-if="flag=='yonghu'">
            <el-form-item label="用户姓名" prop="yonghuName">
              <el-input
                v-model="ruleForm.yonghuName"
                placeholder="请输入用户姓名"
                clearable
                prefix-icon="el-icon-user">
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="flag=='yonghu'">
            <el-form-item label="用户手机号" prop="yonghuPhone">
              <el-input
                v-model="ruleForm.yonghuPhone"
                placeholder="请输入手机号"
                clearable
                prefix-icon="el-icon-phone">
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="flag=='yonghu'">
            <el-form-item label="身份证号" prop="yonghuIdNumber">
              <el-input
                v-model="ruleForm.yonghuIdNumber"
                placeholder="请输入身份证号"
                clearable
                prefix-icon="el-icon-postcard">
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="flag=='yonghu'">
            <el-form-item label="电子邮箱" prop="yonghuEmail">
              <el-input
                v-model="ruleForm.yonghuEmail"
                placeholder="请输入电子邮箱"
                clearable
                prefix-icon="el-icon-message">
              </el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="flag!='users'">
            <el-form-item label="性别" prop="sexTypes">
              <el-select v-model="ruleForm.sexTypes" placeholder="请选择性别" style="width: 100%">
                <el-option
                  v-for="(item,index) in sexTypesOptions"
                  v-bind:key="item.codeIndex"
                  :label="item.indexName"
                  :value="item.codeIndex">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <!-- 管理员用户名字段 -->
          <el-col :span="12" v-if="flag=='users'">
            <el-form-item label="用户名" prop="username">
              <el-input
                v-model="ruleForm.username"
                placeholder="请输入用户名"
                prefix-icon="el-icon-user">
              </el-input>
            </el-form-item>
          </el-col>

          <!-- 用户头像上传 -->
          <el-col :span="24" v-if="flag=='yonghu'">
            <el-form-item label="用户头像" prop="yonghuPhoto">
              <file-upload
                tip="点击上传头像照片"
                action="file/upload"
                :limit="1"
                :multiple="false"
                :fileUrls="ruleForm.yonghuPhoto?ruleForm.yonghuPhoto:''"
                @change="yonghuPhotoUploadChange"
              ></file-upload>
            </el-form-item>
          </el-col>

          <!-- 操作按钮 -->
          <el-col :span="24">
            <el-form-item>
              <el-button type="primary" @click="onUpdateHandler" :loading="loading">
                <i class="el-icon-check"></i>
                保存修改
              </el-button>
              <el-button @click="resetForm">
                <i class="el-icon-refresh"></i>
                重置
              </el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 密码修改卡片 -->
    <el-card class="center-card" style="margin-top: 20px;">
      <div slot="header">
        <span>安全设置</span>
      </div>
      <el-button type="warning" @click="goToPasswordChange">
        <i class="el-icon-key"></i>
        修改密码
      </el-button>
    </el-card>
  </div>
</template>
<script>
// 数字，邮件，手机，url，身份证校验
import { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from "@/utils/validate";

export default {
  data() {
    return {
      ruleForm: {},
      flag: '',
      usersFlag: false,
      sexTypesOptions: [],
      loading: false,
      rules: {
        yonghuName: [
          { required: true, message: '请输入用户姓名', trigger: 'blur' }
        ],
        yonghuPhone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { validator: this.validatePhone, trigger: 'blur' }
        ],
        yonghuIdNumber: [
          { required: true, message: '请输入身份证号', trigger: 'blur' },
          { validator: this.validateIdCard, trigger: 'blur' }
        ],
        yonghuEmail: [
          { validator: this.validateEmail, trigger: 'blur' }
        ],
        sexTypes: [
          { required: true, message: '请选择性别', trigger: 'change' }
        ],
        username: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
        ]
      }
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    init() {
      // 获取当前登录用户的信息
      var table = this.$storage.get("sessionTable");
      this.sessionTable = this.$storage.get("sessionTable");
      this.role = this.$storage.get("role");
      this.flag = table;

      // 获取用户信息
      this.$http({
        url: `${this.$storage.get("sessionTable")}/session`,
        method: "get"
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.ruleForm = data.data;
        } else {
          this.$message.error(data.msg);
        }
      });

      // 获取性别选项
      this.$http({
        url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=sex_types`,
        method: "get"
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.sexTypesOptions = data.data.list;
        } else {
          this.$message.error(data.msg);
        }
      });
    },

    // 表单验证方法
    validatePhone(rule, value, callback) {
      if (value && !isMobile(value)) {
        callback(new Error('请输入正确的手机号格式'));
      } else {
        callback();
      }
    },

    validateIdCard(rule, value, callback) {
      if (value && !checkIdCard(value)) {
        callback(new Error('请输入正确的身份证号格式'));
      } else {
        callback();
      }
    },

    validateEmail(rule, value, callback) {
      if (value && !isEmail(value)) {
        callback(new Error('请输入正确的邮箱格式'));
      } else {
        callback();
      }
    },

    yonghuPhotoUploadChange(fileUrls) {
      this.ruleForm.yonghuPhoto = fileUrls;
    },

    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.init();
    },

    goToPasswordChange() {
      this.$router.push('/updatePassword');
    },

    onUpdateHandler() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          this.loading = true;
          this.$http({
            url: `${this.$storage.get("sessionTable")}/update`,
            method: "post",
            data: this.ruleForm
          }).then(({ data }) => {
            this.loading = false;
            if (data && data.code === 0) {
              this.$message({
                message: "修改信息成功",
                type: "success",
                duration: 1500
              });
            } else {
              this.$message.error(data.msg);
            }
          }).catch(() => {
            this.loading = false;
          });
        } else {
          this.$message.error('请检查表单信息');
          return false;
        }
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.center-container {
  padding: 20px;

  .center-header {
    text-align: center;
    margin-bottom: 30px;

    h2 {
      font-size: 28px;
      color: #2c3e50;
      margin-bottom: 10px;
    }

    p {
      font-size: 16px;
      color: #909399;
    }
  }

  .center-card {
    margin-bottom: 20px;

    ::v-deep .el-card__header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      span {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .center-form {
      padding: 20px 0;

      .el-form-item {
        margin-bottom: 25px;

        ::v-deep .el-form-item__label {
          font-weight: 600;
          color: #2c3e50;
        }

        ::v-deep .el-input__inner {
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;

          &:focus {
            border-color: #00c292;
            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);
          }
        }

        ::v-deep .el-select {
          width: 100%;
        }
      }

      .el-button {
        border-radius: 6px;
        padding: 12px 24px;
        font-weight: 600;

        &.el-button--primary {
          background: linear-gradient(135deg, #00c292 0%, #00a085 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #00a085 0%, #008f75 100%);
          }
        }

        &.el-button--warning {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
          border: none;
          color: white;

          &:hover {
            background: linear-gradient(135deg, #f5576c 0%, #e74c3c 100%);
          }
        }
      }
    }
  }
}

::v-deep .el-upload {
  .el-upload-dragger {
    border-radius: 8px;
    border: 2px dashed #d9d9d9;
    transition: all 0.3s ease;

    &:hover {
      border-color: #00c292;
    }
  }
}
</style>

