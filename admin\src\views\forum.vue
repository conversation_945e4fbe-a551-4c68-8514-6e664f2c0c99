<template>
  <div class="forum-container">
    <!-- 论坛头部 -->
    <div class="forum-header">
      <div class="header-content">
        <h1 class="forum-title">
          <i class="el-icon-chat-dot-round"></i>
          体育馆论坛
        </h1>
        <p class="forum-subtitle">分享运动心得，交流健身经验</p>
      </div>
      <div class="header-actions">
        <el-button 
          type="primary" 
          icon="el-icon-edit" 
          @click="showNewPostDialog = true"
          class="new-post-btn">
          发表新帖
        </el-button>
      </div>
    </div>

    <!-- 论坛统计 -->
    <div class="forum-stats">
      <div class="stat-item">
        <div class="stat-number">{{ totalPosts }}</div>
        <div class="stat-label">总帖子数</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ todayPosts }}</div>
        <div class="stat-label">今日发帖</div>
      </div>
      <div class="stat-item">
        <div class="stat-number">{{ totalReplies }}</div>
        <div class="stat-label">总回复数</div>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="forum-filters">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索帖子标题或内容..."
        prefix-icon="el-icon-search"
        @input="handleSearch"
        class="search-input">
      </el-input>
      <el-select v-model="selectedType" placeholder="帖子类型" @change="handleTypeChange">
        <el-option label="全部类型" value=""></el-option>
        <el-option label="运动分享" value="1"></el-option>
        <el-option label="健身心得" value="2"></el-option>
        <el-option label="场地推荐" value="3"></el-option>
        <el-option label="其他讨论" value="4"></el-option>
      </el-select>
    </div>

    <!-- 帖子列表 -->
    <div class="posts-container">
      <div 
        v-for="post in postList" 
        :key="post.id" 
        class="post-card"
        @click="viewPost(post)">
        <div class="post-header">
          <div class="post-type-tag" :class="getTypeClass(post.forumTypes)">
            {{ getTypeName(post.forumTypes) }}
          </div>
          <div class="post-time">{{ formatTime(post.insertTime) }}</div>
        </div>
        
        <h3 class="post-title">{{ post.forumName }}</h3>
        
        <div class="post-content">
          {{ getContentPreview(post.forumContent) }}
        </div>
        
        <div class="post-footer">
          <div class="post-author">
            <i class="el-icon-user"></i>
            <span v-if="post.yonghuId">{{ post.yonghuName || '用户' }}</span>
            <span v-else>管理员</span>
          </div>
          <div class="post-stats">
            <span class="reply-count">
              <i class="el-icon-chat-dot-round"></i>
              {{ post.replyCount || 0 }} 回复
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-container">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50]"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalCount">
      </el-pagination>
    </div>

    <!-- 发帖对话框 -->
    <el-dialog
      title="发表新帖"
      :visible.sync="showNewPostDialog"
      width="800px"
      class="new-post-dialog">
      <el-form :model="newPost" :rules="postRules" ref="newPostForm" label-width="80px">
        <el-form-item label="帖子类型" prop="forumTypes">
          <el-select v-model="newPost.forumTypes" placeholder="请选择帖子类型">
            <el-option label="运动分享" value="1"></el-option>
            <el-option label="健身心得" value="2"></el-option>
            <el-option label="场地推荐" value="3"></el-option>
            <el-option label="其他讨论" value="4"></el-option>
          </el-select>
        </el-form-item>
        
        <el-form-item label="帖子标题" prop="forumName">
          <el-input v-model="newPost.forumName" placeholder="请输入帖子标题"></el-input>
        </el-form-item>
        
        <el-form-item label="帖子内容" prop="forumContent">
          <el-input
            type="textarea"
            v-model="newPost.forumContent"
            :rows="8"
            placeholder="请输入帖子内容...">
          </el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="showNewPostDialog = false">取消</el-button>
        <el-button type="primary" @click="submitPost" :loading="submitting">发布</el-button>
      </div>
    </el-dialog>

    <!-- 帖子详情对话框 -->
    <el-dialog
      :title="currentPost.forumName"
      :visible.sync="showPostDialog"
      width="900px"
      class="post-detail-dialog">
      <div class="post-detail">
        <div class="post-detail-header">
          <div class="author-info">
            <i class="el-icon-user"></i>
            <span v-if="currentPost.yonghuId">{{ currentPost.yonghuName || '用户' }}</span>
            <span v-else>管理员</span>
            <span class="post-time">{{ formatTime(currentPost.insertTime) }}</span>
          </div>
          <div class="post-type-tag" :class="getTypeClass(currentPost.forumTypes)">
            {{ getTypeName(currentPost.forumTypes) }}
          </div>
        </div>
        
        <div class="post-detail-content" v-html="currentPost.forumContent"></div>
        
        <!-- 回复列表 -->
        <div class="replies-section">
          <h4>回复 ({{ replies.length }})</h4>
          <div v-for="reply in replies" :key="reply.id" class="reply-item">
            <div class="reply-author">
              <i class="el-icon-user"></i>
              <span v-if="reply.yonghuId">{{ reply.yonghuName || '用户' }}</span>
              <span v-else>管理员</span>
              <span class="reply-time">{{ formatTime(reply.insertTime) }}</span>
            </div>
            <div class="reply-content">{{ reply.forumContent }}</div>
          </div>
        </div>
        
        <!-- 回复表单 -->
        <div class="reply-form">
          <el-input
            type="textarea"
            v-model="replyContent"
            :rows="3"
            placeholder="写下你的回复...">
          </el-input>
          <div class="reply-actions">
            <el-button type="primary" @click="submitReply" :loading="replying">回复</el-button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Forum',
  data() {
    return {
      // 帖子列表
      postList: [],
      totalCount: 0,
      currentPage: 1,
      pageSize: 10,
      
      // 搜索和筛选
      searchKeyword: '',
      selectedType: '',
      
      // 统计数据
      totalPosts: 0,
      todayPosts: 0,
      totalReplies: 0,
      
      // 发帖
      showNewPostDialog: false,
      newPost: {
        forumName: '',
        forumContent: '',
        forumTypes: ''
      },
      submitting: false,
      
      // 帖子详情
      showPostDialog: false,
      currentPost: {},
      replies: [],
      replyContent: '',
      replying: false,
      
      // 表单验证
      postRules: {
        forumTypes: [
          { required: true, message: '请选择帖子类型', trigger: 'change' }
        ],
        forumName: [
          { required: true, message: '请输入帖子标题', trigger: 'blur' },
          { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }
        ],
        forumContent: [
          { required: true, message: '请输入帖子内容', trigger: 'blur' },
          { min: 10, message: '内容至少 10 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  
  mounted() {
    this.loadPosts()
    this.loadStats()
  },
  
  methods: {
    // 加载帖子列表
    loadPosts() {
      const params = {
        page: this.currentPage,
        limit: this.pageSize,
        forumStateTypes: 1 // 只显示已审核的帖子
      }
      
      if (this.searchKeyword) {
        params.forumName = this.searchKeyword
      }
      
      if (this.selectedType) {
        params.forumTypes = this.selectedType
      }
      
      this.$http({
        url: 'forum/page',
        method: 'get',
        params
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.postList = data.data.list || []
          this.totalCount = data.data.total || 0
        }
      }).catch(() => {
        this.$message.error('加载帖子失败')
      })
    },
    
    // 加载统计数据
    loadStats() {
      // 获取总帖子数
      this.$http({
        url: 'forum/page',
        method: 'get',
        params: { page: 1, limit: 1, forumStateTypes: 1 }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.totalPosts = data.data.total || 0
        }
      })
      
      // 获取今日发帖数
      const today = new Date().toISOString().split('T')[0]
      this.$http({
        url: 'forum/page',
        method: 'get',
        params: { page: 1, limit: 1, insertTime: today }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.todayPosts = data.data.total || 0
        }
      })
      // 获取回复数
      this.$http({
        url: 'forum/page',
        method: 'get',
        params: { page: 1, limit: 1, forumStateTypes: 2 }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.totalReplies = data.data.total || 0
        }
      })
    },

    // 搜索处理
    handleSearch() {
      this.currentPage = 1
      this.loadPosts()
    },

    // 类型筛选
    handleTypeChange() {
      this.currentPage = 1
      this.loadPosts()
    },

    // 分页处理
    handleSizeChange(val) {
      this.pageSize = val
      this.loadPosts()
    },

    handleCurrentChange(val) {
      this.currentPage = val
      this.loadPosts()
    },

    // 发帖
    submitPost() {
      this.$refs.newPostForm.validate((valid) => {
        if (valid) {
          this.submitting = true

          const userId = this.$storage.get('userId')
          const sessionTable = this.$storage.get('sessionTable')

          console.log('发帖数据准备:', {
            userId,
            sessionTable,
            newPost: this.newPost
          })

          const postData = {
            forumName: this.newPost.forumName,
            forumContent: this.newPost.forumContent,
            forumTypes: parseInt(this.newPost.forumTypes),
            forumStateTypes: 1, // 直接审核通过
            yonghuId: userId
          }

          console.log('提交的帖子数据:', postData)

          this.$http({
            url: 'forum/save',
            method: 'post',
            data: postData
          }).then(({ data }) => {
            console.log('发帖响应:', data)
            this.submitting = false
            if (data && data.code === 0) {
              this.$message.success('发帖成功！')
              this.showNewPostDialog = false
              this.resetNewPost()
              this.loadPosts()
              this.loadStats()
            } else {
              this.$message.error(data.msg || '发帖失败，请稍后重试')
              console.error('发帖失败详情:', data)
            }
          }).catch((error) => {
            console.error('发帖请求错误:', error)
            this.submitting = false
            this.$message.error('网络错误，请稍后重试')
          })
        }
      })
    },

    // 重置发帖表单
    resetNewPost() {
      this.newPost = {
        forumName: '',
        forumContent: '',
        forumTypes: ''
      }
      this.$refs.newPostForm && this.$refs.newPostForm.resetFields()
    },

    // 查看帖子详情
    viewPost(post) {
      this.currentPost = post
      this.showPostDialog = true
      this.loadReplies(post.id)
    },

    // 加载回复
    loadReplies(postId) {
      this.$http({
        url: 'forum/page',
        method: 'get',
        params: {
          superIds: postId,
          forumStateTypes: 2
        }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.replies = data.data.list || []
        }
      })
    },

    // 提交回复
    submitReply() {
      if (!this.replyContent.trim()) {
        this.$message.warning('请输入回复内容')
        return
      }

      this.replying = true

      const userId = this.$storage.get('userId')

      const replyData = {
        forumContent: this.replyContent,
        superIds: this.currentPost.id,
        forumStateTypes: 2, // 回复状态
        yonghuId: userId
      }

      console.log('提交回复数据:', replyData)

      this.$http({
        url: 'forum/save',
        method: 'post',
        data: replyData
      }).then(({ data }) => {
        console.log('回复响应:', data)
        this.replying = false
        if (data && data.code === 0) {
          this.$message.success('回复成功！')
          this.replyContent = ''
          this.loadReplies(this.currentPost.id)
          this.loadStats()
        } else {
          this.$message.error(data.msg || '回复失败')
          console.error('回复失败详情:', data)
        }
      }).catch((error) => {
        console.error('回复请求错误:', error)
        this.replying = false
        this.$message.error('网络错误，请稍后重试')
      })
    },

    // 获取类型名称
    getTypeName(type) {
      const typeMap = {
        '1': '运动分享',
        '2': '健身心得',
        '3': '场地推荐',
        '4': '其他讨论'
      }
      return typeMap[type] || '未分类'
    },

    // 获取类型样式
    getTypeClass(type) {
      const classMap = {
        '1': 'type-sport',
        '2': 'type-fitness',
        '3': 'type-venue',
        '4': 'type-other'
      }
      return classMap[type] || 'type-default'
    },

    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'

      return date.toLocaleDateString()
    },

    // 获取内容预览
    getContentPreview(content) {
      if (!content) return ''
      const text = content.replace(/<[^>]*>/g, '') // 移除HTML标签
      return text.length > 150 ? text.substring(0, 150) + '...' : text
    }
  }
}
</script>

<style lang="scss" scoped>
.forum-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

// 论坛头部
.forum-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-radius: 12px;
  margin-bottom: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  .header-content {
    .forum-title {
      font-size: 28px;
      margin: 0 0 8px 0;
      font-weight: 600;

      i {
        margin-right: 10px;
        color: #ffd700;
      }
    }

    .forum-subtitle {
      font-size: 16px;
      margin: 0;
      opacity: 0.9;
    }
  }

  .new-post-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;

    &:hover {
      background: rgba(255, 255, 255, 0.3);
    }
  }
}

// 论坛统计
.forum-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;

  .stat-item {
    background: white;
    padding: 25px;
    border-radius: 12px;
    text-align: center;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;

    &:hover {
      transform: translateY(-2px);
    }

    .stat-number {
      font-size: 32px;
      font-weight: bold;
      color: #409eff;
      margin-bottom: 8px;
    }

    .stat-label {
      color: #666;
      font-size: 14px;
    }
  }
}

// 搜索和筛选
.forum-filters {
  display: flex;
  gap: 15px;
  margin-bottom: 25px;

  .search-input {
    flex: 1;
    max-width: 400px;
  }
}

// 帖子容器
.posts-container {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

// 帖子卡片
.post-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  }

  .post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;

    .post-time {
      color: #999;
      font-size: 14px;
    }
  }

  .post-title {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 15px 0;
    line-height: 1.4;
  }

  .post-content {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
  }

  .post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .post-author {
      display: flex;
      align-items: center;
      color: #666;
      font-size: 14px;

      i {
        margin-right: 5px;
      }
    }

    .post-stats {
      .reply-count {
        color: #409eff;
        font-size: 14px;

        i {
          margin-right: 5px;
        }
      }
    }
  }
}

// 类型标签
.post-type-tag {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;

  &.type-sport {
    background: #e8f5e8;
    color: #52c41a;
  }

  &.type-fitness {
    background: #fff2e8;
    color: #fa8c16;
  }

  &.type-venue {
    background: #e6f7ff;
    color: #1890ff;
  }

  &.type-other {
    background: #f6f6f6;
    color: #666;
  }
}

// 分页
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 30px;
}

// 对话框样式
.new-post-dialog, .post-detail-dialog {
  .el-dialog__body {
    padding: 30px;
  }
}

// 帖子详情
.post-detail {
  .post-detail-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    margin-bottom: 20px;

    .author-info {
      display: flex;
      align-items: center;
      color: #666;

      i {
        margin-right: 8px;
      }

      .post-time {
        margin-left: 15px;
        font-size: 14px;
      }
    }
  }

  .post-detail-content {
    line-height: 1.8;
    margin-bottom: 30px;
    color: #333;
  }

  .replies-section {
    border-top: 1px solid #eee;
    padding-top: 20px;
    margin-bottom: 20px;

    h4 {
      margin-bottom: 20px;
      color: #333;
    }

    .reply-item {
      background: #f8f9fa;
      padding: 15px;
      border-radius: 8px;
      margin-bottom: 15px;

      .reply-author {
        display: flex;
        align-items: center;
        color: #666;
        font-size: 14px;
        margin-bottom: 10px;

        i {
          margin-right: 5px;
        }

        .reply-time {
          margin-left: 10px;
        }
      }

      .reply-content {
        color: #333;
        line-height: 1.6;
      }
    }
  }

  .reply-form {
    .reply-actions {
      margin-top: 15px;
      text-align: right;
    }
  }
}
</style>
