{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexAsideStatic.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexAsideStatic.vue", "mtime": 1750588761047}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["IndexAsideStatic.vue"], "names": [], "mappings": ";AA+BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexAsideStatic.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n  <el-aside class=\"index-aside\" height=\"100vh\" width=\"250px\">\r\n    <div class=\"index-aside-inner menulist\" style=\"height:100%\">\r\n      <div v-for=\"item in menuList\" :key=\"item.roleName\" v-if=\"role==item.roleName\" class=\"menulist-item\" style=\"height:100%;border:0;background-color:#2c3e50\">\r\n        <div class=\"menulistImg\" style=\"backgroundColor:#ff0000;padding:25px 0\" v-if=\"false && menulistStyle == 'vertical'\">\r\n          <el-image v-if=\"'http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg'\" src=\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\" fit=\"cover\" />\r\n        </div>\r\n        <el-menu mode=\"vertical\" :unique-opened=\"true\" class=\"el-menu-demo\" style=\"height:100%;\" background-color=\"#2c3e50\" text-color=\"#ecf0f1\" active-text-color=\"#00c292\" default-active=\"0\">\r\n          <el-menu-item index=\"(0).toString()\" :style=\"menulistBorderBottom\" @click=\"menuHandler('')\"><i v-if=\"true\" class=\"el-icon-s-home\" />首页</el-menu-item>\r\n          <el-submenu :index=\"(1).toString()\" :style=\"menulistBorderBottom\">\r\n            <template slot=\"title\">\r\n              <i v-if=\"true\" class=\"el-icon-user-solid\" />\r\n              <span>个人中心</span>\r\n            </template>\r\n            <el-menu-item :index=\"(1-2).toString()\" @click=\"menuHandler('updatePassword')\">修改密码</el-menu-item>\r\n            <el-menu-item :index=\"(1-2).toString()\" @click=\"menuHandler('center')\">个人信息</el-menu-item>\r\n          </el-submenu>\r\n          <el-submenu :style=\"menulistBorderBottom\" v-for=\" (menu,index) in item.backMenu\" :key=\"menu.menu\" :index=\"(index+2).toString()\">\r\n            <template slot=\"title\">\r\n              <i v-if=\"true\" :class=\"icons[index]\" />\r\n              <span>{{ menu.menu }}</span>\r\n            </template>\r\n            <el-menu-item v-for=\" (child,sort) in menu.child\" :key=\"sort\" :index=\"((index+2)+'-'+sort).toString()\" @click=\"menuHandler(child.tableName)\">{{ child.menu }}</el-menu-item>\r\n          </el-submenu>\r\n        </el-menu>\r\n\r\n      </div>\r\n    </div>\r\n  </el-aside>\r\n</template>\r\n<script>\r\nimport menu from '@/utils/menu'\r\nexport default {\r\n  data() {\r\n    return {\r\n      menuList: [],\r\n      dynamicMenuRoutes: [],\r\n      role: '',\r\n      icons: [\r\n        'el-icon-s-cooperation',\r\n        'el-icon-s-order',\r\n        'el-icon-s-platform',\r\n        'el-icon-s-fold',\r\n        'el-icon-s-unfold',\r\n        'el-icon-s-operation',\r\n        'el-icon-s-promotion',\r\n        'el-icon-s-release',\r\n        'el-icon-s-ticket',\r\n        'el-icon-s-management',\r\n        'el-icon-s-open',\r\n        'el-icon-s-shop',\r\n        'el-icon-s-marketing',\r\n        'el-icon-s-flag',\r\n        'el-icon-s-comment',\r\n        'el-icon-s-finance',\r\n        'el-icon-s-claim',\r\n        'el-icon-s-custom',\r\n        'el-icon-s-opportunity',\r\n        'el-icon-s-data',\r\n        'el-icon-s-check',\r\n        'el-icon-s-grid',\r\n        'el-icon-menu',\r\n        'el-icon-chat-dot-square',\r\n        'el-icon-message',\r\n        'el-icon-postcard',\r\n        'el-icon-position',\r\n        'el-icon-microphone',\r\n        'el-icon-close-notification',\r\n        'el-icon-bangzhu',\r\n        'el-icon-time',\r\n        'el-icon-odometer',\r\n        'el-icon-crop',\r\n        'el-icon-aim',\r\n        'el-icon-switch-button',\r\n        'el-icon-full-screen',\r\n        'el-icon-copy-document',\r\n        'el-icon-mic',\r\n        'el-icon-stopwatch',\r\n      ],\r\n      menulistStyle: 'vertical',\r\n\t  menulistBorderBottom: {},\r\n    }\r\n  },\r\n  mounted() {\r\n    const menus = menu.list()\r\n    this.menuList = menus\r\n    this.role = this.$storage.get('role')\r\n  },\r\n  created(){\r\n    setTimeout(()=>{\r\n      this.menulistStyleChange()\r\n    },10)\r\n    this.icons.sort(()=>{\r\n      return (0.5-Math.random())\r\n    })\r\n\tthis.lineBorder()\r\n  },\r\n  methods: {\r\n\tlineBorder() {\r\n\t\tlet style = 'vertical'\r\n\t\tlet w = '2px'\r\n\t\tlet s = 'solid'\r\n\t\tlet c = '#D6CFCF'\r\n\t\tif(style == 'vertical') {\r\n\t\t\tthis.menulistBorderBottom = {\r\n\t\t\t\tborderBottomWidth: w,\r\n\t\t\t\tborderBottomStyle: s,\r\n\t\t\t\tborderBottomColor: c\r\n\t\t\t}\r\n\t\t} else {\r\n\t\t\tthis.menulistBorderBottom = {\r\n\t\t\t\tborderRightWidth: w,\r\n\t\t\t\tborderRightStyle: s,\r\n\t\t\t\tborderRightColor: c\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n    menuHandler(name) {\r\n      let router = this.$router\r\n      name = '/'+name\r\n      router.push(name).catch(err => err)\r\n    },\r\n    // 菜单\r\n    setMenulistHoverColor(){\r\n      let that = this\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist .el-menu-item').forEach(el=>{\r\n          el.addEventListener(\"mouseenter\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#00c292\"\r\n          })\r\n          el.addEventListener(\"mouseleave\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#2c3e50\"\r\n          })\r\n          el.addEventListener(\"focus\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#00c292\"\r\n          })\r\n        })\r\n        document.querySelectorAll('.menulist .el-submenu__title').forEach(el=>{\r\n          el.addEventListener(\"mouseenter\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#00c292\"\r\n          })\r\n          el.addEventListener(\"mouseleave\", e => {\r\n            e.stopPropagation()\r\n            el.style.backgroundColor = \"#2c3e50\"\r\n          })\r\n        })\r\n      })\r\n    },\r\n    setMenulistIconColor() {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist .el-submenu__title .el-submenu__icon-arrow').forEach(el=>{\r\n          el.style.color = \"#ecf0f1\"\r\n        })\r\n      })\r\n    },\r\n    menulistStyleChange() {\r\n      this.setMenulistIconColor()\r\n      this.setMenulistHoverColor()\r\n      this.setMenulistStyleHeightChange()\r\n      let str = \"vertical\"\r\n      if(\"horizontal\" === str) {\r\n        this.$nextTick(()=>{\r\n          document.querySelectorAll('.el-container .el-container').forEach(el=>{\r\n            el.style.display = \"block\"\r\n            el.style.paddingTop = \"60px\" // header 高度\r\n          })\r\n          document.querySelectorAll('.el-aside').forEach(el=>{\r\n            el.style.width = \"100%\"\r\n            el.style.height = \"60px\"\r\n            el.style.paddingTop = '0'\r\n          })\r\n          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{\r\n            el.style.paddingTop = '0'\r\n          })\r\n        })\r\n      }\r\n      if(\"vertical\" === str) {\r\n        this.$nextTick(()=>{\r\n          document.querySelectorAll('.index-aside .index-aside-inner').forEach(el=>{\r\n            el.style.paddingTop = \"60px\"\r\n          })\r\n        })\r\n      }\r\n    },\r\n    setMenulistStyleHeightChange() {\r\n      this.$nextTick(()=>{\r\n        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-menu-item').forEach(el=>{\r\n          el.style.height = \"60px\"\r\n          el.style.lineHeight = \"60px\"\r\n        })\r\n        document.querySelectorAll('.menulist-item>.el-menu--horizontal>.el-submenu>.el-submenu__title').forEach(el=>{\r\n          el.style.height = \"60px\"\r\n          el.style.lineHeight = \"60px\"\r\n        })\r\n      })\r\n    },\r\n  }\r\n}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n  .index-aside {\r\n    position: relative;\r\n    overflow: hidden;\r\n\r\n    .menulistImg {\r\n      padding: 24px 0;\r\n      box-sizing: border-box;\r\n\r\n      .el-image {\r\n        margin: 0 auto;\r\n        width: 100px;\r\n        height: 100px;\r\n        border-radius: 100%;\r\n        display: block;\r\n      }\r\n    }\r\n\r\n    .index-aside-inner {\r\n      height: 100%;\r\n      margin-right: -17px;\r\n      margin-bottom: -17px;\r\n      overflow: scroll;\r\n      overflow-x: hidden !important;\r\n      padding-top: 60px;\r\n      box-sizing: border-box;\r\n\r\n      &:focus {\r\n        outline: none;\r\n      }\r\n\r\n      .el-menu {\r\n        border: 0;\r\n      }\r\n    }\r\n  }\r\n</style>\r\n\r\n"]}]}