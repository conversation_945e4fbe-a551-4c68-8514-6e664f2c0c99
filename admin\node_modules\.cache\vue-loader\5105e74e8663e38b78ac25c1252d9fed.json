{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\config\\add-or-update.vue?vue&type=style&index=0&id=ec0c4148&lang=scss", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\config\\add-or-update.vue", "mtime": 1750591708080}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouZWRpdG9yew0KICBoZWlnaHQ6IDUwMHB4Ow0KDQogICYgOjp2LWRlZXAgLnFsLWNvbnRhaW5lciB7DQoJICBoZWlnaHQ6IDMxMHB4Ow0KICB9DQp9DQouYW1hcC13cmFwcGVyIHsNCiAgd2lkdGg6IDEwMCU7DQogIGhlaWdodDogNTAwcHg7DQp9DQouc2VhcmNoLWJveCB7DQogIHBvc2l0aW9uOiBhYnNvbHV0ZTsNCn0NCi5hZGRFZGl0LWJsb2NrIHsNCgltYXJnaW46IC0xMHB4Ow0KfQ0KLmRldGFpbC1mb3JtLWNvbnRlbnQgew0KCXBhZGRpbmc6IDEycHg7DQp9DQouYnRuIC5lbC1idXR0b24gew0KICBwYWRkaW5nOiAwOw0KfQ=="}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AA2XA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/config", "sourcesContent": ["<template>\r\n  <div class=\"addEdit-block\">\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :model=\"ruleForm\"\r\n      :rules=\"rules\"\r\n      label-width=\"80px\"\r\n\t  :style=\"{backgroundColor:addEditForm.addEditBoxColor}\"\r\n    >\r\n      <el-row>\r\n      <el-col :span=\"12\">\r\n        <el-form-item class=\"input\" v-if=\"type!='info'\"  label=\"名称\" prop=\"name\">\r\n          <el-input v-model=\"ruleForm.name\" \r\n              placeholder=\"名称\" clearable  :readonly=\"ro.name\"></el-input>\r\n        </el-form-item>\r\n        <div v-else>\r\n          <el-form-item class=\"input\" label=\"名称\" prop=\"name\">\r\n              <el-input v-model=\"ruleForm.name\" \r\n                placeholder=\"名称\" readonly></el-input>\r\n          </el-form-item>\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"24\">  \r\n        <el-form-item class=\"upload\" v-if=\"type!='info' && !ro.value\" label=\"值\" prop=\"value\">\r\n          <file-upload\r\n          tip=\"点击上传图片\"\r\n          action=\"file/upload\"\r\n          :limit=\"3\"\r\n          :multiple=\"true\"\r\n          :fileUrls=\"ruleForm.value?ruleForm.value:''\"\r\n          @change=\"valueUploadChange\"\r\n          ></file-upload>\r\n        </el-form-item>\r\n        <div v-else>\r\n          <el-form-item v-if=\"ruleForm.value\" label=\"值\" prop=\"value\">\r\n            <img style=\"margin-right:20px;\" v-bind:key=\"index\" v-for=\"(item,index) in ruleForm.value.split(',')\" :src=\"item\" width=\"100\" height=\"100\">\r\n          </el-form-item>\r\n        </div>\r\n      </el-col>\r\n      </el-row>\r\n      <el-form-item class=\"btn\">\r\n        <el-button v-if=\"type!='info'\" type=\"primary\" class=\"btn-success\" @click=\"onSubmit\">提交</el-button>\r\n        <el-button v-if=\"type!='info'\" class=\"btn-close\" @click=\"back()\">取消</el-button>\r\n        <el-button v-if=\"type=='info'\" class=\"btn-close\" @click=\"back()\">返回</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n    \r\n\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\nimport styleJs from \"../../../utils/style.js\";\r\nexport default {\r\n  data() {\r\n    let self = this\r\n    var validateIdCard = (rule, value, callback) => {\r\n      if(!value){\r\n        callback();\r\n      } else if (!checkIdCard(value)) {\r\n        callback(new Error(\"请输入正确的身份证号\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    var validateUrl = (rule, value, callback) => {\r\n      if(!value){\r\n        callback();\r\n      } else if (!isURL(value)) {\r\n        callback(new Error(\"请输入正确的URL地址\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    var validateMobile = (rule, value, callback) => {\r\n      if(!value){\r\n        callback();\r\n      } else if (!isMobile(value)) {\r\n        callback(new Error(\"请输入正确的手机号码\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    var validatePhone = (rule, value, callback) => {\r\n      if(!value){\r\n        callback();\r\n      } else if (!isPhone(value)) {\r\n        callback(new Error(\"请输入正确的电话号码\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    var validateEmail = (rule, value, callback) => {\r\n      if(!value){\r\n        callback();\r\n      } else if (!isEmail(value)) {\r\n        callback(new Error(\"请输入正确的邮箱地址\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    var validateNumber = (rule, value, callback) => {\r\n      if(!value){\r\n        callback();\r\n      } else if (!isNumber(value)) {\r\n        callback(new Error(\"请输入数字\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    var validateIntNumber = (rule, value, callback) => {\r\n      if(!value){\r\n        callback();\r\n      } else if (!isIntNumer(value)) {\r\n        callback(new Error(\"请输入整数\"));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n    return {\r\n\t  addEditForm: null,\r\n      id: '',\r\n      type: '',\r\n      ro:{\r\n\tname : false,\r\n\tvalue : false,\r\n      },\r\n      ruleForm: {\r\n        name: '',\r\n        value: '',\r\n      },\r\n      rules: {\r\n          name: [\r\n                { required: true, message: '名称不能为空', trigger: 'blur' },\r\n          ],\r\n          value: [\r\n          ],\r\n      }\r\n    };\r\n  },\r\n  props: [\"parent\"],\r\n  computed: {\r\n  },\r\n  created() {\r\n    this.addEditForm = styleJs.addStyle();\r\n\tthis.addEditStyleChange()\r\n\tthis.addEditUploadStyleChange()\r\n  },\r\n  methods: {\r\n    // 下载\r\n    download(file){\r\n      window.open(`${file}`)\r\n    },\r\n    // 初始化\r\n    init(id,type) {\r\n      if (id) {\r\n        this.id = id;\r\n        this.type = type;\r\n      }\r\n      if(this.type=='info'||this.type=='else'){\r\n        this.info(id);\r\n      }else if(this.type=='cross'){\r\n        var obj = this.$storage.getObj('crossObj');\r\n        for (var o in obj){\r\n          if(o=='name'){\r\n            this.ruleForm.name = obj[o];\r\n\t    this.ro.name = true;\r\n            continue;\r\n          }\r\n          if(o=='value'){\r\n            this.ruleForm.value = obj[o];\r\n\t    this.ro.value = true;\r\n            continue;\r\n          }\r\n        }\r\n      }\r\n    },\r\n    // 多级联动参数\r\n    info(id) {\r\n      this.$http({\r\n        url: `config/info/${id}`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n        this.ruleForm = data.data;\r\n\t//解决前台上传图片后台不显示的问题\r\n\tlet reg=new RegExp('../../../upload','g')//g代表全部\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n    // 提交\r\n    onSubmit() {\r\n\r\n      this.$refs[\"ruleForm\"].validate(valid => {\r\n        if (valid) {\r\n          this.$http({\r\n            url: `config/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n            method: \"post\",\r\n            data: this.ruleForm\r\n          }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n              this.$message({\r\n                message: \"操作成功\",\r\n                type: \"success\",\r\n                duration: 1500,\r\n                onClose: () => {\r\n                  this.parent.showFlag = true;\r\n                  this.parent.addOrUpdateFlag = false;\r\n                  this.parent.configCrossAddOrUpdateFlag = false;\r\n                  this.parent.search();\r\n                  this.parent.contentStyleChange();\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error(data.msg);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    },\r\n    // 获取uuid\r\n    getUUID () {\r\n      return new Date().getTime();\r\n    },\r\n    // 返回\r\n    back() {\r\n      this.parent.showFlag = true;\r\n      this.parent.addOrUpdateFlag = false;\r\n      this.parent.configCrossAddOrUpdateFlag = false;\r\n      this.parent.contentStyleChange();\r\n    },\r\n    valueUploadChange(fileUrls) {\r\n\tthis.ruleForm.value = fileUrls;\r\n\t\t\tthis.addEditUploadStyleChange()\r\n    },\r\n\taddEditStyleChange() {\r\n\t  this.$nextTick(()=>{\r\n\t    // input\r\n\t    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n\t      el.style.height = this.addEditForm.inputHeight\r\n\t      el.style.color = this.addEditForm.inputFontColor\r\n\t      el.style.fontSize = this.addEditForm.inputFontSize\r\n\t      el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n\t      el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n\t      el.style.borderColor = this.addEditForm.inputBorderColor\r\n\t      el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n\t      el.style.backgroundColor = this.addEditForm.inputBgColor\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n\t      el.style.lineHeight = this.addEditForm.inputHeight\r\n\t      el.style.color = this.addEditForm.inputLableColor\r\n\t      el.style.fontSize = this.addEditForm.inputLableFontSize\r\n\t    })\r\n\t    // select\r\n\t    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n\t      el.style.height = this.addEditForm.selectHeight\r\n\t      el.style.color = this.addEditForm.selectFontColor\r\n\t      el.style.fontSize = this.addEditForm.selectFontSize\r\n\t      el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n\t      el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n\t      el.style.borderColor = this.addEditForm.selectBorderColor\r\n\t      el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n\t      el.style.backgroundColor = this.addEditForm.selectBgColor\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n\t      el.style.lineHeight = this.addEditForm.selectHeight\r\n\t      el.style.color = this.addEditForm.selectLableColor\r\n\t      el.style.fontSize = this.addEditForm.selectLableFontSize\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n\t      el.style.color = this.addEditForm.selectIconFontColor\r\n\t      el.style.fontSize = this.addEditForm.selectIconFontSize\r\n\t    })\r\n\t    // date\r\n\t    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n\t      el.style.height = this.addEditForm.dateHeight\r\n\t      el.style.color = this.addEditForm.dateFontColor\r\n\t      el.style.fontSize = this.addEditForm.dateFontSize\r\n\t      el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n\t      el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n\t      el.style.borderColor = this.addEditForm.dateBorderColor\r\n\t      el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n\t      el.style.backgroundColor = this.addEditForm.dateBgColor\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n\t      el.style.lineHeight = this.addEditForm.dateHeight\r\n\t      el.style.color = this.addEditForm.dateLableColor\r\n\t      el.style.fontSize = this.addEditForm.dateLableFontSize\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n\t      el.style.color = this.addEditForm.dateIconFontColor\r\n\t      el.style.fontSize = this.addEditForm.dateIconFontSize\r\n\t      el.style.lineHeight = this.addEditForm.dateHeight\r\n\t    })\r\n\t    // upload\r\n\t    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n\t    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n\t      el.style.width = this.addEditForm.uploadHeight\r\n\t      el.style.height = this.addEditForm.uploadHeight\r\n\t      el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n\t      el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n\t      el.style.borderColor = this.addEditForm.uploadBorderColor\r\n\t      el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n\t      el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n\t      el.style.lineHeight = this.addEditForm.uploadHeight\r\n\t      el.style.color = this.addEditForm.uploadLableColor\r\n\t      el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n\t      el.style.color = this.addEditForm.uploadIconFontColor\r\n\t      el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n\t      el.style.lineHeight = iconLineHeight\r\n\t      el.style.display = 'block'\r\n\t    })\r\n\t    // 多文本输入框\r\n\t    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n\t      el.style.height = this.addEditForm.textareaHeight\r\n\t      el.style.color = this.addEditForm.textareaFontColor\r\n\t      el.style.fontSize = this.addEditForm.textareaFontSize\r\n\t      el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n\t      el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n\t      el.style.borderColor = this.addEditForm.textareaBorderColor\r\n\t      el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n\t      el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n\t    })\r\n\t    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n\t      // el.style.lineHeight = this.addEditForm.textareaHeight\r\n\t      el.style.color = this.addEditForm.textareaLableColor\r\n\t      el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n\t    })\r\n\t    // 保存\r\n\t    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n\t      el.style.width = this.addEditForm.btnSaveWidth\r\n\t      el.style.height = this.addEditForm.btnSaveHeight\r\n\t      el.style.color = this.addEditForm.btnSaveFontColor\r\n\t      el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n\t      el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n\t      el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n\t      el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n\t      el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n\t      el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n\t    })\r\n\t    // 返回\r\n\t    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n\t      el.style.width = this.addEditForm.btnCancelWidth\r\n\t      el.style.height = this.addEditForm.btnCancelHeight\r\n\t      el.style.color = this.addEditForm.btnCancelFontColor\r\n\t      el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n\t      el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n\t      el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n\t      el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n\t      el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n\t      el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n\t    })\r\n\t  })\r\n\t},\r\n\taddEditUploadStyleChange() {\r\n\t\tthis.$nextTick(()=>{\r\n\t\t  document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n\t\t\tel.style.width = this.addEditForm.uploadHeight\r\n\t\t\tel.style.height = this.addEditForm.uploadHeight\r\n\t\t\tel.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n\t\t\tel.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n\t\t\tel.style.borderColor = this.addEditForm.uploadBorderColor\r\n\t\t\tel.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n\t\t\tel.style.backgroundColor = this.addEditForm.uploadBgColor\r\n\t\t  })\r\n\t  })\r\n\t},\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\">\r\n.editor{\r\n  height: 500px;\r\n\r\n  & ::v-deep .ql-container {\r\n\t  height: 310px;\r\n  }\r\n}\r\n.amap-wrapper {\r\n  width: 100%;\r\n  height: 500px;\r\n}\r\n.search-box {\r\n  position: absolute;\r\n}\r\n.addEdit-block {\r\n\tmargin: -10px;\r\n}\r\n.detail-form-content {\r\n\tpadding: 12px;\r\n}\r\n.btn .el-button {\r\n  padding: 0;\r\n}</style>\r\n\r\n\r\n"]}]}