{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\src\\router\\router-static.js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\router\\router-static.js", "mtime": 1750602738942}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "use", "Index", "Home", "<PERSON><PERSON>", "NotFound", "UpdatePassword", "pay", "register", "center", "chang<PERSON>Booking", "collectionManagement", "collectionAnalytics", "users", "changdi", "changdiCollection", "changdiOrder", "dictionary", "forum", "forumNew", "gonggao", "yonghu", "config", "dictionaryBanquan", "dictionaryChangdi", "dictionaryChangdiCollection", "dictionaryChangdiOrder", "dictionaryForum", "dictionaryForumState", "dictionaryGonggao", "dictionarySex", "dictionaryShangxia", "routes", "path", "name", "component", "children", "meta", "icon", "title", "redirect", "router", "mode"], "sources": ["D:/1/tiyuguan/admin/src/router/router-static.js"], "sourcesContent": ["import Vue from 'vue';\r\n//配置路由\r\nimport VueRouter from 'vue-router'\r\nVue.use(VueRouter);\r\n//1.创建组件\r\nimport Index from '@/views/index'\r\nimport Home from '@/views/home'\r\nimport Login from '@/views/login'\r\nimport NotFound from '@/views/404'\r\nimport UpdatePassword from '@/views/update-password'\r\nimport pay from '@/views/pay'\r\nimport register from '@/views/register'\r\nimport center from '@/views/center'\r\nimport changdiBooking from '@/views/changdi-booking'\r\nimport collectionManagement from '@/views/collection-management'\r\nimport collectionAnalytics from '@/views/collection-analytics'\r\n\r\n         import users from '@/views/modules/users/list'\r\n        import changdi from '@/views/modules/changdi/list'\r\n        import changdiCollection from '@/views/modules/changdiCollection/list'\r\n        import changdiOrder from '@/views/modules/changdiOrder/list'\r\n        import dictionary from '@/views/modules/dictionary/list'\r\n        import forum from '@/views/modules/forum/list'\r\n        import forumNew from '@/views/forum'\r\n        import gonggao from '@/views/modules/gonggao/list'\r\n        import yonghu from '@/views/modules/yonghu/list'\r\n        import config from '@/views/modules/config/list'\r\n        import dictionaryBanquan from '@/views/modules/dictionaryBanquan/list'\r\n        import dictionaryChangdi from '@/views/modules/dictionaryChangdi/list'\r\n        import dictionaryChangdiCollection from '@/views/modules/dictionaryChangdiCollection/list'\r\n        import dictionaryChangdiOrder from '@/views/modules/dictionaryChangdiOrder/list'\r\n        import dictionaryForum from '@/views/modules/dictionaryForum/list'\r\n        import dictionaryForumState from '@/views/modules/dictionaryForumState/list'\r\n        import dictionaryGonggao from '@/views/modules/dictionaryGonggao/list'\r\n        import dictionarySex from '@/views/modules/dictionarySex/list'\r\n        import dictionaryShangxia from '@/views/modules/dictionaryShangxia/list'\r\n\r\n\r\n\r\n\r\n\r\n//2.配置路由   注意：名字\r\nconst routes = [{\r\n    path: '/index',\r\n    name: '首页',\r\n    component: Index,\r\n    children: [{\r\n      // 这里不设置值，是把main作为默认页面\r\n      path: '/',\r\n      name: '首页',\r\n      component: Home,\r\n      meta: {icon:'', title:'center'}\r\n    }, {\r\n      path: '/updatePassword',\r\n      name: '修改密码',\r\n      component: UpdatePassword,\r\n      meta: {icon:'', title:'updatePassword'}\r\n    }, {\r\n      path: '/pay',\r\n      name: '支付',\r\n      component: pay,\r\n      meta: {icon:'', title:'pay'}\r\n    }, {\r\n      path: '/center',\r\n      name: '个人信息',\r\n      component: center,\r\n      meta: {icon:'', title:'center'}\r\n    }, {\r\n      path: '/changdi-booking',\r\n      name: '场地预约',\r\n      component: changdiBooking,\r\n      meta: {icon:'', title:'changdi-booking'}\r\n    } ,{\r\n        path: '/users',\r\n        name: '管理信息',\r\n        component: users\r\n      }\r\n    ,{\r\n        path: '/dictionaryBanquan',\r\n        name: '半全场',\r\n        component: dictionaryBanquan\r\n    }\r\n    ,{\r\n        path: '/dictionaryChangdi',\r\n        name: '场地类型名称',\r\n        component: dictionaryChangdi\r\n    }\r\n    ,{\r\n        path: '/dictionaryChangdiCollection',\r\n        name: '收藏表类型',\r\n        component: dictionaryChangdiCollection\r\n    }\r\n    ,{\r\n        path: '/dictionaryChangdiOrder',\r\n        name: '场地类型名称',\r\n        component: dictionaryChangdiOrder\r\n    }\r\n    ,{\r\n        path: '/dictionaryForum',\r\n        name: '帖子类型名称',\r\n        component: dictionaryForum\r\n    }\r\n    ,{\r\n        path: '/dictionaryForumState',\r\n        name: '帖子状态',\r\n        component: dictionaryForumState\r\n    }\r\n    ,{\r\n        path: '/dictionaryGonggao',\r\n        name: '公告类型名称',\r\n        component: dictionaryGonggao\r\n    }\r\n    ,{\r\n        path: '/dictionarySex',\r\n        name: '性别类型名称',\r\n        component: dictionarySex\r\n    }\r\n    ,{\r\n        path: '/dictionaryShangxia',\r\n        name: '上下架名称',\r\n        component: dictionaryShangxia\r\n    }\r\n    ,{\r\n        path: '/config',\r\n        name: '轮播图',\r\n        component: config\r\n    }\r\n\r\n\r\n    ,{\r\n        path: '/changdi',\r\n        name: '场地',\r\n        component: changdi\r\n      }\r\n    ,{\r\n        path: '/changdiCollection',\r\n        name: '场地收藏',\r\n        component: changdiCollection\r\n      }\r\n    ,{\r\n        path: '/collection-management',\r\n        name: '收藏管理',\r\n        component: collectionManagement\r\n      }\r\n    ,{\r\n        path: '/collection-analytics',\r\n        name: '收藏分析',\r\n        component: collectionAnalytics\r\n      }\r\n    ,{\r\n        path: '/changdiOrder',\r\n        name: '场地预约',\r\n        component: changdiOrder\r\n      }\r\n    ,{\r\n        path: '/dictionary',\r\n        name: '字典',\r\n        component: dictionary\r\n      }\r\n    ,{\r\n        path: '/forum',\r\n        name: '论坛管理',\r\n        component: forum\r\n      }\r\n    ,{\r\n        path: '/forum-new',\r\n        name: '论坛',\r\n        component: forumNew\r\n      }\r\n    ,{\r\n        path: '/gonggao',\r\n        name: '公告信息',\r\n        component: gonggao\r\n      }\r\n    ,{\r\n        path: '/yonghu',\r\n        name: '用户',\r\n        component: yonghu\r\n      }\r\n\r\n\r\n    ]\r\n  },\r\n  {\r\n    path: '/login',\r\n    name: 'login',\r\n    component: Login,\r\n    meta: {icon:'', title:'login'}\r\n  },\r\n  {\r\n    path: '/register',\r\n    name: 'register',\r\n    component: register,\r\n    meta: {icon:'', title:'register'}\r\n  },\r\n  {\r\n    path: '/',\r\n    name: '首页',\r\n    redirect: '/index'\r\n  }, /*默认跳转路由*/\r\n  {\r\n    path: '*',\r\n    component: NotFound\r\n  }\r\n]\r\n//3.实例化VueRouter  注意：名字\r\nconst router = new VueRouter({\r\n  mode: 'hash',\r\n  /*hash模式改为history*/\r\n  routes // （缩写）相当于 routes: routes\r\n})\r\n\r\nexport default router;\r\n"], "mappings": "AAAA,OAAOA,GAAG,MAAM,KAAK;AACrB;AACA,OAAOC,SAAS,MAAM,YAAY;AAClCD,GAAG,CAACE,GAAG,CAACD,SAAS,CAAC;AAClB;AACA,OAAOE,KAAK,MAAM,eAAe;AACjC,OAAOC,IAAI,MAAM,cAAc;AAC/B,OAAOC,KAAK,MAAM,eAAe;AACjC,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,GAAG,MAAM,aAAa;AAC7B,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,OAAOC,MAAM,MAAM,gBAAgB;AACnC,OAAOC,cAAc,MAAM,yBAAyB;AACpD,OAAOC,oBAAoB,MAAM,+BAA+B;AAChE,OAAOC,mBAAmB,MAAM,8BAA8B;AAErD,OAAOC,KAAK,MAAM,4BAA4B;AAC/C,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAOC,YAAY,MAAM,mCAAmC;AAC5D,OAAOC,UAAU,MAAM,iCAAiC;AACxD,OAAOC,KAAK,MAAM,4BAA4B;AAC9C,OAAOC,QAAQ,MAAM,eAAe;AACpC,OAAOC,OAAO,MAAM,8BAA8B;AAClD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,MAAM,MAAM,6BAA6B;AAChD,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAOC,2BAA2B,MAAM,kDAAkD;AAC1F,OAAOC,sBAAsB,MAAM,6CAA6C;AAChF,OAAOC,eAAe,MAAM,sCAAsC;AAClE,OAAOC,oBAAoB,MAAM,2CAA2C;AAC5E,OAAOC,iBAAiB,MAAM,wCAAwC;AACtE,OAAOC,aAAa,MAAM,oCAAoC;AAC9D,OAAOC,kBAAkB,MAAM,yCAAyC;;AAMhF;AACA,IAAMC,MAAM,GAAG,CAAC;EACZC,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,IAAI;EACVC,SAAS,EAAEjC,KAAK;EAChBkC,QAAQ,EAAE,CAAC;IACT;IACAH,IAAI,EAAE,GAAG;IACTC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEhC,IAAI;IACfkC,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAQ;EAChC,CAAC,EAAE;IACDN,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE7B,cAAc;IACzB+B,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAgB;EACxC,CAAC,EAAE;IACDN,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAE5B,GAAG;IACd8B,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAK;EAC7B,CAAC,EAAE;IACDN,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE1B,MAAM;IACjB4B,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAQ;EAChC,CAAC,EAAE;IACDN,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEzB,cAAc;IACzB2B,IAAI,EAAE;MAACC,IAAI,EAAC,EAAE;MAAEC,KAAK,EAAC;IAAiB;EACzC,CAAC,EAAE;IACCN,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEtB;EACb,CAAC,EACF;IACGoB,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEZ;EACf,CAAC,EACA;IACGU,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEX;EACf,CAAC,EACA;IACGS,IAAI,EAAE,8BAA8B;IACpCC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEV;EACf,CAAC,EACA;IACGQ,IAAI,EAAE,yBAAyB;IAC/BC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAET;EACf,CAAC,EACA;IACGO,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAER;EACf,CAAC,EACA;IACGM,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEP;EACf,CAAC,EACA;IACGK,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEN;EACf,CAAC,EACA;IACGI,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE,QAAQ;IACdC,SAAS,EAAEL;EACf,CAAC,EACA;IACGG,IAAI,EAAE,qBAAqB;IAC3BC,IAAI,EAAE,OAAO;IACbC,SAAS,EAAEJ;EACf,CAAC,EACA;IACGE,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,KAAK;IACXC,SAAS,EAAEb;EACf,CAAC,EAGA;IACGW,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAErB;EACb,CAAC,EACF;IACGmB,IAAI,EAAE,oBAAoB;IAC1BC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEpB;EACb,CAAC,EACF;IACGkB,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAExB;EACb,CAAC,EACF;IACGsB,IAAI,EAAE,uBAAuB;IAC7BC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEvB;EACb,CAAC,EACF;IACGqB,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEnB;EACb,CAAC,EACF;IACGiB,IAAI,EAAE,aAAa;IACnBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAElB;EACb,CAAC,EACF;IACGgB,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEjB;EACb,CAAC,EACF;IACGe,IAAI,EAAE,YAAY;IAClBC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEhB;EACb,CAAC,EACF;IACGc,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAEf;EACb,CAAC,EACF;IACGa,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,IAAI;IACVC,SAAS,EAAEd;EACb,CAAC;AAIL,CAAC,EACD;EACEY,IAAI,EAAE,QAAQ;EACdC,IAAI,EAAE,OAAO;EACbC,SAAS,EAAE/B,KAAK;EAChBiC,IAAI,EAAE;IAACC,IAAI,EAAC,EAAE;IAAEC,KAAK,EAAC;EAAO;AAC/B,CAAC,EACD;EACEN,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,SAAS,EAAE3B,QAAQ;EACnB6B,IAAI,EAAE;IAACC,IAAI,EAAC,EAAE;IAAEC,KAAK,EAAC;EAAU;AAClC,CAAC,EACD;EACEN,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,IAAI;EACVM,QAAQ,EAAE;AACZ,CAAC,EAAE;AACH;EACEP,IAAI,EAAE,GAAG;EACTE,SAAS,EAAE9B;AACb,CAAC,CACF;AACD;AACA,IAAMoC,MAAM,GAAG,IAAIzC,SAAS,CAAC;EAC3B0C,IAAI,EAAE,MAAM;EACZ;EACAV,MAAM,EAANA,MAAM,CAAC;AACT,CAAC,CAAC;AAEF,eAAeS,MAAM", "ignoreList": []}]}