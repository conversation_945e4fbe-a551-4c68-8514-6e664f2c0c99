{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\home.vue?vue&type=style&index=0&id=7eb2bc79&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\home.vue", "mtime": 1750589826593}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouaG9tZS1jb250YWluZXIgew0KICBwYWRkaW5nOiAyMHB4Ow0KDQogIC53ZWxjb21lLWJhbm5lciB7DQogICAgbWFyZ2luLWJvdHRvbTogMjBweDsNCg0KICAgIDo6di1kZWVwIC5lbC1hbGVydCB7DQogICAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgICBib3JkZXI6IG5vbmU7DQogICAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KDQogICAgICAuZWwtYWxlcnRfX3RpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgIH0NCg0KICAgICAgLmVsLWFsZXJ0X19jb250ZW50IHsNCiAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICBtYXJnaW4tdG9wOiA4cHg7DQogICAgICB9DQogICAgfQ0KICB9DQoNCiAgLmNoYXJ0LWNvbnRhaW5lciwgLmNhcmQtY29udGFpbmVyIHsNCiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgcGFkZGluZzogMjBweDsNCiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOw0KDQogICAgaDMgew0KICAgICAgbWFyZ2luOiAwIDAgMjBweCAwOw0KICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogICAgICBjb2xvcjogIzJjM2U1MDsNCiAgICAgIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjMDBjMjkyOw0KICAgICAgcGFkZGluZy1ib3R0b206IDEwcHg7DQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["home.vue"], "names": [], "mappings": ";AAwFA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "home.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"home-container\">\r\n    <!-- 欢迎横幅 -->\r\n    <div class=\"welcome-banner\">\r\n      <el-alert :closable=\"false\" title=\"欢迎使用体育馆管理平台\" type=\"success\">\r\n        <template slot>\r\n          <div>\r\n            <p>您好，欢迎使用体育馆管理平台！今天是 {{ currentDate }}</p>\r\n          </div>\r\n        </template>\r\n      </el-alert>\r\n    </div>\r\n\r\n    <!-- 轮播图 -->\r\n    <HomeCarousel />\r\n\r\n    <!-- 统计卡片 -->\r\n    <HomeStats />\r\n\r\n    <!-- 图表和卡片 -->\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"16\">\r\n        <div class=\"chart-container\">\r\n          <h3>数据统计</h3>\r\n          <HomeChart />\r\n        </div>\r\n      </el-col>\r\n      <el-col :span=\"8\">\r\n        <div class=\"card-container\">\r\n          <h3>快捷操作</h3>\r\n          <HomeCard />\r\n        </div>\r\n      </el-col>\r\n    </el-row>\r\n  </div>\r\n</template>\r\n<script>\r\nimport router from '@/router/router-static'\r\nimport HomeCarousel from '@/components/home/<USER>'\r\nimport HomeStats from '@/components/home/<USER>'\r\nimport HomeChart from '@/components/home/<USER>'\r\nimport HomeCard from '@/components/home/<USER>'\r\n\r\nexport default {\r\n  components: {\r\n    HomeCarousel,\r\n    HomeStats,\r\n    HomeChart,\r\n    HomeCard\r\n  },\r\n  data() {\r\n    return {\r\n      currentDate: ''\r\n    }\r\n  },\r\n  mounted(){\r\n    this.init();\r\n    this.getCurrentDate();\r\n  },\r\n  methods:{\r\n    init(){\r\n        if(this.$storage.get('Token')){\r\n        this.$http({\r\n            url: `${this.$storage.get('sessionTable')}/session`,\r\n            method: \"get\"\r\n        }).then(({ data }) => {\r\n            if (data && data.code != 0) {\r\n            router.push({ name: 'login' })\r\n            }\r\n        });\r\n        }else{\r\n            router.push({ name: 'login' })\r\n        }\r\n    },\r\n    getCurrentDate() {\r\n      const now = new Date()\r\n      const year = now.getFullYear()\r\n      const month = String(now.getMonth() + 1).padStart(2, '0')\r\n      const day = String(now.getDate()).padStart(2, '0')\r\n      const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']\r\n      const weekday = weekdays[now.getDay()]\r\n      this.currentDate = `${year}年${month}月${day}日 ${weekday}`\r\n    }\r\n  }\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.home-container {\r\n  padding: 20px;\r\n\r\n  .welcome-banner {\r\n    margin-bottom: 20px;\r\n\r\n    ::v-deep .el-alert {\r\n      border-radius: 8px;\r\n      border: none;\r\n      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n      .el-alert__title {\r\n        font-size: 18px;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .el-alert__content {\r\n        font-size: 14px;\r\n        margin-top: 8px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .chart-container, .card-container {\r\n    background: white;\r\n    border-radius: 8px;\r\n    padding: 20px;\r\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\r\n    h3 {\r\n      margin: 0 0 20px 0;\r\n      font-size: 18px;\r\n      font-weight: bold;\r\n      color: #2c3e50;\r\n      border-bottom: 2px solid #00c292;\r\n      padding-bottom: 10px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"]}]}