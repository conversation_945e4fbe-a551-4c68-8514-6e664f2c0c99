{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue?vue&type=style&index=0&id=6c26a6b1&scoped=true&lang=css", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue", "mtime": 1750602713137}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["collection-analytics.vue"], "names": [], "mappings": ";AAqYA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "collection-analytics.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"collection-analytics\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">\n        <i class=\"el-icon-data-analysis\"></i>\n        收藏数据分析\n      </h1>\n      <p class=\"page-description\">分析用户收藏行为，了解热门场地和收藏趋势</p>\n    </div>\n\n    <!-- 核心指标卡片 -->\n    <div class=\"metrics-section\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"metric-card primary\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ totalCollections }}</h3>\n              <p>总收藏数</p>\n              <span class=\"metric-trend up\">\n                <i class=\"el-icon-top\"></i>\n                +{{ todayIncrease }}\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card success\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ popularVenues }}</h3>\n              <p>热门场地数</p>\n              <span class=\"metric-trend\">\n                收藏>10次\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card warning\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ activeCollectors }}</h3>\n              <p>活跃收藏用户</p>\n              <span class=\"metric-trend\">\n                本月活跃\n              </span>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"metric-card info\">\n            <div class=\"metric-icon\">\n              <i class=\"el-icon-pie-chart\"></i>\n            </div>\n            <div class=\"metric-content\">\n              <h3>{{ avgCollectionsPerUser.toFixed(1) }}</h3>\n              <p>人均收藏数</p>\n              <span class=\"metric-trend\">\n                平均值\n              </span>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 图表区域 -->\n    <div class=\"charts-section\">\n      <el-row :gutter=\"20\">\n        <!-- 收藏趋势图 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>收藏趋势</span>\n              <el-select v-model=\"trendPeriod\" size=\"small\" @change=\"loadTrendData\">\n                <el-option label=\"最近7天\" value=\"7days\"></el-option>\n                <el-option label=\"最近30天\" value=\"30days\"></el-option>\n                <el-option label=\"最近3个月\" value=\"3months\"></el-option>\n              </el-select>\n            </div>\n            <div id=\"trendChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n        \n        <!-- 场地类型分布 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>场地类型收藏分布</span>\n            </div>\n            <div id=\"typeChart\" class=\"chart-container\"></div>\n          </el-card>\n        </el-col>\n      </el-row>\n      \n      <el-row :gutter=\"20\" style=\"margin-top: 20px;\">\n        <!-- 热门场地排行 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>热门场地排行</span>\n            </div>\n            <div class=\"ranking-list\">\n              <div \n                v-for=\"(venue, index) in topVenues\" \n                :key=\"venue.id\"\n                class=\"ranking-item\"\n                :class=\"{ 'top-three': index < 3 }\">\n                <div class=\"ranking-number\" :class=\"getRankingClass(index)\">\n                  {{ index + 1 }}\n                </div>\n                <div class=\"venue-info\">\n                  <img \n                    v-if=\"venue.photo\" \n                    :src=\"venue.photo.split(',')[0]\" \n                    class=\"venue-thumb\"\n                    @error=\"handleImageError\">\n                  <div class=\"venue-details\">\n                    <h4>{{ venue.name }}</h4>\n                    <p>{{ venue.type }}</p>\n                  </div>\n                </div>\n                <div class=\"collection-count\">\n                  <span class=\"count\">{{ venue.collections }}</span>\n                  <span class=\"label\">收藏</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n        \n        <!-- 用户收藏行为分析 -->\n        <el-col :span=\"12\">\n          <el-card class=\"chart-card\">\n            <div slot=\"header\" class=\"chart-header\">\n              <span>用户收藏行为</span>\n            </div>\n            <div class=\"behavior-stats\">\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏高峰时段</div>\n                <div class=\"behavior-value\">14:00 - 16:00</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">平均收藏间隔</div>\n                <div class=\"behavior-value\">2.3天</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">取消收藏率</div>\n                <div class=\"behavior-value\">12.5%</div>\n              </div>\n              <div class=\"behavior-item\">\n                <div class=\"behavior-label\">收藏转预约率</div>\n                <div class=\"behavior-value\">35.8%</div>\n              </div>\n            </div>\n            \n            <div class=\"time-distribution\">\n              <h4>收藏时间分布</h4>\n              <div class=\"time-bars\">\n                <div \n                  v-for=\"hour in timeDistribution\" \n                  :key=\"hour.time\"\n                  class=\"time-bar\">\n                  <div class=\"bar\" :style=\"{ height: hour.percentage + '%' }\"></div>\n                  <span class=\"time-label\">{{ hour.time }}</span>\n                </div>\n              </div>\n            </div>\n          </el-card>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 详细数据表格 -->\n    <div class=\"data-table-section\">\n      <el-card>\n        <div slot=\"header\" class=\"table-header\">\n          <span>收藏详细数据</span>\n          <div class=\"table-actions\">\n            <el-button size=\"small\" @click=\"exportData\" icon=\"el-icon-download\">\n              导出数据\n            </el-button>\n            <el-button size=\"small\" @click=\"refreshData\" icon=\"el-icon-refresh\">\n              刷新\n            </el-button>\n          </div>\n        </div>\n        \n        <el-table :data=\"detailData\" stripe style=\"width: 100%\">\n          <el-table-column prop=\"venueName\" label=\"场地名称\" min-width=\"150\"></el-table-column>\n          <el-table-column prop=\"venueType\" label=\"场地类型\" width=\"120\"></el-table-column>\n          <el-table-column prop=\"totalCollections\" label=\"总收藏数\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"todayCollections\" label=\"今日新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"weekCollections\" label=\"本周新增\" width=\"100\" sortable></el-table-column>\n          <el-table-column prop=\"conversionRate\" label=\"转预约率\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              {{ scope.row.conversionRate }}%\n            </template>\n          </el-table-column>\n          <el-table-column prop=\"avgRating\" label=\"平均评分\" width=\"100\" sortable>\n            <template slot-scope=\"scope\">\n              <el-rate \n                v-model=\"scope.row.avgRating\" \n                disabled \n                show-score \n                text-color=\"#ff9900\"\n                score-template=\"{value}\">\n              </el-rate>\n            </template>\n          </el-table-column>\n        </el-table>\n      </el-card>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'CollectionAnalytics',\n  data() {\n    return {\n      // 核心指标\n      totalCollections: 1256,\n      todayIncrease: 23,\n      popularVenues: 15,\n      activeCollectors: 89,\n      avgCollectionsPerUser: 4.2,\n      \n      // 图表配置\n      trendPeriod: '7days',\n      \n      // 热门场地数据\n      topVenues: [\n        {\n          id: 1,\n          name: '中央体育馆篮球场',\n          type: '篮球场',\n          photo: '/static/images/basketball.jpg',\n          collections: 156\n        },\n        {\n          id: 2,\n          name: '游泳馆标准池',\n          type: '游泳池',\n          photo: '/static/images/swimming.jpg',\n          collections: 134\n        },\n        {\n          id: 3,\n          name: '网球场A区',\n          type: '网球场',\n          photo: '/static/images/tennis.jpg',\n          collections: 98\n        },\n        {\n          id: 4,\n          name: '羽毛球馆1号场',\n          type: '羽毛球场',\n          photo: '/static/images/badminton.jpg',\n          collections: 87\n        },\n        {\n          id: 5,\n          name: '足球场草坪',\n          type: '足球场',\n          photo: '/static/images/football.jpg',\n          collections: 76\n        }\n      ],\n      \n      // 时间分布数据\n      timeDistribution: [\n        { time: '6:00', percentage: 5 },\n        { time: '8:00', percentage: 15 },\n        { time: '10:00', percentage: 25 },\n        { time: '12:00', percentage: 35 },\n        { time: '14:00', percentage: 85 },\n        { time: '16:00', percentage: 90 },\n        { time: '18:00', percentage: 70 },\n        { time: '20:00', percentage: 45 },\n        { time: '22:00', percentage: 20 }\n      ],\n      \n      // 详细数据\n      detailData: [\n        {\n          venueName: '中央体育馆篮球场',\n          venueType: '篮球场',\n          totalCollections: 156,\n          todayCollections: 8,\n          weekCollections: 23,\n          conversionRate: 42.3,\n          avgRating: 4.5\n        },\n        {\n          venueName: '游泳馆标准池',\n          venueType: '游泳池',\n          totalCollections: 134,\n          todayCollections: 5,\n          weekCollections: 18,\n          conversionRate: 38.7,\n          avgRating: 4.3\n        },\n        {\n          venueName: '网球场A区',\n          venueType: '网球场',\n          totalCollections: 98,\n          todayCollections: 3,\n          weekCollections: 12,\n          conversionRate: 35.2,\n          avgRating: 4.2\n        }\n      ]\n    }\n  },\n  \n  mounted() {\n    this.initCharts()\n    this.loadAnalyticsData()\n  },\n  \n  methods: {\n    // 初始化图表\n    initCharts() {\n      this.$nextTick(() => {\n        this.initTrendChart()\n        this.initTypeChart()\n      })\n    },\n    \n    // 初始化趋势图\n    initTrendChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化收藏趋势图')\n    },\n    \n    // 初始化类型分布图\n    initTypeChart() {\n      // 这里应该使用真实的图表库如ECharts\n      console.log('初始化场地类型分布图')\n    },\n    \n    // 加载趋势数据\n    loadTrendData() {\n      console.log('加载趋势数据:', this.trendPeriod)\n    },\n    \n    // 加载分析数据\n    loadAnalyticsData() {\n      // 模拟数据加载\n      console.log('加载收藏分析数据')\n    },\n    \n    // 获取排名样式\n    getRankingClass(index) {\n      if (index === 0) return 'gold'\n      if (index === 1) return 'silver'\n      if (index === 2) return 'bronze'\n      return 'normal'\n    },\n    \n    // 导出数据\n    exportData() {\n      this.$message.info('导出功能开发中...')\n    },\n    \n    // 刷新数据\n    refreshData() {\n      this.loadAnalyticsData()\n      this.$message.success('数据已刷新')\n    },\n    \n    // 图片错误处理\n    handleImageError(event) {\n      event.target.src = '/static/images/default-venue.png'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collection-analytics {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-title {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n}\n\n.page-title i {\n  margin-right: 12px;\n  color: #409eff;\n}\n\n.page-description {\n  color: #909399;\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 核心指标卡片 */\n.metrics-section {\n  margin-bottom: 24px;\n}\n\n.metric-card {\n  background: white;\n  border-radius: 12px;\n  padding: 24px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  display: flex;\n  align-items: center;\n  transition: all 0.3s ease;\n  position: relative;\n  overflow: hidden;\n}\n\n.metric-card:hover {\n  transform: translateY(-4px);\n  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);\n}\n\n.metric-card::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: 4px;\n}\n\n.metric-card.primary::before {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success::before {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning::before {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info::before {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.metric-card.primary .metric-icon {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.metric-card.success .metric-icon {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.metric-card.warning .metric-icon {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.metric-card.info .metric-icon {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.metric-icon i {\n  font-size: 24px;\n  color: white;\n}\n\n.metric-content h3 {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.metric-content p {\n  margin: 0 0 8px 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n.metric-trend {\n  font-size: 12px;\n  padding: 2px 8px;\n  border-radius: 12px;\n  background: #f0f9ff;\n  color: #0369a1;\n}\n\n.metric-trend.up {\n  background: #f0fdf4;\n  color: #166534;\n}\n\n.metric-trend i {\n  margin-right: 2px;\n}\n\n/* 图表区域 */\n.charts-section {\n  margin-bottom: 24px;\n}\n\n.chart-card {\n  height: 400px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);\n  border-radius: 12px;\n}\n\n.chart-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n.chart-container {\n  height: 320px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 排行榜 */\n.ranking-list {\n  max-height: 320px;\n  overflow-y: auto;\n}\n\n.ranking-item {\n  display: flex;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n  transition: background-color 0.3s ease;\n}\n\n.ranking-item:hover {\n  background-color: #f8f9fa;\n}\n\n.ranking-item.top-three {\n  background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);\n  border-radius: 8px;\n  margin-bottom: 8px;\n  padding: 16px 12px;\n}\n\n.ranking-number {\n  width: 32px;\n  height: 32px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  font-weight: 700;\n  margin-right: 12px;\n  color: white;\n  font-size: 14px;\n}\n\n.ranking-number.gold {\n  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);\n}\n\n.ranking-number.silver {\n  background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);\n}\n\n.ranking-number.bronze {\n  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);\n}\n\n.ranking-number.normal {\n  background: #e5e7eb;\n  color: #6b7280;\n}\n\n.venue-info {\n  display: flex;\n  align-items: center;\n  flex: 1;\n}\n\n.venue-thumb {\n  width: 48px;\n  height: 48px;\n  border-radius: 8px;\n  object-fit: cover;\n  margin-right: 12px;\n  border: 1px solid #ebeef5;\n}\n\n.venue-details h4 {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.venue-details p {\n  margin: 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n.collection-count {\n  text-align: center;\n}\n\n.collection-count .count {\n  display: block;\n  font-size: 18px;\n  font-weight: 700;\n  color: #f56c6c;\n}\n\n.collection-count .label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 用户行为分析 */\n.behavior-stats {\n  margin-bottom: 24px;\n}\n\n.behavior-item {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 12px 0;\n  border-bottom: 1px solid #f0f0f0;\n}\n\n.behavior-label {\n  color: #606266;\n  font-size: 14px;\n}\n\n.behavior-value {\n  font-weight: 600;\n  color: #303133;\n  font-size: 16px;\n}\n\n.time-distribution h4 {\n  margin: 0 0 16px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.time-bars {\n  display: flex;\n  align-items: end;\n  justify-content: space-between;\n  height: 120px;\n  padding: 0 8px;\n}\n\n.time-bar {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  flex: 1;\n}\n\n.bar {\n  width: 20px;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 2px 2px 0 0;\n  margin-bottom: 8px;\n  min-height: 4px;\n  transition: all 0.3s ease;\n}\n\n.time-bar:hover .bar {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.time-label {\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 数据表格 */\n.data-table-section {\n  margin-bottom: 24px;\n}\n\n.table-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  font-weight: 600;\n  color: #303133;\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .collection-analytics {\n    padding: 12px;\n  }\n\n  .metrics-section .el-col {\n    margin-bottom: 12px;\n  }\n\n  .charts-section .el-col {\n    margin-bottom: 20px;\n  }\n\n  .metric-card {\n    flex-direction: column;\n    text-align: center;\n  }\n\n  .metric-icon {\n    margin-right: 0;\n    margin-bottom: 12px;\n  }\n\n  .venue-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .venue-thumb {\n    margin-bottom: 8px;\n  }\n}\n</style>\n"]}]}