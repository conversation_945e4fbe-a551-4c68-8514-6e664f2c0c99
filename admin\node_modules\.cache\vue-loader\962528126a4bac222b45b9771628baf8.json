{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue?vue&type=style&index=0&id=9ef81702&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue", "mtime": 1750589903927}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQouYm94LWNhcmQgew0KICBtYXJnaW4tcmlnaHQ6IDEwcHg7DQogIC5oZWFkZXIgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIH0NCiAgLmNvbnRlbnQgew0KICAgIGZvbnQtc2l6ZTogMzBweDsNCiAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICBjb2xvcjogIzY2NjsNCiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogICAgLnVuaXQgew0KICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgIH0NCiAgfQ0KICAuYm90dG9tIHsNCiAgICBkaXNwbGF5OiBmbGV4Ow0KICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIG1hcmdpbi10b3A6IDEwcHg7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["HomeCard.vue"], "names": [], "mappings": ";AA8DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HomeCard.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\r\n  <div class=\"quick-actions\">\r\n    <div class=\"action-item\" v-for=\"(action, index) in actions\" :key=\"index\" @click=\"handleAction(action)\">\r\n      <div class=\"action-icon\">\r\n        <i :class=\"action.icon\"></i>\r\n      </div>\r\n      <div class=\"action-content\">\r\n        <div class=\"action-title\">{{ action.title }}</div>\r\n        <div class=\"action-desc\">{{ action.description }}</div>\r\n      </div>\r\n      <div class=\"action-arrow\">\r\n        <i class=\"el-icon-arrow-right\"></i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      actions: [\r\n        {\r\n          title: '场地管理',\r\n          description: '管理体育场地信息',\r\n          icon: 'el-icon-office-building',\r\n          route: '/changdi'\r\n        },\r\n        {\r\n          title: '预约管理',\r\n          description: '查看和管理预约订单',\r\n          icon: 'el-icon-date',\r\n          route: '/changdiOrder'\r\n        },\r\n        {\r\n          title: '用户管理',\r\n          description: '管理系统用户',\r\n          icon: 'el-icon-user',\r\n          route: '/yonghu'\r\n        },\r\n        {\r\n          title: '公告管理',\r\n          description: '发布和管理公告',\r\n          icon: 'el-icon-bell',\r\n          route: '/gonggao'\r\n        },\r\n        {\r\n          title: '论坛管理',\r\n          description: '管理论坛内容',\r\n          icon: 'el-icon-chat-dot-round',\r\n          route: '/forum'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    handleAction(action) {\r\n      this.$router.push(action.route)\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.box-card {\r\n  margin-right: 10px;\r\n  .header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n  .content {\r\n    font-size: 30px;\r\n    font-weight: bold;\r\n    color: #666;\r\n    text-align: center;\r\n    .unit {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n  .bottom {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}