{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue?vue&type=style&index=0&id=9ef81702&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue", "mtime": 1750589925172}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQoucXVpY2stYWN0aW9ucyB7DQogIC5hY3Rpb24taXRlbSB7DQogICAgZGlzcGxheTogZmxleDsNCiAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgIHBhZGRpbmc6IDE1cHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsNCiAgICBib3JkZXItcmFkaXVzOiA4cHg7DQogICAgYm94LXNoYWRvdzogMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4xKTsNCiAgICBjdXJzb3I6IHBvaW50ZXI7DQogICAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTsNCg0KICAgICY6aG92ZXIgew0KICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOw0KICAgICAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMTUpOw0KICAgICAgYmFja2dyb3VuZDogI2Y4ZjlmYTsNCiAgICB9DQoNCiAgICAmOmxhc3QtY2hpbGQgew0KICAgICAgbWFyZ2luLWJvdHRvbTogMDsNCiAgICB9DQoNCiAgICAuYWN0aW9uLWljb24gew0KICAgICAgd2lkdGg6IDQwcHg7DQogICAgICBoZWlnaHQ6IDQwcHg7DQogICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjMDBjMjkyIDAlLCAjMDBhMDg1IDEwMCUpOw0KICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgICAgIG1hcmdpbi1yaWdodDogMTVweDsNCg0KICAgICAgaSB7DQogICAgICAgIGZvbnQtc2l6ZTogMThweDsNCiAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgfQ0KICAgIH0NCg0KICAgIC5hY3Rpb24tY29udGVudCB7DQogICAgICBmbGV4OiAxOw0KDQogICAgICAuYWN0aW9uLXRpdGxlIHsNCiAgICAgICAgZm9udC1zaXplOiAxNnB4Ow0KICAgICAgICBmb250LXdlaWdodDogYm9sZDsNCiAgICAgICAgY29sb3I6ICMyYzNlNTA7DQogICAgICAgIG1hcmdpbi1ib3R0b206IDRweDsNCiAgICAgIH0NCg0KICAgICAgLmFjdGlvbi1kZXNjIHsNCiAgICAgICAgZm9udC1zaXplOiAxMnB4Ow0KICAgICAgICBjb2xvcjogIzkwOTM5OTsNCiAgICAgIH0NCiAgICB9DQoNCiAgICAuYWN0aW9uLWFycm93IHsNCiAgICAgIGNvbG9yOiAjYzBjNGNjOw0KICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgIH0NCiAgfQ0KfQ0K"}, {"version": 3, "sources": ["HomeCard.vue"], "names": [], "mappings": ";AA8DA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "HomeCard.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\r\n  <div class=\"quick-actions\">\r\n    <div class=\"action-item\" v-for=\"(action, index) in actions\" :key=\"index\" @click=\"handleAction(action)\">\r\n      <div class=\"action-icon\">\r\n        <i :class=\"action.icon\"></i>\r\n      </div>\r\n      <div class=\"action-content\">\r\n        <div class=\"action-title\">{{ action.title }}</div>\r\n        <div class=\"action-desc\">{{ action.description }}</div>\r\n      </div>\r\n      <div class=\"action-arrow\">\r\n        <i class=\"el-icon-arrow-right\"></i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      actions: [\r\n        {\r\n          title: '场地管理',\r\n          description: '管理体育场地信息',\r\n          icon: 'el-icon-office-building',\r\n          route: '/changdi'\r\n        },\r\n        {\r\n          title: '预约管理',\r\n          description: '查看和管理预约订单',\r\n          icon: 'el-icon-date',\r\n          route: '/changdiOrder'\r\n        },\r\n        {\r\n          title: '用户管理',\r\n          description: '管理系统用户',\r\n          icon: 'el-icon-user',\r\n          route: '/yonghu'\r\n        },\r\n        {\r\n          title: '公告管理',\r\n          description: '发布和管理公告',\r\n          icon: 'el-icon-bell',\r\n          route: '/gonggao'\r\n        },\r\n        {\r\n          title: '论坛管理',\r\n          description: '管理论坛内容',\r\n          icon: 'el-icon-chat-dot-round',\r\n          route: '/forum'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    handleAction(action) {\r\n      this.$router.push(action.route)\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.quick-actions {\r\n  .action-item {\r\n    display: flex;\r\n    align-items: center;\r\n    padding: 15px;\r\n    margin-bottom: 10px;\r\n    background: white;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n    cursor: pointer;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      transform: translateY(-2px);\r\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\r\n      background: #f8f9fa;\r\n    }\r\n\r\n    &:last-child {\r\n      margin-bottom: 0;\r\n    }\r\n\r\n    .action-icon {\r\n      width: 40px;\r\n      height: 40px;\r\n      border-radius: 50%;\r\n      background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 15px;\r\n\r\n      i {\r\n        font-size: 18px;\r\n        color: white;\r\n      }\r\n    }\r\n\r\n    .action-content {\r\n      flex: 1;\r\n\r\n      .action-title {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #2c3e50;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .action-desc {\r\n        font-size: 12px;\r\n        color: #909399;\r\n      }\r\n    }\r\n\r\n    .action-arrow {\r\n      color: #c0c4cc;\r\n      font-size: 14px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}