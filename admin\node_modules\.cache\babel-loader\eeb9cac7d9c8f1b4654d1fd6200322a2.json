{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\@babel\\runtime\\helpers\\esm\\classCallCheck.js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\node_modules\\@babel\\runtime\\helpers\\esm\\classCallCheck.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuZXJyb3IuY2F1c2UuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5lcnJvci50by1zdHJpbmcuanMiOwpmdW5jdGlvbiBfY2xhc3NDYWxsQ2hlY2soYSwgbikgewogIGlmICghKGEgaW5zdGFuY2VvZiBuKSkgdGhyb3cgbmV3IFR5cGVFcnJvcigiQ2Fubm90IGNhbGwgYSBjbGFzcyBhcyBhIGZ1bmN0aW9uIik7Cn0KZXhwb3J0IHsgX2NsYXNzQ2FsbENoZWNrIGFzIGRlZmF1bHQgfTs="}, {"version": 3, "names": ["_classCallCheck", "a", "n", "TypeError", "default"], "sources": ["D:/1/tiyuguan/admin/node_modules/@babel/runtime/helpers/esm/classCallCheck.js"], "sourcesContent": ["function _classCallCheck(a, n) {\n  if (!(a instanceof n)) throw new TypeError(\"Cannot call a class as a function\");\n}\nexport { _classCallCheck as default };"], "mappings": ";;AAAA,SAASA,eAAeA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC7B,IAAI,EAAED,CAAC,YAAYC,CAAC,CAAC,EAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;AACjF;AACA,SAASH,eAAe,IAAII,OAAO", "ignoreList": []}]}