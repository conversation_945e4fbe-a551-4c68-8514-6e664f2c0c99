{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-management.vue", "mtime": 1750603368047}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["collection-management.vue"], "names": [], "mappings": ";AAwQA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "collection-management.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"collection-management\">\n    <!-- 页面标题 -->\n    <div class=\"page-header\">\n      <h1 class=\"page-title\">\n        <i class=\"el-icon-star-on\"></i>\n        收藏管理\n      </h1>\n      <p class=\"page-description\">管理用户的场地收藏记录，查看收藏统计和趋势</p>\n    </div>\n\n    <!-- 统计卡片 -->\n    <div class=\"stats-cards\">\n      <el-row :gutter=\"20\">\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon total\">\n              <i class=\"el-icon-star-on\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ totalCollections }}</h3>\n              <p>总收藏数</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon today\">\n              <i class=\"el-icon-date\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ todayCollections }}</h3>\n              <p>今日新增</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon users\">\n              <i class=\"el-icon-user\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ activeUsers }}</h3>\n              <p>活跃用户</p>\n            </div>\n          </div>\n        </el-col>\n        <el-col :span=\"6\">\n          <div class=\"stat-card\">\n            <div class=\"stat-icon venues\">\n              <i class=\"el-icon-location\"></i>\n            </div>\n            <div class=\"stat-content\">\n              <h3>{{ popularVenues }}</h3>\n              <p>热门场地</p>\n            </div>\n          </div>\n        </el-col>\n      </el-row>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"search-section\">\n      <el-card>\n        <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\n          <el-form-item label=\"场地名称\">\n            <el-input\n              v-model=\"searchForm.changdiName\"\n              placeholder=\"请输入场地名称\"\n              clearable\n              @keyup.enter.native=\"handleSearch\">\n            </el-input>\n          </el-form-item>\n          <el-form-item v-if=\"isAdmin\" label=\"用户姓名\">\n            <el-input\n              v-model=\"searchForm.yonghuName\"\n              placeholder=\"请输入用户姓名\"\n              clearable\n              @keyup.enter.native=\"handleSearch\">\n            </el-input>\n          </el-form-item>\n          <el-form-item label=\"收藏时间\">\n            <el-date-picker\n              v-model=\"searchForm.dateRange\"\n              type=\"daterange\"\n              range-separator=\"至\"\n              start-placeholder=\"开始日期\"\n              end-placeholder=\"结束日期\"\n              format=\"yyyy-MM-dd\"\n              value-format=\"yyyy-MM-dd\">\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item>\n            <el-button type=\"primary\" @click=\"handleSearch\" icon=\"el-icon-search\">搜索</el-button>\n            <el-button @click=\"handleReset\" icon=\"el-icon-refresh\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-card>\n    </div>\n\n    <!-- 操作栏 -->\n    <div class=\"action-bar\">\n      <div class=\"action-left\">\n        <el-button\n          type=\"danger\"\n          :disabled=\"selectedCollections.length === 0\"\n          @click=\"handleBatchDelete\"\n          icon=\"el-icon-delete\">\n          批量删除 ({{ selectedCollections.length }})\n        </el-button>\n        <el-button\n          type=\"success\"\n          @click=\"exportCollections\"\n          icon=\"el-icon-download\">\n          导出数据\n        </el-button>\n        <el-button\n          v-if=\"isAdmin\"\n          type=\"primary\"\n          @click=\"syncToServer\"\n          :loading=\"syncing\"\n          icon=\"el-icon-upload2\">\n          同步到服务器\n        </el-button>\n        <el-button\n          v-if=\"isAdmin\"\n          type=\"info\"\n          @click=\"pullFromServer\"\n          :loading=\"pulling\"\n          icon=\"el-icon-download\">\n          从服务器拉取\n        </el-button>\n      </div>\n      <div class=\"action-right\">\n        <el-tag v-if=\"syncStatus.status !== 'never'\" :type=\"getSyncStatusType()\">\n          {{ getSyncStatusText() }}\n        </el-tag>\n        <el-button @click=\"loadCollections\" icon=\"el-icon-refresh\">刷新</el-button>\n      </div>\n    </div>\n\n    <!-- 收藏列表 -->\n    <div class=\"collection-table\">\n      <el-table\n        v-loading=\"loading\"\n        :data=\"collectionList\"\n        @selection-change=\"handleSelectionChange\"\n        stripe\n        style=\"width: 100%\">\n        \n        <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n        \n        <el-table-column prop=\"id\" label=\"ID\" width=\"80\"></el-table-column>\n        \n        <el-table-column label=\"场地信息\" min-width=\"200\">\n          <template slot-scope=\"scope\">\n            <div class=\"venue-info\">\n              <img \n                v-if=\"scope.row.changdiPhoto\" \n                :src=\"scope.row.changdiPhoto.split(',')[0]\" \n                class=\"venue-image\"\n                @error=\"handleImageError\">\n              <div class=\"venue-details\">\n                <h4>{{ scope.row.changdiName }}</h4>\n                <p class=\"venue-type\">{{ scope.row.changdiValue }}</p>\n                <p class=\"venue-price\">¥{{ scope.row.changdiNewMoney }}</p>\n              </div>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column v-if=\"isAdmin\" label=\"用户信息\" min-width=\"150\">\n          <template slot-scope=\"scope\">\n            <div class=\"user-info\">\n              <p class=\"user-name\">{{ scope.row.yonghuName }}</p>\n              <p class=\"user-phone\">{{ scope.row.yonghuPhone }}</p>\n            </div>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"changdiCollectionValue\" label=\"收藏类型\" width=\"100\">\n          <template slot-scope=\"scope\">\n            <el-tag :type=\"getCollectionTypeTag(scope.row.changdiCollectionTypes)\">\n              {{ scope.row.changdiCollectionValue }}\n            </el-tag>\n          </template>\n        </el-table-column>\n        \n        <el-table-column prop=\"insertTime\" label=\"收藏时间\" width=\"180\">\n          <template slot-scope=\"scope\">\n            {{ formatDate(scope.row.insertTime) }}\n          </template>\n        </el-table-column>\n        \n        <el-table-column label=\"操作\" width=\"150\" fixed=\"right\">\n          <template slot-scope=\"scope\">\n            <el-button\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"viewDetails(scope.row)\"\n              icon=\"el-icon-view\">\n              查看\n            </el-button>\n            <el-button\n              size=\"mini\"\n              type=\"danger\"\n              @click=\"handleDelete(scope.row)\"\n              icon=\"el-icon-delete\">\n              删除\n            </el-button>\n          </template>\n        </el-table-column>\n      </el-table>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-wrapper\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50, 100]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 详情对话框 -->\n    <el-dialog\n      title=\"收藏详情\"\n      :visible.sync=\"detailDialogVisible\"\n      width=\"600px\">\n      <div v-if=\"currentCollection\" class=\"collection-detail\">\n        <el-descriptions :column=\"2\" border>\n          <el-descriptions-item label=\"收藏ID\">{{ currentCollection.id }}</el-descriptions-item>\n          <el-descriptions-item label=\"场地名称\">{{ currentCollection.changdiName }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户姓名\">{{ currentCollection.yonghuName }}</el-descriptions-item>\n          <el-descriptions-item label=\"用户电话\">{{ currentCollection.yonghuPhone }}</el-descriptions-item>\n          <el-descriptions-item label=\"收藏类型\">{{ currentCollection.changdiCollectionValue }}</el-descriptions-item>\n          <el-descriptions-item label=\"收藏时间\">{{ formatDate(currentCollection.insertTime) }}</el-descriptions-item>\n        </el-descriptions>\n        \n        <div class=\"venue-preview\" v-if=\"currentCollection.changdiPhoto\">\n          <h4>场地图片</h4>\n          <div class=\"image-gallery\">\n            <img \n              v-for=\"(image, index) in currentCollection.changdiPhoto.split(',')\" \n              :key=\"index\"\n              :src=\"image\" \n              class=\"preview-image\"\n              @error=\"handleImageError\">\n          </div>\n        </div>\n      </div>\n      \n      <span slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"detailDialogVisible = false\">关闭</el-button>\n      </span>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport collectionStorage from '@/utils/collection-storage'\n\nexport default {\n  name: 'CollectionManagement',\n  data() {\n    return {\n      loading: false,\n      collectionList: [],\n      selectedCollections: [],\n      currentPage: 1,\n      pageSize: 20,\n      totalCount: 0,\n      \n      // 统计数据\n      totalCollections: 0,\n      todayCollections: 0,\n      activeUsers: 0,\n      popularVenues: 0,\n      \n      // 搜索表单\n      searchForm: {\n        changdiName: '',\n        yonghuName: '',\n        dateRange: []\n      },\n      \n      // 详情对话框\n      detailDialogVisible: false,\n      currentCollection: null,\n\n      // 同步状态\n      syncing: false,\n      pulling: false,\n      syncStatus: { status: 'never', lastSync: null }\n    }\n  },\n\n  computed: {\n    // 检查是否为管理员\n    isAdmin() {\n      return this.$storage.get('role') === '管理员'\n    }\n  },\n\n  mounted() {\n    this.loadCollections()\n    this.loadStats()\n    this.loadLocalCollections()\n    this.syncStatus = collectionStorage.getSyncStatus()\n  },\n  \n  methods: {\n    // 加载收藏列表\n    loadCollections() {\n      this.loading = true\n\n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize\n      }\n\n      // 如果是普通用户，只显示自己的收藏\n      const userRole = this.$storage.get('role')\n      if (userRole === '用户') {\n        params.yonghuId = this.$storage.get('userid')\n      }\n\n      // 添加搜索条件\n      if (this.searchForm.changdiName) {\n        params.changdiName = this.searchForm.changdiName\n      }\n      if (this.searchForm.yonghuName && userRole !== '用户') {\n        // 普通用户不能搜索其他用户\n        params.yonghuName = this.searchForm.yonghuName\n      }\n      if (this.searchForm.dateRange && this.searchForm.dateRange.length === 2) {\n        params.insertTimeStart = this.searchForm.dateRange[0]\n        params.insertTimeEnd = this.searchForm.dateRange[1]\n      }\n      \n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        this.loading = false\n        if (data && data.code === 0) {\n          this.collectionList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        } else {\n          this.$message.error(data.msg || '获取收藏列表失败')\n        }\n      }).catch((error) => {\n        this.loading = false\n        console.error('加载收藏列表失败:', error)\n        // 加载本地收藏数据作为备用\n        this.loadLocalCollections()\n      })\n    },\n\n    // 加载本地收藏数据\n    loadLocalCollections() {\n      try {\n        const localCollections = collectionStorage.getAllCollections()\n        if (localCollections.length > 0 && this.collectionList.length === 0) {\n          this.collectionList = localCollections\n          this.totalCount = localCollections.length\n          this.$message.info(`已加载本地收藏数据 (${localCollections.length} 条)`)\n        }\n      } catch (error) {\n        console.error('加载本地收藏数据失败:', error)\n      }\n    },\n\n    // 同步到服务器\n    async syncToServer() {\n      this.syncing = true\n      try {\n        const result = await collectionStorage.syncToServer(this.$http)\n        if (result.success) {\n          this.$message.success(result.message)\n          this.loadCollections() // 重新加载数据\n        } else {\n          this.$message.error(result.message)\n        }\n        this.syncStatus = collectionStorage.getSyncStatus()\n      } catch (error) {\n        console.error('同步失败:', error)\n        this.$message.error('同步失败: ' + error.message)\n      } finally {\n        this.syncing = false\n      }\n    },\n\n    // 从服务器拉取数据\n    async pullFromServer() {\n      this.pulling = true\n      try {\n        const result = await collectionStorage.pullFromServer(this.$http)\n        if (result.success) {\n          this.$message.success(result.message)\n          this.loadLocalCollections() // 重新加载本地数据\n        } else {\n          this.$message.error(result.message)\n        }\n        this.syncStatus = collectionStorage.getSyncStatus()\n      } catch (error) {\n        console.error('拉取数据失败:', error)\n        this.$message.error('拉取数据失败: ' + error.message)\n      } finally {\n        this.pulling = false\n      }\n    },\n\n    // 获取同步状态类型\n    getSyncStatusType() {\n      switch (this.syncStatus.status) {\n        case 'pending': return 'warning'\n        case 'completed': return 'success'\n        case 'failed': return 'danger'\n        default: return 'info'\n      }\n    },\n\n    // 获取同步状态文本\n    getSyncStatusText() {\n      switch (this.syncStatus.status) {\n        case 'pending': return '待同步'\n        case 'completed': return '已同步'\n        case 'failed': return '同步失败'\n        case 'never': return '从未同步'\n        default: return '未知状态'\n      }\n    },\n\n    // 加载统计数据\n    loadStats() {\n      // 获取总收藏数\n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalCollections = data.data.total || 0\n        }\n      }).catch(() => {\n        // 使用本地数据作为备用\n        const localStats = collectionStorage.getCollectionStats()\n        this.totalCollections = localStats.total\n        this.todayCollections = localStats.today\n      })\n\n      // 获取今日收藏数\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'changdiCollection/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 1000,\n          insertTimeStart: today,\n          insertTimeEnd: today\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.todayCollections = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取今日收藏数失败，使用本地数据')\n      })\n\n      // 获取活跃用户数（简化统计）\n      this.activeUsers = 89\n      this.popularVenues = 23\n    },\n\n    // 搜索\n    handleSearch() {\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 重置搜索\n    handleReset() {\n      this.searchForm = {\n        changdiName: '',\n        yonghuName: '',\n        dateRange: []\n      }\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 选择变化\n    handleSelectionChange(selection) {\n      this.selectedCollections = selection\n    },\n\n    // 批量删除\n    handleBatchDelete() {\n      if (this.selectedCollections.length === 0) {\n        this.$message.warning('请选择要删除的收藏记录')\n        return\n      }\n\n      this.$confirm(`确定要删除选中的 ${this.selectedCollections.length} 条收藏记录吗？`, '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        const ids = this.selectedCollections.map(item => item.id)\n        this.deleteCollections(ids)\n      })\n    },\n\n    // 单个删除\n    handleDelete(row) {\n      this.$confirm('确定要删除这条收藏记录吗？', '确认删除', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        this.deleteCollections([row.id])\n      })\n    },\n\n    // 删除收藏\n    deleteCollections(ids) {\n      this.$http({\n        url: 'changdiCollection/delete',\n        method: 'post',\n        data: ids\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.$message.success('删除成功')\n          this.loadCollections()\n          this.selectedCollections = []\n        } else {\n          this.$message.error(data.msg || '删除失败')\n        }\n      }).catch((error) => {\n        console.error('删除失败:', error)\n        this.$message.error('删除失败，请稍后重试')\n      })\n    },\n\n    // 查看详情\n    viewDetails(row) {\n      this.currentCollection = row\n      this.detailDialogVisible = true\n    },\n\n    // 导出数据\n    exportCollections() {\n      try {\n        collectionStorage.exportCollections()\n        this.$message.success('收藏数据已导出')\n      } catch (error) {\n        console.error('导出失败:', error)\n        this.$message.error('导出失败: ' + error.message)\n      }\n    },\n\n    // 分页大小变化\n    handleSizeChange(size) {\n      this.pageSize = size\n      this.currentPage = 1\n      this.loadCollections()\n    },\n\n    // 当前页变化\n    handleCurrentChange(page) {\n      this.currentPage = page\n      this.loadCollections()\n    },\n\n    // 获取收藏类型标签\n    getCollectionTypeTag(type) {\n      switch (type) {\n        case 1: return 'success'\n        case 2: return 'warning'\n        case 3: return 'danger'\n        default: return 'info'\n      }\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return '-'\n      return new Date(date).toLocaleString('zh-CN')\n    },\n\n    // 图片错误处理\n    handleImageError(event) {\n      event.target.src = '/static/images/default-venue.png'\n    }\n  }\n}\n</script>\n\n<style scoped>\n.collection-management {\n  padding: 20px;\n  background-color: #f5f7fa;\n  min-height: 100vh;\n}\n\n/* 页面标题 */\n.page-header {\n  margin-bottom: 24px;\n}\n\n.page-title {\n  font-size: 28px;\n  font-weight: 600;\n  color: #303133;\n  margin: 0 0 8px 0;\n  display: flex;\n  align-items: center;\n}\n\n.page-title i {\n  margin-right: 12px;\n  color: #409eff;\n}\n\n.page-description {\n  color: #909399;\n  margin: 0;\n  font-size: 14px;\n}\n\n/* 统计卡片 */\n.stats-cards {\n  margin-bottom: 24px;\n}\n\n.stat-card {\n  background: white;\n  border-radius: 8px;\n  padding: 24px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  display: flex;\n  align-items: center;\n  transition: transform 0.3s ease;\n}\n\n.stat-card:hover {\n  transform: translateY(-2px);\n}\n\n.stat-icon {\n  width: 60px;\n  height: 60px;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 16px;\n}\n\n.stat-icon i {\n  font-size: 24px;\n  color: white;\n}\n\n.stat-icon.total {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.stat-icon.today {\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n}\n\n.stat-icon.users {\n  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n}\n\n.stat-icon.venues {\n  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n}\n\n.stat-content h3 {\n  font-size: 32px;\n  font-weight: 700;\n  margin: 0 0 4px 0;\n  color: #303133;\n}\n\n.stat-content p {\n  margin: 0;\n  color: #909399;\n  font-size: 14px;\n}\n\n/* 搜索区域 */\n.search-section {\n  margin-bottom: 20px;\n}\n\n.search-form {\n  margin: 0;\n}\n\n/* 操作栏 */\n.action-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 20px;\n  padding: 16px 20px;\n  background: white;\n  border-radius: 8px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* 表格区域 */\n.collection-table {\n  background: white;\n  border-radius: 8px;\n  overflow: hidden;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n}\n\n/* 场地信息 */\n.venue-info {\n  display: flex;\n  align-items: center;\n}\n\n.venue-image {\n  width: 60px;\n  height: 60px;\n  border-radius: 8px;\n  object-fit: cover;\n  margin-right: 12px;\n  border: 1px solid #ebeef5;\n}\n\n.venue-details h4 {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.venue-type {\n  margin: 0 0 4px 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n.venue-price {\n  margin: 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #f56c6c;\n}\n\n/* 用户信息 */\n.user-info .user-name {\n  margin: 0 0 4px 0;\n  font-size: 14px;\n  font-weight: 600;\n  color: #303133;\n}\n\n.user-info .user-phone {\n  margin: 0;\n  font-size: 12px;\n  color: #909399;\n}\n\n/* 分页 */\n.pagination-wrapper {\n  margin-top: 20px;\n  text-align: center;\n}\n\n/* 详情对话框 */\n.collection-detail {\n  padding: 20px 0;\n}\n\n.venue-preview {\n  margin-top: 20px;\n}\n\n.venue-preview h4 {\n  margin: 0 0 12px 0;\n  font-size: 16px;\n  color: #303133;\n}\n\n.image-gallery {\n  display: flex;\n  flex-wrap: wrap;\n  gap: 12px;\n}\n\n.preview-image {\n  width: 120px;\n  height: 120px;\n  border-radius: 8px;\n  object-fit: cover;\n  border: 1px solid #ebeef5;\n  cursor: pointer;\n  transition: transform 0.3s ease;\n}\n\n.preview-image:hover {\n  transform: scale(1.05);\n}\n\n/* 响应式设计 */\n@media (max-width: 768px) {\n  .collection-management {\n    padding: 12px;\n  }\n\n  .stats-cards .el-col {\n    margin-bottom: 12px;\n  }\n\n  .action-bar {\n    flex-direction: column;\n    gap: 12px;\n  }\n\n  .venue-info {\n    flex-direction: column;\n    align-items: flex-start;\n  }\n\n  .venue-image {\n    margin-bottom: 8px;\n  }\n}\n</style>\n"]}]}