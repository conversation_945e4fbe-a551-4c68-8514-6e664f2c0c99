{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\register.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\register.vue", "mtime": 1750594699322}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "loading", "ruleForm", "username", "password", "repetitionPassword", "yo<PERSON><PERSON><PERSON><PERSON>", "yonghuPhone", "yonghuIdNumber", "yonghuEmail", "tableName", "rules", "sexTypesOptions", "mounted", "table", "$storage", "get", "methods", "getUUID", "Date", "getTime", "close", "$router", "push", "path", "validateForm", "$message", "error", "length", "$validate", "isMobile", "isEmail", "register", "_this", "$http", "url", "concat", "method", "then", "_ref", "code", "message", "type", "duration", "onClose", "replace", "msg", "catch"], "sources": ["src/views/register.vue"], "sourcesContent": ["<template>\r\n    <div class=\"register-container\">\r\n        <!-- 背景装饰 -->\r\n        <div class=\"background-decoration\">\r\n            <div class=\"decoration-circle circle-1\"></div>\r\n            <div class=\"decoration-circle circle-2\"></div>\r\n            <div class=\"decoration-circle circle-3\"></div>\r\n        </div>\r\n\r\n        <!-- 主要内容区域 -->\r\n        <div class=\"main-content\">\r\n            <!-- 左侧信息区域 -->\r\n            <div class=\"info-section\">\r\n                <div class=\"brand-info\">\r\n                    <div class=\"logo-container\">\r\n                        <i class=\"el-icon-user-solid\"></i>\r\n                        <h1>用户注册</h1>\r\n                    </div>\r\n                    <p class=\"brand-description\">\r\n                        加入我们的体育馆管理平台<br>\r\n                        享受便捷的场地预约服务\r\n                    </p>\r\n                    <div class=\"features\">\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-check\"></i>\r\n                            <span>快速注册，立即使用</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-star-on\"></i>\r\n                            <span>专业的场地管理</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-service\"></i>\r\n                            <span>贴心的客户服务</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 右侧注册表单 -->\r\n            <div class=\"register-section\">\r\n                <div class=\"register-card\">\r\n                    <div class=\"card-header\">\r\n                        <h2>创建新账户</h2>\r\n                        <p>请填写以下信息完成注册</p>\r\n                    </div>\r\n\r\n                    <el-form ref=\"rgsForm\" class=\"register-form\" :model=\"ruleForm\" @submit.native.prevent=\"register\">\r\n                        <div class=\"form-row\">\r\n                            <el-form-item class=\"form-item\">\r\n                                <div class=\"input-wrapper\">\r\n                                    <i class=\"el-icon-user input-icon\"></i>\r\n                                    <el-input\r\n                                        v-model=\"ruleForm.username\"\r\n                                        placeholder=\"请输入账号\"\r\n                                        size=\"large\"\r\n                                        clearable>\r\n                                    </el-input>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </div>\r\n\r\n                        <div class=\"form-row\">\r\n                            <el-form-item class=\"form-item half-width\">\r\n                                <div class=\"input-wrapper\">\r\n                                    <i class=\"el-icon-lock input-icon\"></i>\r\n                                    <el-input\r\n                                        v-model=\"ruleForm.password\"\r\n                                        type=\"password\"\r\n                                        placeholder=\"请输入密码\"\r\n                                        size=\"large\"\r\n                                        show-password\r\n                                        clearable>\r\n                                    </el-input>\r\n                                </div>\r\n                            </el-form-item>\r\n\r\n                            <el-form-item class=\"form-item half-width\">\r\n                                <div class=\"input-wrapper\">\r\n                                    <i class=\"el-icon-key input-icon\"></i>\r\n                                    <el-input\r\n                                        v-model=\"ruleForm.repetitionPassword\"\r\n                                        type=\"password\"\r\n                                        placeholder=\"确认密码\"\r\n                                        size=\"large\"\r\n                                        show-password\r\n                                        clearable>\r\n                                    </el-input>\r\n                                </div>\r\n                            </el-form-item>\r\n                        </div>\r\n\r\n                        <div v-if=\"tableName=='yonghu'\" class=\"user-info-section\">\r\n                            <div class=\"section-title\">\r\n                                <i class=\"el-icon-info\"></i>\r\n                                个人信息\r\n                            </div>\r\n\r\n                            <div class=\"form-row\">\r\n                                <el-form-item class=\"form-item half-width\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-user-solid input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuName\"\r\n                                            placeholder=\"请输入真实姓名\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n\r\n                                <el-form-item class=\"form-item half-width\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-phone input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuPhone\"\r\n                                            placeholder=\"请输入手机号\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n                            </div>\r\n\r\n                            <div class=\"form-row\">\r\n                                <el-form-item class=\"form-item\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-postcard input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuIdNumber\"\r\n                                            placeholder=\"请输入身份证号\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n                            </div>\r\n\r\n                            <div class=\"form-row\">\r\n                                <el-form-item class=\"form-item\">\r\n                                    <div class=\"input-wrapper\">\r\n                                        <i class=\"el-icon-message input-icon\"></i>\r\n                                        <el-input\r\n                                            v-model=\"ruleForm.yonghuEmail\"\r\n                                            placeholder=\"请输入电子邮箱\"\r\n                                            size=\"large\"\r\n                                            clearable>\r\n                                        </el-input>\r\n                                    </div>\r\n                                </el-form-item>\r\n                            </div>\r\n                        </div>\r\n\r\n                        <div class=\"form-actions\">\r\n                            <el-button\r\n                                type=\"primary\"\r\n                                @click=\"register()\"\r\n                                class=\"register-button\"\r\n                                size=\"large\"\r\n                                :loading=\"loading\">\r\n                                <i class=\"el-icon-check\"></i>\r\n                                立即注册\r\n                            </el-button>\r\n\r\n                            <el-button\r\n                                @click=\"close()\"\r\n                                class=\"cancel-button\"\r\n                                size=\"large\">\r\n                                <i class=\"el-icon-close\"></i>\r\n                                取消\r\n                            </el-button>\r\n                        </div>\r\n\r\n                        <div class=\"form-footer\">\r\n                            <div class=\"login-link\" @click=\"close()\">\r\n                                <i class=\"el-icon-back\"></i>\r\n                                已有账户？立即登录\r\n                            </div>\r\n                        </div>\r\n                    </el-form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                ruleForm: {\r\n                    username: '',\r\n                    password: '',\r\n                    repetitionPassword: '',\r\n                    yonghuName: '',\r\n                    yonghuPhone: '',\r\n                    yonghuIdNumber: '',\r\n                    yonghuEmail: ''\r\n                },\r\n                tableName: \"\",\r\n                rules: {},\r\n                sexTypesOptions: [],\r\n            };\r\n        },\r\n        mounted(){\r\n            let table = this.$storage.get(\"loginTable\");\r\n            this.tableName = table || 'yonghu';\r\n        },\r\n        methods: {\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n\r\n            // 返回登录页面\r\n            close(){\r\n                this.$router.push({ path: \"/login\" });\r\n            },\r\n\r\n            // 验证表单\r\n            validateForm() {\r\n                // 基础验证\r\n                if (!this.ruleForm.username) {\r\n                    this.$message.error('账号不能为空');\r\n                    return false;\r\n                }\r\n                if (this.ruleForm.username.length < 3) {\r\n                    this.$message.error('账号长度至少3位');\r\n                    return false;\r\n                }\r\n                if (!this.ruleForm.password) {\r\n                    this.$message.error('密码不能为空');\r\n                    return false;\r\n                }\r\n                if (this.ruleForm.password.length < 6) {\r\n                    this.$message.error('密码长度至少6位');\r\n                    return false;\r\n                }\r\n                if (!this.ruleForm.repetitionPassword) {\r\n                    this.$message.error('请确认密码');\r\n                    return false;\r\n                }\r\n                if (this.ruleForm.repetitionPassword !== this.ruleForm.password) {\r\n                    this.$message.error('两次输入的密码不一致');\r\n                    return false;\r\n                }\r\n\r\n                // 用户信息验证\r\n                if (this.tableName === 'yonghu') {\r\n                    if (!this.ruleForm.yonghuName) {\r\n                        this.$message.error('用户姓名不能为空');\r\n                        return false;\r\n                    }\r\n                    if (this.ruleForm.yonghuPhone && !this.$validate.isMobile(this.ruleForm.yonghuPhone)) {\r\n                        this.$message.error('请输入正确的手机号格式');\r\n                        return false;\r\n                    }\r\n                    if (!this.ruleForm.yonghuIdNumber) {\r\n                        this.$message.error('身份证号不能为空');\r\n                        return false;\r\n                    }\r\n                    if (this.ruleForm.yonghuEmail && !this.$validate.isEmail(this.ruleForm.yonghuEmail)) {\r\n                        this.$message.error('请输入正确的邮箱格式');\r\n                        return false;\r\n                    }\r\n                }\r\n\r\n                return true;\r\n            },\r\n\r\n            // 注册\r\n            register() {\r\n                if (!this.validateForm()) {\r\n                    return;\r\n                }\r\n\r\n                this.loading = true;\r\n                this.$http({\r\n                    url: `${this.tableName}/register`,\r\n                    method: \"post\",\r\n                    data: this.ruleForm\r\n                }).then(({ data }) => {\r\n                    this.loading = false;\r\n                    if (data && data.code === 0) {\r\n                        this.$message({\r\n                            message: \"注册成功！请登录后在个人中心完善个人信息\",\r\n                            type: \"success\",\r\n                            duration: 2000,\r\n                            onClose: () => {\r\n                                this.$router.replace({ path: \"/login\" });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        this.$message.error(data.msg || '注册失败，请稍后重试');\r\n                    }\r\n                }).catch(() => {\r\n                    this.loading = false;\r\n                    this.$message.error('网络错误，请稍后重试');\r\n                });\r\n            }\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.register-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  // 背景装饰\r\n  .background-decoration {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    pointer-events: none;\r\n\r\n    .decoration-circle {\r\n      position: absolute;\r\n      border-radius: 50%;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      animation: float 6s ease-in-out infinite;\r\n\r\n      &.circle-1 {\r\n        width: 200px;\r\n        height: 200px;\r\n        top: 10%;\r\n        left: 10%;\r\n        animation-delay: 0s;\r\n      }\r\n\r\n      &.circle-2 {\r\n        width: 150px;\r\n        height: 150px;\r\n        top: 60%;\r\n        right: 15%;\r\n        animation-delay: 2s;\r\n      }\r\n\r\n      &.circle-3 {\r\n        width: 100px;\r\n        height: 100px;\r\n        bottom: 20%;\r\n        left: 20%;\r\n        animation-delay: 4s;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域\r\n  .main-content {\r\n    display: flex;\r\n    width: 100%;\r\n    max-width: 1400px;\r\n    margin: 0 auto;\r\n    padding: 40px;\r\n    gap: 60px;\r\n    align-items: center;\r\n\r\n    // 左侧信息区域\r\n    .info-section {\r\n      flex: 1;\r\n      color: white;\r\n\r\n      .brand-info {\r\n        .logo-container {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 30px;\r\n\r\n          i {\r\n            font-size: 48px;\r\n            color: #00c292;\r\n            margin-right: 20px;\r\n          }\r\n\r\n          h1 {\r\n            font-size: 36px;\r\n            font-weight: 700;\r\n            margin: 0;\r\n            background: linear-gradient(45deg, #00c292, #00e5ff);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            background-clip: text;\r\n          }\r\n        }\r\n\r\n        .brand-description {\r\n          font-size: 18px;\r\n          line-height: 1.6;\r\n          margin-bottom: 40px;\r\n          opacity: 0.9;\r\n        }\r\n\r\n        .features {\r\n          .feature-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n\r\n            i {\r\n              font-size: 20px;\r\n              color: #00c292;\r\n              margin-right: 15px;\r\n              width: 24px;\r\n            }\r\n\r\n            span {\r\n              font-size: 16px;\r\n              opacity: 0.9;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 右侧注册表单\r\n    .register-section {\r\n      flex: 0 0 550px;\r\n\r\n      .register-card {\r\n        background: rgba(255, 255, 255, 0.95);\r\n        backdrop-filter: blur(20px);\r\n        border-radius: 20px;\r\n        padding: 40px;\r\n        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid rgba(255, 255, 255, 0.2);\r\n        max-height: 90vh;\r\n        overflow-y: auto;\r\n\r\n        .card-header {\r\n          text-align: center;\r\n          margin-bottom: 30px;\r\n\r\n          h2 {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #2c3e50 !important;\r\n            margin: 0 0 10px 0;\r\n          }\r\n\r\n          p {\r\n            color: #666 !important;\r\n            font-size: 16px;\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        .register-form {\r\n          .form-row {\r\n            display: flex;\r\n            gap: 15px;\r\n            margin-bottom: 20px;\r\n\r\n            &:last-child {\r\n              margin-bottom: 0;\r\n            }\r\n          }\r\n\r\n          .form-item {\r\n            flex: 1;\r\n            margin-bottom: 0;\r\n\r\n            &.half-width {\r\n              flex: 0 0 calc(50% - 7.5px);\r\n            }\r\n\r\n            .input-wrapper {\r\n              position: relative;\r\n\r\n              .input-icon {\r\n                position: absolute;\r\n                left: 15px;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                color: #00c292;\r\n                font-size: 18px;\r\n                z-index: 2;\r\n              }\r\n\r\n              ::v-deep .el-input__inner {\r\n                height: 50px;\r\n                padding-left: 50px;\r\n                border: 2px solid #e8f4f8;\r\n                border-radius: 12px;\r\n                font-size: 16px;\r\n                transition: all 0.3s ease;\r\n                background: #f8fffe !important;\r\n                color: #333 !important;\r\n\r\n                &:focus {\r\n                  border-color: #00c292;\r\n                  box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);\r\n                  background: white !important;\r\n                  color: #333 !important;\r\n                }\r\n\r\n                &::placeholder {\r\n                  color: #999 !important;\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .user-info-section {\r\n            margin: 30px 0;\r\n            padding: 25px;\r\n            background: rgba(0, 194, 146, 0.05);\r\n            border-radius: 15px;\r\n            border: 1px solid rgba(0, 194, 146, 0.1);\r\n\r\n            .section-title {\r\n              display: flex;\r\n              align-items: center;\r\n              margin-bottom: 20px;\r\n              font-size: 18px;\r\n              font-weight: 600;\r\n              color: #2c3e50 !important;\r\n\r\n              i {\r\n                margin-right: 10px;\r\n                color: #00c292;\r\n                font-size: 20px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .form-actions {\r\n            display: flex;\r\n            gap: 15px;\r\n            margin-top: 30px;\r\n\r\n            .register-button {\r\n              flex: 1;\r\n              height: 50px;\r\n              font-size: 18px;\r\n              font-weight: 600;\r\n              border-radius: 12px;\r\n              background: linear-gradient(45deg, #00c292, #00a085);\r\n              border: none;\r\n              transition: all 0.3s ease;\r\n\r\n              &:hover {\r\n                transform: translateY(-2px);\r\n                box-shadow: 0 8px 25px rgba(0, 194, 146, 0.3);\r\n              }\r\n\r\n              &:active {\r\n                transform: translateY(0);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n              }\r\n            }\r\n\r\n            .cancel-button {\r\n              flex: 0 0 120px;\r\n              height: 50px;\r\n              font-size: 16px;\r\n              font-weight: 600;\r\n              border-radius: 12px;\r\n              background: white;\r\n              border: 2px solid #e8f4f8;\r\n              color: #666;\r\n              transition: all 0.3s ease;\r\n\r\n              &:hover {\r\n                border-color: #00c292;\r\n                color: #00c292;\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .form-footer {\r\n            text-align: center;\r\n            margin-top: 25px;\r\n\r\n            .login-link {\r\n              color: #00c292;\r\n              cursor: pointer;\r\n              font-size: 16px;\r\n              transition: all 0.3s ease;\r\n              display: inline-flex;\r\n              align-items: center;\r\n\r\n              &:hover {\r\n                color: #00a085;\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n                font-size: 18px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1200px) {\r\n  .register-container .main-content {\r\n    .register-section {\r\n      flex: 0 0 500px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 1024px) {\r\n  .register-container .main-content {\r\n    flex-direction: column;\r\n    gap: 40px;\r\n    padding: 20px;\r\n\r\n    .info-section {\r\n      text-align: center;\r\n\r\n      .brand-info .logo-container {\r\n        justify-content: center;\r\n\r\n        h1 {\r\n          font-size: 28px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .register-section {\r\n      flex: none;\r\n      width: 100%;\r\n      max-width: 550px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .register-container .main-content {\r\n    padding: 15px;\r\n\r\n    .register-section .register-card {\r\n      padding: 30px 20px;\r\n\r\n      .card-header h2 {\r\n        font-size: 24px;\r\n      }\r\n\r\n      .register-form {\r\n        .form-row {\r\n          flex-direction: column;\r\n          gap: 0;\r\n\r\n          .form-item {\r\n            margin-bottom: 20px;\r\n\r\n            &.half-width {\r\n              flex: 1;\r\n            }\r\n          }\r\n        }\r\n\r\n        .form-actions {\r\n          flex-direction: column;\r\n\r\n          .cancel-button {\r\n            flex: 1;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .info-section .brand-info .logo-container h1 {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n\r\n// 滚动条样式\r\n.register-card::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.register-card::-webkit-scrollbar-track {\r\n  background: rgba(0, 0, 0, 0.1);\r\n  border-radius: 3px;\r\n}\r\n\r\n.register-card::-webkit-scrollbar-thumb {\r\n  background: rgba(0, 194, 146, 0.3);\r\n  border-radius: 3px;\r\n\r\n  &:hover {\r\n    background: rgba(0, 194, 146, 0.5);\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;AA0LA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA;MACAC,QAAA;QACAC,QAAA;QACAC,QAAA;QACAC,kBAAA;QACAC,UAAA;QACAC,WAAA;QACAC,cAAA;QACAC,WAAA;MACA;MACAC,SAAA;MACAC,KAAA;MACAC,eAAA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,IAAAC,KAAA,QAAAC,QAAA,CAAAC,GAAA;IACA,KAAAN,SAAA,GAAAI,KAAA;EACA;EACAG,OAAA;IACA;IACAC,OAAA,WAAAA,QAAA;MACA,WAAAC,IAAA,GAAAC,OAAA;IACA;IAEA;IACAC,KAAA,WAAAA,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA;QAAAC,IAAA;MAAA;IACA;IAEA;IACAC,YAAA,WAAAA,aAAA;MACA;MACA,UAAAvB,QAAA,CAAAC,QAAA;QACA,KAAAuB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAAzB,QAAA,CAAAC,QAAA,CAAAyB,MAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAzB,QAAA,CAAAE,QAAA;QACA,KAAAsB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAAzB,QAAA,CAAAE,QAAA,CAAAwB,MAAA;QACA,KAAAF,QAAA,CAAAC,KAAA;QACA;MACA;MACA,UAAAzB,QAAA,CAAAG,kBAAA;QACA,KAAAqB,QAAA,CAAAC,KAAA;QACA;MACA;MACA,SAAAzB,QAAA,CAAAG,kBAAA,UAAAH,QAAA,CAAAE,QAAA;QACA,KAAAsB,QAAA,CAAAC,KAAA;QACA;MACA;;MAEA;MACA,SAAAjB,SAAA;QACA,UAAAR,QAAA,CAAAI,UAAA;UACA,KAAAoB,QAAA,CAAAC,KAAA;UACA;QACA;QACA,SAAAzB,QAAA,CAAAK,WAAA,UAAAsB,SAAA,CAAAC,QAAA,MAAA5B,QAAA,CAAAK,WAAA;UACA,KAAAmB,QAAA,CAAAC,KAAA;UACA;QACA;QACA,UAAAzB,QAAA,CAAAM,cAAA;UACA,KAAAkB,QAAA,CAAAC,KAAA;UACA;QACA;QACA,SAAAzB,QAAA,CAAAO,WAAA,UAAAoB,SAAA,CAAAE,OAAA,MAAA7B,QAAA,CAAAO,WAAA;UACA,KAAAiB,QAAA,CAAAC,KAAA;UACA;QACA;MACA;MAEA;IACA;IAEA;IACAK,QAAA,WAAAA,SAAA;MAAA,IAAAC,KAAA;MACA,UAAAR,YAAA;QACA;MACA;MAEA,KAAAxB,OAAA;MACA,KAAAiC,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAA1B,SAAA;QACA2B,MAAA;QACArC,IAAA,OAAAE;MACA,GAAAoC,IAAA,WAAAC,IAAA;QAAA,IAAAvC,IAAA,GAAAuC,IAAA,CAAAvC,IAAA;QACAiC,KAAA,CAAAhC,OAAA;QACA,IAAAD,IAAA,IAAAA,IAAA,CAAAwC,IAAA;UACAP,KAAA,CAAAP,QAAA;YACAe,OAAA;YACAC,IAAA;YACAC,QAAA;YACAC,OAAA,WAAAA,QAAA;cACAX,KAAA,CAAAX,OAAA,CAAAuB,OAAA;gBAAArB,IAAA;cAAA;YACA;UACA;QACA;UACAS,KAAA,CAAAP,QAAA,CAAAC,KAAA,CAAA3B,IAAA,CAAA8C,GAAA;QACA;MACA,GAAAC,KAAA;QACAd,KAAA,CAAAhC,OAAA;QACAgC,KAAA,CAAAP,QAAA,CAAAC,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}