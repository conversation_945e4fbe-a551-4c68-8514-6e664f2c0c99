{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue?vue&type=template&id=6c26a6b1&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\collection-analytics.vue", "mtime": 1750603257910}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}