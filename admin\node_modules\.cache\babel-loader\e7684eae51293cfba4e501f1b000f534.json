{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue", "mtime": 1750599624911}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "postList", "totalCount", "currentPage", "pageSize", "searchKeyword", "selectedType", "totalPosts", "todayPosts", "totalReplies", "showNewPostDialog", "newPost", "forumName", "forumContent", "forumTypes", "submitting", "showPostDialog", "currentPost", "replies", "replyContent", "replying", "postRules", "required", "message", "trigger", "min", "max", "mounted", "loadPosts", "loadStats", "methods", "_this", "params", "page", "limit", "forumStateTypes", "console", "log", "$http", "url", "method", "then", "_ref", "code", "list", "total", "error", "$message", "msg", "catch", "_this2", "_ref2", "today", "Date", "toISOString", "split", "insertTime", "_ref3", "_ref4", "handleSearch", "handleTypeChange", "handleSizeChange", "val", "handleCurrentChange", "submitPost", "_this3", "$refs", "newPostForm", "validate", "valid", "userId", "$storage", "get", "sessionTable", "postData", "parseInt", "yonghuId", "_ref5", "success", "resetNewPost", "resetFields", "viewPost", "post", "loadReplies", "id", "postId", "_this4", "superIds", "_ref6", "submitReply", "_this5", "trim", "warning", "replyData", "_ref7", "getTypeName", "type", "typeMap", "getTypeClass", "classMap", "formatTime", "time", "date", "now", "diff", "Math", "floor", "toLocaleDateString", "getContentPreview", "content", "text", "replace", "length", "substring"], "sources": ["src/views/forum.vue"], "sourcesContent": ["<template>\n  <div class=\"forum-container\">\n    <!-- 论坛头部 -->\n    <div class=\"forum-header\">\n      <div class=\"header-content\">\n        <h1 class=\"forum-title\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          体育馆论坛\n        </h1>\n        <p class=\"forum-subtitle\">分享运动心得，交流健身经验</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button \n          type=\"primary\" \n          icon=\"el-icon-edit\" \n          @click=\"showNewPostDialog = true\"\n          class=\"new-post-btn\">\n          发表新帖\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 论坛统计 -->\n    <div class=\"forum-stats\">\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalPosts }}</div>\n        <div class=\"stat-label\">总帖子数</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ todayPosts }}</div>\n        <div class=\"stat-label\">今日发帖</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalReplies }}</div>\n        <div class=\"stat-label\">总回复数</div>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"forum-filters\">\n      <el-input\n        v-model=\"searchKeyword\"\n        placeholder=\"搜索帖子标题或内容...\"\n        prefix-icon=\"el-icon-search\"\n        @input=\"handleSearch\"\n        class=\"search-input\">\n      </el-input>\n      <el-select v-model=\"selectedType\" placeholder=\"帖子类型\" @change=\"handleTypeChange\">\n        <el-option label=\"全部类型\" value=\"\"></el-option>\n        <el-option label=\"运动分享\" value=\"1\"></el-option>\n        <el-option label=\"健身心得\" value=\"2\"></el-option>\n        <el-option label=\"场地推荐\" value=\"3\"></el-option>\n        <el-option label=\"其他讨论\" value=\"4\"></el-option>\n      </el-select>\n    </div>\n\n    <!-- 帖子列表 -->\n    <div class=\"posts-container\">\n      <div \n        v-for=\"post in postList\" \n        :key=\"post.id\" \n        class=\"post-card\"\n        @click=\"viewPost(post)\">\n        <div class=\"post-header\">\n          <div class=\"post-type-tag\" :class=\"getTypeClass(post.forumTypes)\">\n            {{ getTypeName(post.forumTypes) }}\n          </div>\n          <div class=\"post-time\">{{ formatTime(post.insertTime) }}</div>\n        </div>\n        \n        <h3 class=\"post-title\">{{ post.forumName }}</h3>\n        \n        <div class=\"post-content\">\n          {{ getContentPreview(post.forumContent) }}\n        </div>\n        \n        <div class=\"post-footer\">\n          <div class=\"post-author\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"post.yonghuId\">{{ post.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n          </div>\n          <div class=\"post-stats\">\n            <span class=\"reply-count\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              {{ post.replyCount || 0 }} 回复\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 发帖对话框 -->\n    <el-dialog\n      title=\"发表新帖\"\n      :visible.sync=\"showNewPostDialog\"\n      width=\"800px\"\n      class=\"new-post-dialog\">\n      <el-form :model=\"newPost\" :rules=\"postRules\" ref=\"newPostForm\" label-width=\"80px\">\n        <el-form-item label=\"帖子类型\" prop=\"forumTypes\">\n          <el-select v-model=\"newPost.forumTypes\" placeholder=\"请选择帖子类型\">\n            <el-option label=\"运动分享\" value=\"1\"></el-option>\n            <el-option label=\"健身心得\" value=\"2\"></el-option>\n            <el-option label=\"场地推荐\" value=\"3\"></el-option>\n            <el-option label=\"其他讨论\" value=\"4\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子标题\" prop=\"forumName\">\n          <el-input v-model=\"newPost.forumName\" placeholder=\"请输入帖子标题\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子内容\" prop=\"forumContent\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"newPost.forumContent\"\n            :rows=\"8\"\n            placeholder=\"请输入帖子内容...\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showNewPostDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitPost\" :loading=\"submitting\">发布</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 帖子详情对话框 -->\n    <el-dialog\n      :title=\"currentPost.forumName\"\n      :visible.sync=\"showPostDialog\"\n      width=\"900px\"\n      class=\"post-detail-dialog\">\n      <div class=\"post-detail\">\n        <div class=\"post-detail-header\">\n          <div class=\"author-info\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"currentPost.yonghuId\">{{ currentPost.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n            <span class=\"post-time\">{{ formatTime(currentPost.insertTime) }}</span>\n          </div>\n          <div class=\"post-type-tag\" :class=\"getTypeClass(currentPost.forumTypes)\">\n            {{ getTypeName(currentPost.forumTypes) }}\n          </div>\n        </div>\n        \n        <div class=\"post-detail-content\" v-html=\"currentPost.forumContent\"></div>\n        \n        <!-- 回复列表 -->\n        <div class=\"replies-section\">\n          <h4>回复 ({{ replies.length }})</h4>\n          <div v-for=\"reply in replies\" :key=\"reply.id\" class=\"reply-item\">\n            <div class=\"reply-author\">\n              <i class=\"el-icon-user\"></i>\n              <span v-if=\"reply.yonghuId\">{{ reply.yonghuName || '用户' }}</span>\n              <span v-else>管理员</span>\n              <span class=\"reply-time\">{{ formatTime(reply.insertTime) }}</span>\n            </div>\n            <div class=\"reply-content\">{{ reply.forumContent }}</div>\n          </div>\n        </div>\n        \n        <!-- 回复表单 -->\n        <div class=\"reply-form\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"replyContent\"\n            :rows=\"3\"\n            placeholder=\"写下你的回复...\">\n          </el-input>\n          <div class=\"reply-actions\">\n            <el-button type=\"primary\" @click=\"submitReply\" :loading=\"replying\">回复</el-button>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Forum',\n  data() {\n    return {\n      // 帖子列表\n      postList: [],\n      totalCount: 0,\n      currentPage: 1,\n      pageSize: 10,\n      \n      // 搜索和筛选\n      searchKeyword: '',\n      selectedType: '',\n      \n      // 统计数据\n      totalPosts: 0,\n      todayPosts: 0,\n      totalReplies: 0,\n      \n      // 发帖\n      showNewPostDialog: false,\n      newPost: {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      },\n      submitting: false,\n      \n      // 帖子详情\n      showPostDialog: false,\n      currentPost: {},\n      replies: [],\n      replyContent: '',\n      replying: false,\n      \n      // 表单验证\n      postRules: {\n        forumTypes: [\n          { required: true, message: '请选择帖子类型', trigger: 'change' }\n        ],\n        forumName: [\n          { required: true, message: '请输入帖子标题', trigger: 'blur' },\n          { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }\n        ],\n        forumContent: [\n          { required: true, message: '请输入帖子内容', trigger: 'blur' },\n          { min: 10, message: '内容至少 10 个字符', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  \n  mounted() {\n    this.loadPosts()\n    this.loadStats()\n  },\n  \n  methods: {\n    // 加载帖子列表\n    loadPosts() {\n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize,\n        forumStateTypes: 1 // 只显示已审核的帖子\n      }\n\n      if (this.searchKeyword) {\n        params.forumName = this.searchKeyword\n      }\n\n      if (this.selectedType) {\n        params.forumTypes = this.selectedType\n      }\n\n      console.log('加载帖子参数:', params)\n\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        console.log('帖子列表响应:', data)\n        if (data && data.code === 0) {\n          this.postList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        } else {\n          console.error('获取帖子列表失败:', data)\n          this.$message.error(data.msg || '获取帖子列表失败')\n        }\n      }).catch((error) => {\n        console.error('加载帖子网络错误:', error)\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n    \n    // 加载统计数据\n    loadStats() {\n      // 获取总帖子数\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalPosts = data.data.total || 0\n        }\n      })\n      \n      // 获取今日发帖数\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: { page: 1, limit: 1, insertTime: today }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.todayPosts = data.data.total || 0\n        }\n      })\n      // 获取回复数\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 2 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalReplies = data.data.total || 0\n        }\n      })\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 类型筛选\n    handleTypeChange() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 分页处理\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.loadPosts()\n    },\n\n    handleCurrentChange(val) {\n      this.currentPage = val\n      this.loadPosts()\n    },\n\n    // 发帖\n    submitPost() {\n      this.$refs.newPostForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n\n          const userId = this.$storage.get('userId')\n          const sessionTable = this.$storage.get('sessionTable')\n\n          console.log('发帖数据准备:', {\n            userId,\n            sessionTable,\n            newPost: this.newPost\n          })\n\n          const postData = {\n            forumName: this.newPost.forumName,\n            forumContent: this.newPost.forumContent,\n            forumTypes: parseInt(this.newPost.forumTypes),\n            forumStateTypes: 1, // 直接审核通过\n            yonghuId: userId\n          }\n\n          console.log('提交的帖子数据:', postData)\n\n          this.$http({\n            url: 'forum/save',\n            method: 'post',\n            data: postData\n          }).then(({ data }) => {\n            console.log('发帖响应:', data)\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('发帖成功！')\n              this.showNewPostDialog = false\n              this.resetNewPost()\n              this.loadPosts()\n              this.loadStats()\n            } else {\n              this.$message.error(data.msg || '发帖失败，请稍后重试')\n              console.error('发帖失败详情:', data)\n            }\n          }).catch((error) => {\n            console.error('发帖请求错误:', error)\n            this.submitting = false\n            this.$message.error('网络错误，请稍后重试')\n          })\n        }\n      })\n    },\n\n    // 重置发帖表单\n    resetNewPost() {\n      this.newPost = {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      }\n      this.$refs.newPostForm && this.$refs.newPostForm.resetFields()\n    },\n\n    // 查看帖子详情\n    viewPost(post) {\n      this.currentPost = post\n      this.showPostDialog = true\n      this.loadReplies(post.id)\n    },\n\n    // 加载回复\n    loadReplies(postId) {\n      this.$http({\n        url: 'forum/page',\n        method: 'get',\n        params: {\n          superIds: postId,\n          forumStateTypes: 2\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.replies = data.data.list || []\n        }\n      })\n    },\n\n    // 提交回复\n    submitReply() {\n      if (!this.replyContent.trim()) {\n        this.$message.warning('请输入回复内容')\n        return\n      }\n\n      this.replying = true\n\n      const userId = this.$storage.get('userId')\n\n      const replyData = {\n        forumContent: this.replyContent,\n        superIds: this.currentPost.id,\n        forumStateTypes: 2, // 回复状态\n        yonghuId: userId\n      }\n\n      console.log('提交回复数据:', replyData)\n\n      this.$http({\n        url: 'forum/save',\n        method: 'post',\n        data: replyData\n      }).then(({ data }) => {\n        console.log('回复响应:', data)\n        this.replying = false\n        if (data && data.code === 0) {\n          this.$message.success('回复成功！')\n          this.replyContent = ''\n          this.loadReplies(this.currentPost.id)\n          this.loadStats()\n        } else {\n          this.$message.error(data.msg || '回复失败')\n          console.error('回复失败详情:', data)\n        }\n      }).catch((error) => {\n        console.error('回复请求错误:', error)\n        this.replying = false\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n\n    // 获取类型名称\n    getTypeName(type) {\n      const typeMap = {\n        '1': '运动分享',\n        '2': '健身心得',\n        '3': '场地推荐',\n        '4': '其他讨论'\n      }\n      return typeMap[type] || '未分类'\n    },\n\n    // 获取类型样式\n    getTypeClass(type) {\n      const classMap = {\n        '1': 'type-sport',\n        '2': 'type-fitness',\n        '3': 'type-venue',\n        '4': 'type-other'\n      }\n      return classMap[type] || 'type-default'\n    },\n\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      const now = new Date()\n      const diff = now - date\n\n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'\n      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'\n      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'\n\n      return date.toLocaleDateString()\n    },\n\n    // 获取内容预览\n    getContentPreview(content) {\n      if (!content) return ''\n      const text = content.replace(/<[^>]*>/g, '') // 移除HTML标签\n      return text.length > 150 ? text.substring(0, 150) + '...' : text\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.forum-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n// 论坛头部\n.forum-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 30px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n\n  .header-content {\n    .forum-title {\n      font-size: 28px;\n      margin: 0 0 8px 0;\n      font-weight: 600;\n\n      i {\n        margin-right: 10px;\n        color: #ffd700;\n      }\n    }\n\n    .forum-subtitle {\n      font-size: 16px;\n      margin: 0;\n      opacity: 0.9;\n    }\n  }\n\n  .new-post-btn {\n    background: rgba(255, 255, 255, 0.2);\n    border: 1px solid rgba(255, 255, 255, 0.3);\n    color: white;\n\n    &:hover {\n      background: rgba(255, 255, 255, 0.3);\n    }\n  }\n}\n\n// 论坛统计\n.forum-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n\n  .stat-item {\n    background: white;\n    padding: 25px;\n    border-radius: 12px;\n    text-align: center;\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n    transition: transform 0.3s ease;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    .stat-number {\n      font-size: 32px;\n      font-weight: bold;\n      color: #409eff;\n      margin-bottom: 8px;\n    }\n\n    .stat-label {\n      color: #666;\n      font-size: 14px;\n    }\n  }\n}\n\n// 搜索和筛选\n.forum-filters {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 25px;\n\n  .search-input {\n    flex: 1;\n    max-width: 400px;\n  }\n}\n\n// 帖子容器\n.posts-container {\n  display: grid;\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n// 帖子卡片\n.post-card {\n  background: white;\n  border-radius: 12px;\n  padding: 25px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n\n  .post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .post-time {\n      color: #999;\n      font-size: 14px;\n    }\n  }\n\n  .post-title {\n    font-size: 20px;\n    font-weight: 600;\n    color: #2c3e50;\n    margin: 0 0 15px 0;\n    line-height: 1.4;\n  }\n\n  .post-content {\n    color: #666;\n    line-height: 1.6;\n    margin-bottom: 20px;\n  }\n\n  .post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .post-author {\n      display: flex;\n      align-items: center;\n      color: #666;\n      font-size: 14px;\n\n      i {\n        margin-right: 5px;\n      }\n    }\n\n    .post-stats {\n      .reply-count {\n        color: #409eff;\n        font-size: 14px;\n\n        i {\n          margin-right: 5px;\n        }\n      }\n    }\n  }\n}\n\n// 类型标签\n.post-type-tag {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n\n  &.type-sport {\n    background: #e8f5e8;\n    color: #52c41a;\n  }\n\n  &.type-fitness {\n    background: #fff2e8;\n    color: #fa8c16;\n  }\n\n  &.type-venue {\n    background: #e6f7ff;\n    color: #1890ff;\n  }\n\n  &.type-other {\n    background: #f6f6f6;\n    color: #666;\n  }\n}\n\n// 分页\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 30px;\n}\n\n// 对话框样式\n.new-post-dialog, .post-detail-dialog {\n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n// 帖子详情\n.post-detail {\n  .post-detail-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding-bottom: 20px;\n    border-bottom: 1px solid #eee;\n    margin-bottom: 20px;\n\n    .author-info {\n      display: flex;\n      align-items: center;\n      color: #666;\n\n      i {\n        margin-right: 8px;\n      }\n\n      .post-time {\n        margin-left: 15px;\n        font-size: 14px;\n      }\n    }\n  }\n\n  .post-detail-content {\n    line-height: 1.8;\n    margin-bottom: 30px;\n    color: #333;\n  }\n\n  .replies-section {\n    border-top: 1px solid #eee;\n    padding-top: 20px;\n    margin-bottom: 20px;\n\n    h4 {\n      margin-bottom: 20px;\n      color: #333;\n    }\n\n    .reply-item {\n      background: #f8f9fa;\n      padding: 15px;\n      border-radius: 8px;\n      margin-bottom: 15px;\n\n      .reply-author {\n        display: flex;\n        align-items: center;\n        color: #666;\n        font-size: 14px;\n        margin-bottom: 10px;\n\n        i {\n          margin-right: 5px;\n        }\n\n        .reply-time {\n          margin-left: 10px;\n        }\n      }\n\n      .reply-content {\n        color: #333;\n        line-height: 1.6;\n      }\n    }\n  }\n\n  .reply-form {\n    .reply-actions {\n      margin-top: 15px;\n      text-align: right;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;AAkMA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,QAAA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;MAEA;MACAC,aAAA;MACAC,YAAA;MAEA;MACAC,UAAA;MACAC,UAAA;MACAC,YAAA;MAEA;MACAC,iBAAA;MACAC,OAAA;QACAC,SAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACAC,UAAA;MAEA;MACAC,cAAA;MACAC,WAAA;MACAC,OAAA;MACAC,YAAA;MACAC,QAAA;MAEA;MACAC,SAAA;QACAP,UAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,SAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,YAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EAEAG,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;IACA,KAAAC,SAAA;EACA;EAEAC,OAAA;IACA;IACAF,SAAA,WAAAA,UAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,MAAA;QACAC,IAAA,OAAA9B,WAAA;QACA+B,KAAA,OAAA9B,QAAA;QACA+B,eAAA;MACA;MAEA,SAAA9B,aAAA;QACA2B,MAAA,CAAApB,SAAA,QAAAP,aAAA;MACA;MAEA,SAAAC,YAAA;QACA0B,MAAA,CAAAlB,UAAA,QAAAR,YAAA;MACA;MAEA8B,OAAA,CAAAC,GAAA,YAAAL,MAAA;MAEA,KAAAM,KAAA;QACAC,GAAA;QACAC,MAAA;QACAR,MAAA,EAAAA;MACA,GAAAS,IAAA,WAAAC,IAAA;QAAA,IAAA1C,IAAA,GAAA0C,IAAA,CAAA1C,IAAA;QACAoC,OAAA,CAAAC,GAAA,YAAArC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2C,IAAA;UACAZ,KAAA,CAAA9B,QAAA,GAAAD,IAAA,CAAAA,IAAA,CAAA4C,IAAA;UACAb,KAAA,CAAA7B,UAAA,GAAAF,IAAA,CAAAA,IAAA,CAAA6C,KAAA;QACA;UACAT,OAAA,CAAAU,KAAA,cAAA9C,IAAA;UACA+B,KAAA,CAAAgB,QAAA,CAAAD,KAAA,CAAA9C,IAAA,CAAAgD,GAAA;QACA;MACA,GAAAC,KAAA,WAAAH,KAAA;QACAV,OAAA,CAAAU,KAAA,cAAAA,KAAA;QACAf,KAAA,CAAAgB,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACAjB,SAAA,WAAAA,UAAA;MAAA,IAAAqB,MAAA;MACA;MACA,KAAAZ,KAAA;QACAC,GAAA;QACAC,MAAA;QACAR,MAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,eAAA;QAAA;MACA,GAAAM,IAAA,WAAAU,KAAA;QAAA,IAAAnD,IAAA,GAAAmD,KAAA,CAAAnD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2C,IAAA;UACAO,MAAA,CAAA3C,UAAA,GAAAP,IAAA,CAAAA,IAAA,CAAA6C,KAAA;QACA;MACA;;MAEA;MACA,IAAAO,KAAA,OAAAC,IAAA,GAAAC,WAAA,GAAAC,KAAA;MACA,KAAAjB,KAAA;QACAC,GAAA;QACAC,MAAA;QACAR,MAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAsB,UAAA,EAAAJ;QAAA;MACA,GAAAX,IAAA,WAAAgB,KAAA;QAAA,IAAAzD,IAAA,GAAAyD,KAAA,CAAAzD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2C,IAAA;UACAO,MAAA,CAAA1C,UAAA,GAAAR,IAAA,CAAAA,IAAA,CAAA6C,KAAA;QACA;MACA;MACA;MACA,KAAAP,KAAA;QACAC,GAAA;QACAC,MAAA;QACAR,MAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAC,eAAA;QAAA;MACA,GAAAM,IAAA,WAAAiB,KAAA;QAAA,IAAA1D,IAAA,GAAA0D,KAAA,CAAA1D,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2C,IAAA;UACAO,MAAA,CAAAzC,YAAA,GAAAT,IAAA,CAAAA,IAAA,CAAA6C,KAAA;QACA;MACA;IACA;IAEA;IACAc,YAAA,WAAAA,aAAA;MACA,KAAAxD,WAAA;MACA,KAAAyB,SAAA;IACA;IAEA;IACAgC,gBAAA,WAAAA,iBAAA;MACA,KAAAzD,WAAA;MACA,KAAAyB,SAAA;IACA;IAEA;IACAiC,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAA1D,QAAA,GAAA0D,GAAA;MACA,KAAAlC,SAAA;IACA;IAEAmC,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAA3D,WAAA,GAAA2D,GAAA;MACA,KAAAlC,SAAA;IACA;IAEA;IACAoC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,MAAA,CAAAlD,UAAA;UAEA,IAAAuD,MAAA,GAAAL,MAAA,CAAAM,QAAA,CAAAC,GAAA;UACA,IAAAC,YAAA,GAAAR,MAAA,CAAAM,QAAA,CAAAC,GAAA;UAEApC,OAAA,CAAAC,GAAA;YACAiC,MAAA,EAAAA,MAAA;YACAG,YAAA,EAAAA,YAAA;YACA9D,OAAA,EAAAsD,MAAA,CAAAtD;UACA;UAEA,IAAA+D,QAAA;YACA9D,SAAA,EAAAqD,MAAA,CAAAtD,OAAA,CAAAC,SAAA;YACAC,YAAA,EAAAoD,MAAA,CAAAtD,OAAA,CAAAE,YAAA;YACAC,UAAA,EAAA6D,QAAA,CAAAV,MAAA,CAAAtD,OAAA,CAAAG,UAAA;YACAqB,eAAA;YAAA;YACAyC,QAAA,EAAAN;UACA;UAEAlC,OAAA,CAAAC,GAAA,aAAAqC,QAAA;UAEAT,MAAA,CAAA3B,KAAA;YACAC,GAAA;YACAC,MAAA;YACAxC,IAAA,EAAA0E;UACA,GAAAjC,IAAA,WAAAoC,KAAA;YAAA,IAAA7E,IAAA,GAAA6E,KAAA,CAAA7E,IAAA;YACAoC,OAAA,CAAAC,GAAA,UAAArC,IAAA;YACAiE,MAAA,CAAAlD,UAAA;YACA,IAAAf,IAAA,IAAAA,IAAA,CAAA2C,IAAA;cACAsB,MAAA,CAAAlB,QAAA,CAAA+B,OAAA;cACAb,MAAA,CAAAvD,iBAAA;cACAuD,MAAA,CAAAc,YAAA;cACAd,MAAA,CAAArC,SAAA;cACAqC,MAAA,CAAApC,SAAA;YACA;cACAoC,MAAA,CAAAlB,QAAA,CAAAD,KAAA,CAAA9C,IAAA,CAAAgD,GAAA;cACAZ,OAAA,CAAAU,KAAA,YAAA9C,IAAA;YACA;UACA,GAAAiD,KAAA,WAAAH,KAAA;YACAV,OAAA,CAAAU,KAAA,YAAAA,KAAA;YACAmB,MAAA,CAAAlD,UAAA;YACAkD,MAAA,CAAAlB,QAAA,CAAAD,KAAA;UACA;QACA;MACA;IACA;IAEA;IACAiC,YAAA,WAAAA,aAAA;MACA,KAAApE,OAAA;QACAC,SAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACA,KAAAoD,KAAA,CAAAC,WAAA,SAAAD,KAAA,CAAAC,WAAA,CAAAa,WAAA;IACA;IAEA;IACAC,QAAA,WAAAA,SAAAC,IAAA;MACA,KAAAjE,WAAA,GAAAiE,IAAA;MACA,KAAAlE,cAAA;MACA,KAAAmE,WAAA,CAAAD,IAAA,CAAAE,EAAA;IACA;IAEA;IACAD,WAAA,WAAAA,YAAAE,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAhD,KAAA;QACAC,GAAA;QACAC,MAAA;QACAR,MAAA;UACAuD,QAAA,EAAAF,MAAA;UACAlD,eAAA;QACA;MACA,GAAAM,IAAA,WAAA+C,KAAA;QAAA,IAAAxF,IAAA,GAAAwF,KAAA,CAAAxF,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAA2C,IAAA;UACA2C,MAAA,CAAApE,OAAA,GAAAlB,IAAA,CAAAA,IAAA,CAAA4C,IAAA;QACA;MACA;IACA;IAEA;IACA6C,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAAvE,YAAA,CAAAwE,IAAA;QACA,KAAA5C,QAAA,CAAA6C,OAAA;QACA;MACA;MAEA,KAAAxE,QAAA;MAEA,IAAAkD,MAAA,QAAAC,QAAA,CAAAC,GAAA;MAEA,IAAAqB,SAAA;QACAhF,YAAA,OAAAM,YAAA;QACAoE,QAAA,OAAAtE,WAAA,CAAAmE,EAAA;QACAjD,eAAA;QAAA;QACAyC,QAAA,EAAAN;MACA;MAEAlC,OAAA,CAAAC,GAAA,YAAAwD,SAAA;MAEA,KAAAvD,KAAA;QACAC,GAAA;QACAC,MAAA;QACAxC,IAAA,EAAA6F;MACA,GAAApD,IAAA,WAAAqD,KAAA;QAAA,IAAA9F,IAAA,GAAA8F,KAAA,CAAA9F,IAAA;QACAoC,OAAA,CAAAC,GAAA,UAAArC,IAAA;QACA0F,MAAA,CAAAtE,QAAA;QACA,IAAApB,IAAA,IAAAA,IAAA,CAAA2C,IAAA;UACA+C,MAAA,CAAA3C,QAAA,CAAA+B,OAAA;UACAY,MAAA,CAAAvE,YAAA;UACAuE,MAAA,CAAAP,WAAA,CAAAO,MAAA,CAAAzE,WAAA,CAAAmE,EAAA;UACAM,MAAA,CAAA7D,SAAA;QACA;UACA6D,MAAA,CAAA3C,QAAA,CAAAD,KAAA,CAAA9C,IAAA,CAAAgD,GAAA;UACAZ,OAAA,CAAAU,KAAA,YAAA9C,IAAA;QACA;MACA,GAAAiD,KAAA,WAAAH,KAAA;QACAV,OAAA,CAAAU,KAAA,YAAAA,KAAA;QACA4C,MAAA,CAAAtE,QAAA;QACAsE,MAAA,CAAA3C,QAAA,CAAAD,KAAA;MACA;IACA;IAEA;IACAiD,WAAA,WAAAA,YAAAC,IAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAAF,IAAA;MACA,IAAAG,QAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAH,IAAA;IACA;IAEA;IACAI,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAjD,IAAA,CAAAgD,IAAA;MACA,IAAAE,GAAA,OAAAlD,IAAA;MACA,IAAAmD,IAAA,GAAAD,GAAA,GAAAD,IAAA;MAEA,IAAAE,IAAA;MACA,IAAAA,IAAA,mBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA,IAAAA,IAAA,oBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA,IAAAA,IAAA,qBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MAEA,OAAAF,IAAA,CAAAK,kBAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAAC,OAAA;MACA,KAAAA,OAAA;MACA,IAAAC,IAAA,GAAAD,OAAA,CAAAE,OAAA;MACA,OAAAD,IAAA,CAAAE,MAAA,SAAAF,IAAA,CAAAG,SAAA,mBAAAH,IAAA;IACA;EACA;AACA", "ignoreList": []}]}