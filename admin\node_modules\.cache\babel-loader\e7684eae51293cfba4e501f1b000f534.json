{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue", "mtime": 1750601879409}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkuZmlsdGVyLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkudW5zaGlmdC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmRhdGUudG8taXNvLXN0cmluZy5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmRhdGUudG8tanNvbi5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLmpzb24uc3RyaW5naWZ5LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMub2JqZWN0LmtleXMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5vYmplY3QudG8tc3RyaW5nLmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucGFyc2UtaW50LmpzIjsKaW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMucmVnZXhwLmV4ZWMuanMiOwppbXBvcnQgImNvcmUtanMvbW9kdWxlcy9lcy5zdHJpbmcucmVwbGFjZS5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy5zdGFydHMtd2l0aC5qcyI7CmltcG9ydCAiY29yZS1qcy9tb2R1bGVzL2VzLnN0cmluZy50cmltLmpzIjsKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICdGb3J1bScsCiAgZGF0YTogZnVuY3Rpb24gZGF0YSgpIHsKICAgIHJldHVybiB7CiAgICAgIC8vIOW4luWtkOWIl+ihqAogICAgICBwb3N0TGlzdDogW10sCiAgICAgIHRvdGFsQ291bnQ6IDAsCiAgICAgIGN1cnJlbnRQYWdlOiAxLAogICAgICBwYWdlU2l6ZTogMTAsCiAgICAgIC8vIOaQnOe0ouWSjOetm+mAiQogICAgICBzZWFyY2hLZXl3b3JkOiAnJywKICAgICAgc2VsZWN0ZWRUeXBlOiAnJywKICAgICAgLy8g57uf6K6h5pWw5o2uCiAgICAgIHRvdGFsUG9zdHM6IDAsCiAgICAgIHRvZGF5UG9zdHM6IDAsCiAgICAgIHRvdGFsUmVwbGllczogMCwKICAgICAgLy8g5Y+R5biWCiAgICAgIHNob3dOZXdQb3N0RGlhbG9nOiBmYWxzZSwKICAgICAgbmV3UG9zdDogewogICAgICAgIGZvcnVtTmFtZTogJycsCiAgICAgICAgZm9ydW1Db250ZW50OiAnJywKICAgICAgICBmb3J1bVR5cGVzOiAnJwogICAgICB9LAogICAgICBzdWJtaXR0aW5nOiBmYWxzZSwKICAgICAgLy8g5biW5a2Q6K+m5oOFCiAgICAgIHNob3dQb3N0RGlhbG9nOiBmYWxzZSwKICAgICAgY3VycmVudFBvc3Q6IHt9LAogICAgICByZXBsaWVzOiBbXSwKICAgICAgcmVwbHlDb250ZW50OiAnJywKICAgICAgcmVwbHlpbmc6IGZhbHNlLAogICAgICAvLyDooajljZXpqozor4EKICAgICAgcG9zdFJ1bGVzOiB7CiAgICAgICAgZm9ydW1UeXBlczogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+mAieaLqeW4luWtkOexu+WeiycsCiAgICAgICAgICB0cmlnZ2VyOiAnY2hhbmdlJwogICAgICAgIH1dLAogICAgICAgIGZvcnVtTmFtZTogW3sKICAgICAgICAgIHJlcXVpcmVkOiB0cnVlLAogICAgICAgICAgbWVzc2FnZTogJ+ivt+i+k+WFpeW4luWtkOagh+mimCcsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9LCB7CiAgICAgICAgICBtaW46IDUsCiAgICAgICAgICBtYXg6IDEwMCwKICAgICAgICAgIG1lc3NhZ2U6ICfmoIfpopjplb/luqblnKggNSDliLAgMTAwIOS4quWtl+espicsCiAgICAgICAgICB0cmlnZ2VyOiAnYmx1cicKICAgICAgICB9XSwKICAgICAgICBmb3J1bUNvbnRlbnQ6IFt7CiAgICAgICAgICByZXF1aXJlZDogdHJ1ZSwKICAgICAgICAgIG1lc3NhZ2U6ICfor7fovpPlhaXluJblrZDlhoXlrrknLAogICAgICAgICAgdHJpZ2dlcjogJ2JsdXInCiAgICAgICAgfSwgewogICAgICAgICAgbWluOiAxMCwKICAgICAgICAgIG1lc3NhZ2U6ICflhoXlrrnoh7PlsJEgMTAg5Liq5a2X56ymJywKICAgICAgICAgIHRyaWdnZXI6ICdibHVyJwogICAgICAgIH1dCiAgICAgIH0KICAgIH07CiAgfSwKICBtb3VudGVkOiBmdW5jdGlvbiBtb3VudGVkKCkgewogICAgLy8g5qOA5p+l5piv5ZCm5pyJ5pys5Zyw5a2Y5YKo55qE5biW5a2Q5pWw5o2uCiAgICB0aGlzLmxvYWRMb2NhbFBvc3RzKCk7CiAgICB0aGlzLmxvYWRQb3N0cygpOwogICAgdGhpcy5sb2FkU3RhdHMoKTsKICB9LAogIG1ldGhvZHM6IHsKICAgIC8vIOWKoOi9veacrOWcsOWtmOWCqOeahOW4luWtkAogICAgbG9hZExvY2FsUG9zdHM6IGZ1bmN0aW9uIGxvYWRMb2NhbFBvc3RzKCkgewogICAgICB2YXIgbG9jYWxQb3N0cyA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCdmb3J1bV9wb3N0cycpOwogICAgICBpZiAobG9jYWxQb3N0cykgewogICAgICAgIHRyeSB7CiAgICAgICAgICB2YXIgcG9zdHMgPSBKU09OLnBhcnNlKGxvY2FsUG9zdHMpOwogICAgICAgICAgdGhpcy5wb3N0TGlzdCA9IHBvc3RzOwogICAgICAgICAgdGhpcy50b3RhbENvdW50ID0gcG9zdHMubGVuZ3RoOwogICAgICAgICAgdGhpcy50b3RhbFBvc3RzID0gcG9zdHMubGVuZ3RoOwoKICAgICAgICAgIC8vIOiuoeeul+S7iuaXpeWPkeW4luaVsAogICAgICAgICAgdmFyIHRvZGF5ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07CiAgICAgICAgICB0aGlzLnRvZGF5UG9zdHMgPSBwb3N0cy5maWx0ZXIoZnVuY3Rpb24gKHBvc3QpIHsKICAgICAgICAgICAgcmV0dXJuIHBvc3QuaW5zZXJ0VGltZSAmJiBwb3N0Lmluc2VydFRpbWUuc3RhcnRzV2l0aCh0b2RheSk7CiAgICAgICAgICB9KS5sZW5ndGg7CgogICAgICAgICAgLy8g6K6h566X5Zue5aSN5pWw77yI6L+Z6YeM566A5YyW5aSE55CG77yJCiAgICAgICAgICB0aGlzLnRvdGFsUmVwbGllcyA9IHBvc3RzLmZpbHRlcihmdW5jdGlvbiAocG9zdCkgewogICAgICAgICAgICByZXR1cm4gcG9zdC5zdXBlcklkczsKICAgICAgICAgIH0pLmxlbmd0aDsKICAgICAgICAgIGNvbnNvbGUubG9nKCfku47mnKzlnLDlrZjlgqjliqDovb3kuoYnLCBwb3N0cy5sZW5ndGgsICfkuKrluJblrZAnKTsKICAgICAgICAgIHJldHVybjsKICAgICAgICB9IGNhdGNoIChlKSB7CiAgICAgICAgICBjb25zb2xlLmVycm9yKCfop6PmnpDmnKzlnLDluJblrZDmlbDmja7lpLHotKU6JywgZSk7CiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlpoLmnpzmsqHmnInmnKzlnLDmlbDmja7vvIzliJvlu7rkuIDkupvnpLrkvovmlbDmja4KICAgICAgdGhpcy5jcmVhdGVTYW1wbGVQb3N0cygpOwogICAgfSwKICAgIC8vIOWIm+W7uuekuuS+i+W4luWtkOaVsOaNrgogICAgY3JlYXRlU2FtcGxlUG9zdHM6IGZ1bmN0aW9uIGNyZWF0ZVNhbXBsZVBvc3RzKCkgewogICAgICB2YXIgc2FtcGxlUG9zdHMgPSBbewogICAgICAgIGlkOiAxLAogICAgICAgIGZvcnVtTmFtZTogJ+asoui/juadpeWIsOS9k+iCsummhuiuuuWdm++8gScsCiAgICAgICAgZm9ydW1Db250ZW50OiAn6L+Z6YeM5piv5aSn5a625Lqk5rWB6L+Q5Yqo5b+D5b6X44CB5YiG5Lqr5YGl6Lqr57uP6aqM55qE5Zyw5pa544CC5qyi6L+O5aSn5a6256ev5p6B5Y+C5LiO6K6o6K6677yBJywKICAgICAgICBmb3J1bVR5cGVzOiA0LAogICAgICAgIGZvcnVtU3RhdGVUeXBlczogMSwKICAgICAgICB5b25naHVJZDogMSwKICAgICAgICB5b25naHVOYW1lOiAn566h55CG5ZGYJywKICAgICAgICBpbnNlcnRUaW1lOiBuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCksCiAgICAgICAgY3JlYXRlVGltZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpCiAgICAgIH0sIHsKICAgICAgICBpZDogMiwKICAgICAgICBmb3J1bU5hbWU6ICfnr67nkIPlnLrlnLDmjqjojZAnLAogICAgICAgIGZvcnVtQ29udGVudDogJ+aOqOiNkOWHoOS4quS4jemUmeeahOevrueQg+WcuuWcsO+8jOiuvuaWvem9kOWFqO+8jOeOr+Wig+S8mOe+ju+8jOmAguWQiOWQhOenjeawtOW5s+eahOeQg+WPi+OAgicsCiAgICAgICAgZm9ydW1UeXBlczogMywKICAgICAgICBmb3J1bVN0YXRlVHlwZXM6IDEsCiAgICAgICAgeW9uZ2h1SWQ6IDIsCiAgICAgICAgeW9uZ2h1TmFtZTogJ+evrueQg+eIseWlveiAhScsCiAgICAgICAgaW5zZXJ0VGltZTogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDM2MDAwMDApLnRvSVNPU3RyaW5nKCksCiAgICAgICAgY3JlYXRlVGltZTogbmV3IERhdGUoRGF0ZS5ub3coKSAtIDM2MDAwMDApLnRvSVNPU3RyaW5nKCkKICAgICAgfSwgewogICAgICAgIGlkOiAzLAogICAgICAgIGZvcnVtTmFtZTogJ+WBpei6q+aIv+iuree7g+W/g+W+lycsCiAgICAgICAgZm9ydW1Db250ZW50OiAn5YiG5Lqr5LiA5Lqb5YGl6Lqr5oi/6K6t57uD55qE5b+D5b6X5L2T5Lya77yM5YyF5ous5Zmo5qKw5L2/55So5oqA5ben5ZKM6K6t57uD6K6h5YiS5a6J5o6S44CCJywKICAgICAgICBmb3J1bVR5cGVzOiAyLAogICAgICAgIGZvcnVtU3RhdGVUeXBlczogMSwKICAgICAgICB5b25naHVJZDogMywKICAgICAgICB5b25naHVOYW1lOiAn5YGl6Lqr6L6+5Lq6JywKICAgICAgICBpbnNlcnRUaW1lOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gNzIwMDAwMCkudG9JU09TdHJpbmcoKSwKICAgICAgICBjcmVhdGVUaW1lOiBuZXcgRGF0ZShEYXRlLm5vdygpIC0gNzIwMDAwMCkudG9JU09TdHJpbmcoKQogICAgICB9XTsKICAgICAgdGhpcy5wb3N0TGlzdCA9IHNhbXBsZVBvc3RzOwogICAgICB0aGlzLnRvdGFsQ291bnQgPSBzYW1wbGVQb3N0cy5sZW5ndGg7CiAgICAgIHRoaXMudG90YWxQb3N0cyA9IHNhbXBsZVBvc3RzLmxlbmd0aDsKICAgICAgdGhpcy50b2RheVBvc3RzID0gMTsKICAgICAgdGhpcy50b3RhbFJlcGxpZXMgPSAwOwoKICAgICAgLy8g5L+d5a2Y5Yiw5pys5Zyw5a2Y5YKoCiAgICAgIHRoaXMuc2F2ZVBvc3RzVG9Mb2NhbCgpOwogICAgICBjb25zb2xlLmxvZygn5Yib5bu65LqG56S65L6L5biW5a2Q5pWw5o2uJyk7CiAgICB9LAogICAgLy8g5L+d5a2Y5biW5a2Q5Yiw5pys5Zyw5a2Y5YKoCiAgICBzYXZlUG9zdHNUb0xvY2FsOiBmdW5jdGlvbiBzYXZlUG9zdHNUb0xvY2FsKCkgewogICAgICB0cnkgewogICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKCdmb3J1bV9wb3N0cycsIEpTT04uc3RyaW5naWZ5KHRoaXMucG9zdExpc3QpKTsKICAgICAgfSBjYXRjaCAoZSkgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOW4luWtkOWIsOacrOWcsOWtmOWCqOWksei0pTonLCBlKTsKICAgICAgfQogICAgfSwKICAgIC8vIOWKoOi9veW4luWtkOWIl+ihqAogICAgbG9hZFBvc3RzOiBmdW5jdGlvbiBsb2FkUG9zdHMoKSB7CiAgICAgIHZhciBfdGhpcyA9IHRoaXM7CiAgICAgIHZhciBwYXJhbXMgPSB7CiAgICAgICAgcGFnZTogdGhpcy5jdXJyZW50UGFnZSwKICAgICAgICBsaW1pdDogdGhpcy5wYWdlU2l6ZSwKICAgICAgICBmb3J1bVN0YXRlVHlwZXM6IDEgLy8g5Y+q5pi+56S65bey5a6h5qC455qE5biW5a2QCiAgICAgIH07CiAgICAgIGlmICh0aGlzLnNlYXJjaEtleXdvcmQpIHsKICAgICAgICBwYXJhbXMuZm9ydW1OYW1lID0gdGhpcy5zZWFyY2hLZXl3b3JkOwogICAgICB9CiAgICAgIGlmICh0aGlzLnNlbGVjdGVkVHlwZSkgewogICAgICAgIHBhcmFtcy5mb3J1bVR5cGVzID0gdGhpcy5zZWxlY3RlZFR5cGU7CiAgICAgIH0KICAgICAgY29uc29sZS5sb2coJ+WKoOi9veW4luWtkOWPguaVsDonLCBwYXJhbXMpOwogICAgICB0aGlzLiRodHRwKHsKICAgICAgICB1cmw6ICdmb3J1bS9saXN0JywKICAgICAgICBtZXRob2Q6ICdnZXQnLAogICAgICAgIHBhcmFtczogcGFyYW1zCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKF9yZWYpIHsKICAgICAgICB2YXIgZGF0YSA9IF9yZWYuZGF0YTsKICAgICAgICBjb25zb2xlLmxvZygn5biW5a2Q5YiX6KGo5ZON5bqUOicsIGRhdGEpOwogICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgewogICAgICAgICAgX3RoaXMucG9zdExpc3QgPSBkYXRhLmRhdGEubGlzdCB8fCBbXTsKICAgICAgICAgIF90aGlzLnRvdGFsQ291bnQgPSBkYXRhLmRhdGEudG90YWwgfHwgMDsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgY29uc29sZS5lcnJvcign6I635Y+W5biW5a2Q5YiX6KGo5aSx6LSlOicsIGRhdGEpOwogICAgICAgICAgX3RoaXMuJG1lc3NhZ2UuZXJyb3IoZGF0YS5tc2cgfHwgJ+iOt+WPluW4luWtkOWIl+ihqOWksei0pScpOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKGVycm9yKSB7CiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295biW5a2Q572R57uc6ZSZ6K+vOicsIGVycm9yKTsKICAgICAgICAvLyDlkI7nq6/kuI3lj6/nlKjml7bvvIzkuI3mmL7npLrplJnor6/mtojmga/vvIzkvb/nlKjmnKzlnLDmlbDmja4KICAgICAgICBjb25zb2xlLmxvZygn5L2/55So5pys5Zyw5a2Y5YKo55qE5biW5a2Q5pWw5o2uJyk7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOWKoOi9vee7n+iuoeaVsOaNrgogICAgbG9hZFN0YXRzOiBmdW5jdGlvbiBsb2FkU3RhdHMoKSB7CiAgICAgIHZhciBfdGhpczIgPSB0aGlzOwogICAgICAvLyDojrflj5bmgLvluJblrZDmlbAKICAgICAgdGhpcy4kaHR0cCh7CiAgICAgICAgdXJsOiAnZm9ydW0vbGlzdCcsCiAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgIHBhZ2U6IDEsCiAgICAgICAgICBsaW1pdDogMSwKICAgICAgICAgIGZvcnVtU3RhdGVUeXBlczogMQogICAgICAgIH0KICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZjIpIHsKICAgICAgICB2YXIgZGF0YSA9IF9yZWYyLmRhdGE7CiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgICBfdGhpczIudG90YWxQb3N0cyA9IGRhdGEuZGF0YS50b3RhbCB8fCAwOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bmgLvluJblrZDmlbDlpLHotKUnKTsKICAgICAgfSk7CgogICAgICAvLyDojrflj5bku4rml6Xlj5HluJbmlbAKICAgICAgdmFyIHRvZGF5ID0gbmV3IERhdGUoKS50b0lTT1N0cmluZygpLnNwbGl0KCdUJylbMF07CiAgICAgIHRoaXMuJGh0dHAoewogICAgICAgIHVybDogJ2ZvcnVtL2xpc3QnLAogICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICBwYWdlOiAxLAogICAgICAgICAgbGltaXQ6IDEsCiAgICAgICAgICBpbnNlcnRUaW1lOiB0b2RheQogICAgICAgIH0KICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZjMpIHsKICAgICAgICB2YXIgZGF0YSA9IF9yZWYzLmRhdGE7CiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgICBfdGhpczIudG9kYXlQb3N0cyA9IGRhdGEuZGF0YS50b3RhbCB8fCAwOwogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIGNvbnNvbGUubG9nKCfojrflj5bku4rml6Xlj5HluJbmlbDlpLHotKUnKTsKICAgICAgfSk7CgogICAgICAvLyDojrflj5blm57lpI3mlbAKICAgICAgdGhpcy4kaHR0cCh7CiAgICAgICAgdXJsOiAnZm9ydW0vbGlzdCcsCiAgICAgICAgbWV0aG9kOiAnZ2V0JywKICAgICAgICBwYXJhbXM6IHsKICAgICAgICAgIHBhZ2U6IDEsCiAgICAgICAgICBsaW1pdDogMSwKICAgICAgICAgIGZvcnVtU3RhdGVUeXBlczogMgogICAgICAgIH0KICAgICAgfSkudGhlbihmdW5jdGlvbiAoX3JlZjQpIHsKICAgICAgICB2YXIgZGF0YSA9IF9yZWY0LmRhdGE7CiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgICBfdGhpczIudG90YWxSZXBsaWVzID0gZGF0YS5kYXRhLnRvdGFsIHx8IDA7CiAgICAgICAgfQogICAgICB9KS5jYXRjaChmdW5jdGlvbiAoKSB7CiAgICAgICAgY29uc29sZS5sb2coJ+iOt+WPluWbnuWkjeaVsOWksei0pScpOwogICAgICB9KTsKICAgIH0sCiAgICAvLyDmkJzntKLlpITnkIYKICAgIGhhbmRsZVNlYXJjaDogZnVuY3Rpb24gaGFuZGxlU2VhcmNoKCkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gMTsKICAgICAgdGhpcy5sb2FkUG9zdHMoKTsKICAgIH0sCiAgICAvLyDnsbvlnovnrZvpgIkKICAgIGhhbmRsZVR5cGVDaGFuZ2U6IGZ1bmN0aW9uIGhhbmRsZVR5cGVDaGFuZ2UoKSB7CiAgICAgIHRoaXMuY3VycmVudFBhZ2UgPSAxOwogICAgICB0aGlzLmxvYWRQb3N0cygpOwogICAgfSwKICAgIC8vIOWIhumhteWkhOeQhgogICAgaGFuZGxlU2l6ZUNoYW5nZTogZnVuY3Rpb24gaGFuZGxlU2l6ZUNoYW5nZSh2YWwpIHsKICAgICAgdGhpcy5wYWdlU2l6ZSA9IHZhbDsKICAgICAgdGhpcy5sb2FkUG9zdHMoKTsKICAgIH0sCiAgICBoYW5kbGVDdXJyZW50Q2hhbmdlOiBmdW5jdGlvbiBoYW5kbGVDdXJyZW50Q2hhbmdlKHZhbCkgewogICAgICB0aGlzLmN1cnJlbnRQYWdlID0gdmFsOwogICAgICB0aGlzLmxvYWRQb3N0cygpOwogICAgfSwKICAgIC8vIOWPkeW4lgogICAgc3VibWl0UG9zdDogZnVuY3Rpb24gc3VibWl0UG9zdCgpIHsKICAgICAgdmFyIF90aGlzMyA9IHRoaXM7CiAgICAgIHRoaXMuJHJlZnMubmV3UG9zdEZvcm0udmFsaWRhdGUoZnVuY3Rpb24gKHZhbGlkKSB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICBfdGhpczMuc3VibWl0dGluZyA9IHRydWU7CiAgICAgICAgICB2YXIgdXNlcklkID0gX3RoaXMzLiRzdG9yYWdlLmdldCgndXNlcklkJyk7CiAgICAgICAgICB2YXIgc2Vzc2lvblRhYmxlID0gX3RoaXMzLiRzdG9yYWdlLmdldCgnc2Vzc2lvblRhYmxlJyk7CiAgICAgICAgICBjb25zb2xlLmxvZygn5Y+R5biW5pWw5o2u5YeG5aSHOicsIHsKICAgICAgICAgICAgdXNlcklkOiB1c2VySWQsCiAgICAgICAgICAgIHNlc3Npb25UYWJsZTogc2Vzc2lvblRhYmxlLAogICAgICAgICAgICBuZXdQb3N0OiBfdGhpczMubmV3UG9zdAogICAgICAgICAgfSk7CiAgICAgICAgICB2YXIgcG9zdERhdGEgPSB7CiAgICAgICAgICAgIGZvcnVtTmFtZTogX3RoaXMzLm5ld1Bvc3QuZm9ydW1OYW1lLAogICAgICAgICAgICBmb3J1bUNvbnRlbnQ6IF90aGlzMy5uZXdQb3N0LmZvcnVtQ29udGVudCwKICAgICAgICAgICAgZm9ydW1UeXBlczogcGFyc2VJbnQoX3RoaXMzLm5ld1Bvc3QuZm9ydW1UeXBlcyksCiAgICAgICAgICAgIGZvcnVtU3RhdGVUeXBlczogMSwKICAgICAgICAgICAgLy8g55u05o6l5a6h5qC46YCa6L+HCiAgICAgICAgICAgIHlvbmdodUlkOiB1c2VySWQKICAgICAgICAgIH07CiAgICAgICAgICBjb25zb2xlLmxvZygn5o+Q5Lqk55qE5biW5a2Q5pWw5o2uOicsIHBvc3REYXRhKTsKICAgICAgICAgIF90aGlzMy4kaHR0cCh7CiAgICAgICAgICAgIHVybDogJ2ZvcnVtL3NhdmUnLAogICAgICAgICAgICBtZXRob2Q6ICdwb3N0JywKICAgICAgICAgICAgZGF0YTogcG9zdERhdGEKICAgICAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKF9yZWY1KSB7CiAgICAgICAgICAgIHZhciBkYXRhID0gX3JlZjUuZGF0YTsKICAgICAgICAgICAgY29uc29sZS5sb2coJ+WPkeW4luWTjeW6lDonLCBkYXRhKTsKICAgICAgICAgICAgX3RoaXMzLnN1Ym1pdHRpbmcgPSBmYWxzZTsKICAgICAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgICAgICAgX3RoaXMzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WPkeW4luaIkOWKn++8gScpOwogICAgICAgICAgICAgIF90aGlzMy5zaG93TmV3UG9zdERpYWxvZyA9IGZhbHNlOwogICAgICAgICAgICAgIF90aGlzMy5yZXNldE5ld1Bvc3QoKTsKICAgICAgICAgICAgICBfdGhpczMubG9hZFBvc3RzKCk7CiAgICAgICAgICAgICAgX3RoaXMzLmxvYWRTdGF0cygpOwogICAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICAgIF90aGlzMy4kbWVzc2FnZS5lcnJvcihkYXRhLm1zZyB8fCAn5Y+R5biW5aSx6LSl77yM6K+356iN5ZCO6YeN6K+VJyk7CiAgICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Y+R5biW5aSx6LSl6K+m5oOFOicsIGRhdGEpOwogICAgICAgICAgICB9CiAgICAgICAgICB9KS5jYXRjaChmdW5jdGlvbiAoZXJyb3IpIHsKICAgICAgICAgICAgY29uc29sZS5lcnJvcign5Y+R5biW6K+35rGC6ZSZ6K+vOicsIGVycm9yKTsKICAgICAgICAgICAgLy8g5ZCO56uv5LiN5Y+v55So5pe277yM5L2/55So5pys5Zyw5a2Y5YKoCiAgICAgICAgICAgIF90aGlzMy5zYXZlUG9zdExvY2FsbHkocG9zdERhdGEpOwogICAgICAgICAgfSk7CiAgICAgICAgfQogICAgICB9KTsKICAgIH0sCiAgICAvLyDmnKzlnLDkv53lrZjluJblrZAKICAgIHNhdmVQb3N0TG9jYWxseTogZnVuY3Rpb24gc2F2ZVBvc3RMb2NhbGx5KHBvc3REYXRhKSB7CiAgICAgIHRyeSB7CiAgICAgICAgLy8g5Yib5bu65paw5biW5a2Q5a+56LGhCiAgICAgICAgdmFyIG5ld1Bvc3QgPSB7CiAgICAgICAgICBpZDogRGF0ZS5ub3coKSwKICAgICAgICAgIC8vIOS9v+eUqOaXtumXtOaIs+S9nOS4uklECiAgICAgICAgICBmb3J1bU5hbWU6IHBvc3REYXRhLmZvcnVtTmFtZSwKICAgICAgICAgIGZvcnVtQ29udGVudDogcG9zdERhdGEuZm9ydW1Db250ZW50LAogICAgICAgICAgZm9ydW1UeXBlczogcG9zdERhdGEuZm9ydW1UeXBlcywKICAgICAgICAgIGZvcnVtU3RhdGVUeXBlczogMSwKICAgICAgICAgIHlvbmdodUlkOiBwb3N0RGF0YS55b25naHVJZCwKICAgICAgICAgIHlvbmdodU5hbWU6IHRoaXMuJHN0b3JhZ2UuZ2V0KCd1c2VybmFtZScpIHx8ICfnlKjmiLcnLAogICAgICAgICAgaW5zZXJ0VGltZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpLAogICAgICAgICAgY3JlYXRlVGltZTogbmV3IERhdGUoKS50b0lTT1N0cmluZygpCiAgICAgICAgfTsKCiAgICAgICAgLy8g5re75Yqg5Yiw5biW5a2Q5YiX6KGoCiAgICAgICAgdGhpcy5wb3N0TGlzdC51bnNoaWZ0KG5ld1Bvc3QpOwogICAgICAgIHRoaXMudG90YWxDb3VudCA9IHRoaXMucG9zdExpc3QubGVuZ3RoOwogICAgICAgIHRoaXMudG90YWxQb3N0cyA9IHRoaXMucG9zdExpc3QubGVuZ3RoOwogICAgICAgIHRoaXMudG9kYXlQb3N0cyArPSAxOwoKICAgICAgICAvLyDkv53lrZjliLDmnKzlnLDlrZjlgqgKICAgICAgICB0aGlzLnNhdmVQb3N0c1RvTG9jYWwoKTsKICAgICAgICB0aGlzLnN1Ym1pdHRpbmcgPSBmYWxzZTsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WPkeW4luaIkOWKn++8ge+8iOW3suS/neWtmOWIsOacrOWcsO+8iScpOwogICAgICAgIHRoaXMuc2hvd05ld1Bvc3REaWFsb2cgPSBmYWxzZTsKICAgICAgICB0aGlzLnJlc2V0TmV3UG9zdCgpOwogICAgICAgIGNvbnNvbGUubG9nKCfluJblrZDlt7Lkv53lrZjliLDmnKzlnLDlrZjlgqgnKTsKICAgICAgfSBjYXRjaCAoZXJyb3IpIHsKICAgICAgICBjb25zb2xlLmVycm9yKCfmnKzlnLDkv53lrZjluJblrZDlpLHotKU6JywgZXJyb3IpOwogICAgICAgIHRoaXMuc3VibWl0dGluZyA9IGZhbHNlOwogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WPkeW4luWksei0pScpOwogICAgICB9CiAgICB9LAogICAgLy8g6YeN572u5Y+R5biW6KGo5Y2VCiAgICByZXNldE5ld1Bvc3Q6IGZ1bmN0aW9uIHJlc2V0TmV3UG9zdCgpIHsKICAgICAgdGhpcy5uZXdQb3N0ID0gewogICAgICAgIGZvcnVtTmFtZTogJycsCiAgICAgICAgZm9ydW1Db250ZW50OiAnJywKICAgICAgICBmb3J1bVR5cGVzOiAnJwogICAgICB9OwogICAgICB0aGlzLiRyZWZzLm5ld1Bvc3RGb3JtICYmIHRoaXMuJHJlZnMubmV3UG9zdEZvcm0ucmVzZXRGaWVsZHMoKTsKICAgIH0sCiAgICAvLyDmn6XnnIvluJblrZDor6bmg4UKICAgIHZpZXdQb3N0OiBmdW5jdGlvbiB2aWV3UG9zdChwb3N0KSB7CiAgICAgIHRoaXMuY3VycmVudFBvc3QgPSBwb3N0OwogICAgICB0aGlzLnNob3dQb3N0RGlhbG9nID0gdHJ1ZTsKICAgICAgdGhpcy5sb2FkUmVwbGllcyhwb3N0LmlkKTsKICAgIH0sCiAgICAvLyDliqDovb3lm57lpI0KICAgIGxvYWRSZXBsaWVzOiBmdW5jdGlvbiBsb2FkUmVwbGllcyhwb3N0SWQpIHsKICAgICAgdmFyIF90aGlzNCA9IHRoaXM7CiAgICAgIHRoaXMuJGh0dHAoewogICAgICAgIHVybDogJ2ZvcnVtL2xpc3QnLAogICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICBzdXBlcklkczogcG9zdElkLAogICAgICAgICAgZm9ydW1TdGF0ZVR5cGVzOiAyCiAgICAgICAgfQogICAgICB9KS50aGVuKGZ1bmN0aW9uIChfcmVmNikgewogICAgICAgIHZhciBkYXRhID0gX3JlZjYuZGF0YTsKICAgICAgICBjb25zb2xlLmxvZygn5Zue5aSN5YiX6KGo5ZON5bqUOicsIGRhdGEpOwogICAgICAgIGlmIChkYXRhICYmIGRhdGEuY29kZSA9PT0gMCkgewogICAgICAgICAgX3RoaXM0LnJlcGxpZXMgPSBkYXRhLmRhdGEubGlzdCB8fCBbXTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWbnuWkjeWksei0pTonLCBlcnJvcik7CiAgICAgIH0pOwogICAgfSwKICAgIC8vIOaPkOS6pOWbnuWkjQogICAgc3VibWl0UmVwbHk6IGZ1bmN0aW9uIHN1Ym1pdFJlcGx5KCkgewogICAgICB2YXIgX3RoaXM1ID0gdGhpczsKICAgICAgaWYgKCF0aGlzLnJlcGx5Q29udGVudC50cmltKCkpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+ivt+i+k+WFpeWbnuWkjeWGheWuuScpOwogICAgICAgIHJldHVybjsKICAgICAgfQogICAgICB0aGlzLnJlcGx5aW5nID0gdHJ1ZTsKICAgICAgdmFyIHVzZXJJZCA9IHRoaXMuJHN0b3JhZ2UuZ2V0KCd1c2VySWQnKTsKICAgICAgdmFyIHJlcGx5RGF0YSA9IHsKICAgICAgICBmb3J1bUNvbnRlbnQ6IHRoaXMucmVwbHlDb250ZW50LAogICAgICAgIHN1cGVySWRzOiB0aGlzLmN1cnJlbnRQb3N0LmlkLAogICAgICAgIGZvcnVtU3RhdGVUeXBlczogMiwKICAgICAgICAvLyDlm57lpI3nirbmgIEKICAgICAgICB5b25naHVJZDogdXNlcklkCiAgICAgIH07CiAgICAgIGNvbnNvbGUubG9nKCfmj5DkuqTlm57lpI3mlbDmja46JywgcmVwbHlEYXRhKTsKICAgICAgdGhpcy4kaHR0cCh7CiAgICAgICAgdXJsOiAnZm9ydW0vc2F2ZScsCiAgICAgICAgbWV0aG9kOiAncG9zdCcsCiAgICAgICAgZGF0YTogcmVwbHlEYXRhCiAgICAgIH0pLnRoZW4oZnVuY3Rpb24gKF9yZWY3KSB7CiAgICAgICAgdmFyIGRhdGEgPSBfcmVmNy5kYXRhOwogICAgICAgIGNvbnNvbGUubG9nKCflm57lpI3lk43lupQ6JywgZGF0YSk7CiAgICAgICAgX3RoaXM1LnJlcGx5aW5nID0gZmFsc2U7CiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwKSB7CiAgICAgICAgICBfdGhpczUuJG1lc3NhZ2Uuc3VjY2Vzcygn5Zue5aSN5oiQ5Yqf77yBJyk7CiAgICAgICAgICBfdGhpczUucmVwbHlDb250ZW50ID0gJyc7CiAgICAgICAgICBfdGhpczUubG9hZFJlcGxpZXMoX3RoaXM1LmN1cnJlbnRQb3N0LmlkKTsKICAgICAgICAgIF90aGlzNS5sb2FkU3RhdHMoKTsKICAgICAgICB9IGVsc2UgewogICAgICAgICAgX3RoaXM1LiRtZXNzYWdlLmVycm9yKGRhdGEubXNnIHx8ICflm57lpI3lpLHotKUnKTsKICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WbnuWkjeWksei0peivpuaDhTonLCBkYXRhKTsKICAgICAgICB9CiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uIChlcnJvcikgewogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WbnuWkjeivt+axgumUmeivrzonLCBlcnJvcik7CiAgICAgICAgX3RoaXM1LnJlcGx5aW5nID0gZmFsc2U7CiAgICAgICAgX3RoaXM1LiRtZXNzYWdlLmVycm9yKCfnvZHnu5zplJnor6/vvIzor7fnqI3lkI7ph43or5UnKTsKICAgICAgfSk7CiAgICB9LAogICAgLy8g6I635Y+W57G75Z6L5ZCN56ewCiAgICBnZXRUeXBlTmFtZTogZnVuY3Rpb24gZ2V0VHlwZU5hbWUodHlwZSkgewogICAgICB2YXIgdHlwZU1hcCA9IHsKICAgICAgICAnMSc6ICfov5DliqjliIbkuqsnLAogICAgICAgICcyJzogJ+WBpei6q+W/g+W+lycsCiAgICAgICAgJzMnOiAn5Zy65Zyw5o6o6I2QJywKICAgICAgICAnNCc6ICflhbbku5borqjorronCiAgICAgIH07CiAgICAgIHJldHVybiB0eXBlTWFwW3R5cGVdIHx8ICfmnKrliIbnsbsnOwogICAgfSwKICAgIC8vIOiOt+WPluexu+Wei+agt+W8jwogICAgZ2V0VHlwZUNsYXNzOiBmdW5jdGlvbiBnZXRUeXBlQ2xhc3ModHlwZSkgewogICAgICB2YXIgY2xhc3NNYXAgPSB7CiAgICAgICAgJzEnOiAndHlwZS1zcG9ydCcsCiAgICAgICAgJzInOiAndHlwZS1maXRuZXNzJywKICAgICAgICAnMyc6ICd0eXBlLXZlbnVlJywKICAgICAgICAnNCc6ICd0eXBlLW90aGVyJwogICAgICB9OwogICAgICByZXR1cm4gY2xhc3NNYXBbdHlwZV0gfHwgJ3R5cGUtZGVmYXVsdCc7CiAgICB9LAogICAgLy8g5qC85byP5YyW5pe26Ze0CiAgICBmb3JtYXRUaW1lOiBmdW5jdGlvbiBmb3JtYXRUaW1lKHRpbWUpIHsKICAgICAgaWYgKCF0aW1lKSByZXR1cm4gJyc7CiAgICAgIHZhciBkYXRlID0gbmV3IERhdGUodGltZSk7CiAgICAgIHZhciBub3cgPSBuZXcgRGF0ZSgpOwogICAgICB2YXIgZGlmZiA9IG5vdyAtIGRhdGU7CiAgICAgIGlmIChkaWZmIDwgNjAwMDApIHJldHVybiAn5Yia5YiaJzsKICAgICAgaWYgKGRpZmYgPCAzNjAwMDAwKSByZXR1cm4gTWF0aC5mbG9vcihkaWZmIC8gNjAwMDApICsgJ+WIhumSn+WJjSc7CiAgICAgIGlmIChkaWZmIDwgODY0MDAwMDApIHJldHVybiBNYXRoLmZsb29yKGRpZmYgLyAzNjAwMDAwKSArICflsI/ml7bliY0nOwogICAgICBpZiAoZGlmZiA8IDYwNDgwMDAwMCkgcmV0dXJuIE1hdGguZmxvb3IoZGlmZiAvIDg2NDAwMDAwKSArICflpKnliY0nOwogICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoKTsKICAgIH0sCiAgICAvLyDojrflj5blhoXlrrnpooTop4gKICAgIGdldENvbnRlbnRQcmV2aWV3OiBmdW5jdGlvbiBnZXRDb250ZW50UHJldmlldyhjb250ZW50KSB7CiAgICAgIGlmICghY29udGVudCkgcmV0dXJuICcnOwogICAgICB2YXIgdGV4dCA9IGNvbnRlbnQucmVwbGFjZSgvPFtePl0qPi9nLCAnJyk7IC8vIOenu+mZpEhUTUzmoIfnrb4KICAgICAgcmV0dXJuIHRleHQubGVuZ3RoID4gMTUwID8gdGV4dC5zdWJzdHJpbmcoMCwgMTUwKSArICcuLi4nIDogdGV4dDsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["name", "data", "postList", "totalCount", "currentPage", "pageSize", "searchKeyword", "selectedType", "totalPosts", "todayPosts", "totalReplies", "showNewPostDialog", "newPost", "forumName", "forumContent", "forumTypes", "submitting", "showPostDialog", "currentPost", "replies", "replyContent", "replying", "postRules", "required", "message", "trigger", "min", "max", "mounted", "loadLocalPosts", "loadPosts", "loadStats", "methods", "localPosts", "localStorage", "getItem", "posts", "JSON", "parse", "length", "today", "Date", "toISOString", "split", "filter", "post", "insertTime", "startsWith", "superIds", "console", "log", "e", "error", "createSamplePosts", "samplePosts", "id", "forumStateTypes", "yonghuId", "yo<PERSON><PERSON><PERSON><PERSON>", "createTime", "now", "savePostsToLocal", "setItem", "stringify", "_this", "params", "page", "limit", "$http", "url", "method", "then", "_ref", "code", "list", "total", "$message", "msg", "catch", "_this2", "_ref2", "_ref3", "_ref4", "handleSearch", "handleTypeChange", "handleSizeChange", "val", "handleCurrentChange", "submitPost", "_this3", "$refs", "newPostForm", "validate", "valid", "userId", "$storage", "get", "sessionTable", "postData", "parseInt", "_ref5", "success", "resetNewPost", "savePostLocally", "unshift", "resetFields", "viewPost", "loadReplies", "postId", "_this4", "_ref6", "submitReply", "_this5", "trim", "warning", "replyData", "_ref7", "getTypeName", "type", "typeMap", "getTypeClass", "classMap", "formatTime", "time", "date", "diff", "Math", "floor", "toLocaleDateString", "getContentPreview", "content", "text", "replace", "substring"], "sources": ["src/views/forum.vue"], "sourcesContent": ["<template>\n  <div class=\"forum-container\">\n    <!-- 论坛头部 -->\n    <div class=\"forum-header\">\n      <div class=\"header-content\">\n        <h1 class=\"forum-title\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          体育馆论坛\n        </h1>\n        <p class=\"forum-subtitle\">分享运动心得，交流健身经验</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button \n          type=\"primary\" \n          icon=\"el-icon-edit\" \n          @click=\"showNewPostDialog = true\"\n          class=\"new-post-btn\">\n          发表新帖\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 论坛统计 -->\n    <div class=\"forum-stats\">\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalPosts }}</div>\n        <div class=\"stat-label\">总帖子数</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ todayPosts }}</div>\n        <div class=\"stat-label\">今日发帖</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalReplies }}</div>\n        <div class=\"stat-label\">总回复数</div>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"forum-filters\">\n      <el-input\n        v-model=\"searchKeyword\"\n        placeholder=\"搜索帖子标题或内容...\"\n        prefix-icon=\"el-icon-search\"\n        @input=\"handleSearch\"\n        class=\"search-input\">\n      </el-input>\n      <el-select v-model=\"selectedType\" placeholder=\"帖子类型\" @change=\"handleTypeChange\">\n        <el-option label=\"全部类型\" value=\"\"></el-option>\n        <el-option label=\"运动分享\" value=\"1\"></el-option>\n        <el-option label=\"健身心得\" value=\"2\"></el-option>\n        <el-option label=\"场地推荐\" value=\"3\"></el-option>\n        <el-option label=\"其他讨论\" value=\"4\"></el-option>\n      </el-select>\n    </div>\n\n    <!-- 帖子列表 -->\n    <div class=\"posts-container\">\n      <div \n        v-for=\"post in postList\" \n        :key=\"post.id\" \n        class=\"post-card\"\n        @click=\"viewPost(post)\">\n        <div class=\"post-header\">\n          <div class=\"post-type-tag\" :class=\"getTypeClass(post.forumTypes)\">\n            {{ getTypeName(post.forumTypes) }}\n          </div>\n          <div class=\"post-time\">{{ formatTime(post.insertTime) }}</div>\n        </div>\n        \n        <h3 class=\"post-title\">{{ post.forumName }}</h3>\n        \n        <div class=\"post-content\">\n          {{ getContentPreview(post.forumContent) }}\n        </div>\n        \n        <div class=\"post-footer\">\n          <div class=\"post-author\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"post.yonghuId\">{{ post.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n          </div>\n          <div class=\"post-stats\">\n            <span class=\"reply-count\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              {{ post.replyCount || 0 }} 回复\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 发帖对话框 -->\n    <el-dialog\n      title=\"发表新帖\"\n      :visible.sync=\"showNewPostDialog\"\n      width=\"800px\"\n      class=\"new-post-dialog\">\n      <el-form :model=\"newPost\" :rules=\"postRules\" ref=\"newPostForm\" label-width=\"80px\">\n        <el-form-item label=\"帖子类型\" prop=\"forumTypes\">\n          <el-select v-model=\"newPost.forumTypes\" placeholder=\"请选择帖子类型\">\n            <el-option label=\"运动分享\" value=\"1\"></el-option>\n            <el-option label=\"健身心得\" value=\"2\"></el-option>\n            <el-option label=\"场地推荐\" value=\"3\"></el-option>\n            <el-option label=\"其他讨论\" value=\"4\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子标题\" prop=\"forumName\">\n          <el-input v-model=\"newPost.forumName\" placeholder=\"请输入帖子标题\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子内容\" prop=\"forumContent\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"newPost.forumContent\"\n            :rows=\"8\"\n            placeholder=\"请输入帖子内容...\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showNewPostDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitPost\" :loading=\"submitting\">发布</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 帖子详情对话框 -->\n    <el-dialog\n      :title=\"currentPost.forumName\"\n      :visible.sync=\"showPostDialog\"\n      width=\"900px\"\n      class=\"post-detail-dialog\">\n      <div class=\"post-detail\">\n        <div class=\"post-detail-header\">\n          <div class=\"author-info\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"currentPost.yonghuId\">{{ currentPost.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n            <span class=\"post-time\">{{ formatTime(currentPost.insertTime) }}</span>\n          </div>\n          <div class=\"post-type-tag\" :class=\"getTypeClass(currentPost.forumTypes)\">\n            {{ getTypeName(currentPost.forumTypes) }}\n          </div>\n        </div>\n        \n        <div class=\"post-detail-content\" v-html=\"currentPost.forumContent\"></div>\n        \n        <!-- 回复列表 -->\n        <div class=\"replies-section\">\n          <h4>回复 ({{ replies.length }})</h4>\n          <div v-for=\"reply in replies\" :key=\"reply.id\" class=\"reply-item\">\n            <div class=\"reply-author\">\n              <i class=\"el-icon-user\"></i>\n              <span v-if=\"reply.yonghuId\">{{ reply.yonghuName || '用户' }}</span>\n              <span v-else>管理员</span>\n              <span class=\"reply-time\">{{ formatTime(reply.insertTime) }}</span>\n            </div>\n            <div class=\"reply-content\">{{ reply.forumContent }}</div>\n          </div>\n        </div>\n        \n        <!-- 回复表单 -->\n        <div class=\"reply-form\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"replyContent\"\n            :rows=\"3\"\n            placeholder=\"写下你的回复...\">\n          </el-input>\n          <div class=\"reply-actions\">\n            <el-button type=\"primary\" @click=\"submitReply\" :loading=\"replying\">回复</el-button>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Forum',\n  data() {\n    return {\n      // 帖子列表\n      postList: [],\n      totalCount: 0,\n      currentPage: 1,\n      pageSize: 10,\n      \n      // 搜索和筛选\n      searchKeyword: '',\n      selectedType: '',\n      \n      // 统计数据\n      totalPosts: 0,\n      todayPosts: 0,\n      totalReplies: 0,\n      \n      // 发帖\n      showNewPostDialog: false,\n      newPost: {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      },\n      submitting: false,\n      \n      // 帖子详情\n      showPostDialog: false,\n      currentPost: {},\n      replies: [],\n      replyContent: '',\n      replying: false,\n      \n      // 表单验证\n      postRules: {\n        forumTypes: [\n          { required: true, message: '请选择帖子类型', trigger: 'change' }\n        ],\n        forumName: [\n          { required: true, message: '请输入帖子标题', trigger: 'blur' },\n          { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }\n        ],\n        forumContent: [\n          { required: true, message: '请输入帖子内容', trigger: 'blur' },\n          { min: 10, message: '内容至少 10 个字符', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  \n  mounted() {\n    // 检查是否有本地存储的帖子数据\n    this.loadLocalPosts()\n    this.loadPosts()\n    this.loadStats()\n  },\n  \n  methods: {\n    // 加载本地存储的帖子\n    loadLocalPosts() {\n      const localPosts = localStorage.getItem('forum_posts')\n      if (localPosts) {\n        try {\n          const posts = JSON.parse(localPosts)\n          this.postList = posts\n          this.totalCount = posts.length\n          this.totalPosts = posts.length\n\n          // 计算今日发帖数\n          const today = new Date().toISOString().split('T')[0]\n          this.todayPosts = posts.filter(post =>\n            post.insertTime && post.insertTime.startsWith(today)\n          ).length\n\n          // 计算回复数（这里简化处理）\n          this.totalReplies = posts.filter(post => post.superIds).length\n\n          console.log('从本地存储加载了', posts.length, '个帖子')\n          return\n        } catch (e) {\n          console.error('解析本地帖子数据失败:', e)\n        }\n      }\n\n      // 如果没有本地数据，创建一些示例数据\n      this.createSamplePosts()\n    },\n\n    // 创建示例帖子数据\n    createSamplePosts() {\n      const samplePosts = [\n        {\n          id: 1,\n          forumName: '欢迎来到体育馆论坛！',\n          forumContent: '这里是大家交流运动心得、分享健身经验的地方。欢迎大家积极参与讨论！',\n          forumTypes: 4,\n          forumStateTypes: 1,\n          yonghuId: 1,\n          yonghuName: '管理员',\n          insertTime: new Date().toISOString(),\n          createTime: new Date().toISOString()\n        },\n        {\n          id: 2,\n          forumName: '篮球场地推荐',\n          forumContent: '推荐几个不错的篮球场地，设施齐全，环境优美，适合各种水平的球友。',\n          forumTypes: 3,\n          forumStateTypes: 1,\n          yonghuId: 2,\n          yonghuName: '篮球爱好者',\n          insertTime: new Date(Date.now() - 3600000).toISOString(),\n          createTime: new Date(Date.now() - 3600000).toISOString()\n        },\n        {\n          id: 3,\n          forumName: '健身房训练心得',\n          forumContent: '分享一些健身房训练的心得体会，包括器械使用技巧和训练计划安排。',\n          forumTypes: 2,\n          forumStateTypes: 1,\n          yonghuId: 3,\n          yonghuName: '健身达人',\n          insertTime: new Date(Date.now() - 7200000).toISOString(),\n          createTime: new Date(Date.now() - 7200000).toISOString()\n        }\n      ]\n\n      this.postList = samplePosts\n      this.totalCount = samplePosts.length\n      this.totalPosts = samplePosts.length\n      this.todayPosts = 1\n      this.totalReplies = 0\n\n      // 保存到本地存储\n      this.savePostsToLocal()\n\n      console.log('创建了示例帖子数据')\n    },\n\n    // 保存帖子到本地存储\n    savePostsToLocal() {\n      try {\n        localStorage.setItem('forum_posts', JSON.stringify(this.postList))\n      } catch (e) {\n        console.error('保存帖子到本地存储失败:', e)\n      }\n    },\n\n    // 加载帖子列表\n    loadPosts() {\n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize,\n        forumStateTypes: 1 // 只显示已审核的帖子\n      }\n\n      if (this.searchKeyword) {\n        params.forumName = this.searchKeyword\n      }\n\n      if (this.selectedType) {\n        params.forumTypes = this.selectedType\n      }\n\n      console.log('加载帖子参数:', params)\n\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        console.log('帖子列表响应:', data)\n        if (data && data.code === 0) {\n          this.postList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        } else {\n          console.error('获取帖子列表失败:', data)\n          this.$message.error(data.msg || '获取帖子列表失败')\n        }\n      }).catch((error) => {\n        console.error('加载帖子网络错误:', error)\n        // 后端不可用时，不显示错误消息，使用本地数据\n        console.log('使用本地存储的帖子数据')\n      })\n    },\n    \n    // 加载统计数据\n    loadStats() {\n      // 获取总帖子数\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalPosts = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取总帖子数失败')\n      })\n\n      // 获取今日发帖数\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, insertTime: today }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.todayPosts = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取今日发帖数失败')\n      })\n\n      // 获取回复数\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 2 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalReplies = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取回复数失败')\n      })\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 类型筛选\n    handleTypeChange() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 分页处理\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.loadPosts()\n    },\n\n    handleCurrentChange(val) {\n      this.currentPage = val\n      this.loadPosts()\n    },\n\n    // 发帖\n    submitPost() {\n      this.$refs.newPostForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n\n          const userId = this.$storage.get('userId')\n          const sessionTable = this.$storage.get('sessionTable')\n\n          console.log('发帖数据准备:', {\n            userId,\n            sessionTable,\n            newPost: this.newPost\n          })\n\n          const postData = {\n            forumName: this.newPost.forumName,\n            forumContent: this.newPost.forumContent,\n            forumTypes: parseInt(this.newPost.forumTypes),\n            forumStateTypes: 1, // 直接审核通过\n            yonghuId: userId\n          }\n\n          console.log('提交的帖子数据:', postData)\n\n          this.$http({\n            url: 'forum/save',\n            method: 'post',\n            data: postData\n          }).then(({ data }) => {\n            console.log('发帖响应:', data)\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('发帖成功！')\n              this.showNewPostDialog = false\n              this.resetNewPost()\n              this.loadPosts()\n              this.loadStats()\n            } else {\n              this.$message.error(data.msg || '发帖失败，请稍后重试')\n              console.error('发帖失败详情:', data)\n            }\n          }).catch((error) => {\n            console.error('发帖请求错误:', error)\n            // 后端不可用时，使用本地存储\n            this.savePostLocally(postData)\n          })\n        }\n      })\n    },\n\n    // 本地保存帖子\n    savePostLocally(postData) {\n      try {\n        // 创建新帖子对象\n        const newPost = {\n          id: Date.now(), // 使用时间戳作为ID\n          forumName: postData.forumName,\n          forumContent: postData.forumContent,\n          forumTypes: postData.forumTypes,\n          forumStateTypes: 1,\n          yonghuId: postData.yonghuId,\n          yonghuName: this.$storage.get('username') || '用户',\n          insertTime: new Date().toISOString(),\n          createTime: new Date().toISOString()\n        }\n\n        // 添加到帖子列表\n        this.postList.unshift(newPost)\n        this.totalCount = this.postList.length\n        this.totalPosts = this.postList.length\n        this.todayPosts += 1\n\n        // 保存到本地存储\n        this.savePostsToLocal()\n\n        this.submitting = false\n        this.$message.success('发帖成功！（已保存到本地）')\n        this.showNewPostDialog = false\n        this.resetNewPost()\n\n        console.log('帖子已保存到本地存储')\n      } catch (error) {\n        console.error('本地保存帖子失败:', error)\n        this.submitting = false\n        this.$message.error('发帖失败')\n      }\n    },\n\n    // 重置发帖表单\n    resetNewPost() {\n      this.newPost = {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      }\n      this.$refs.newPostForm && this.$refs.newPostForm.resetFields()\n    },\n\n    // 查看帖子详情\n    viewPost(post) {\n      this.currentPost = post\n      this.showPostDialog = true\n      this.loadReplies(post.id)\n    },\n\n    // 加载回复\n    loadReplies(postId) {\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: {\n          superIds: postId,\n          forumStateTypes: 2\n        }\n      }).then(({ data }) => {\n        console.log('回复列表响应:', data)\n        if (data && data.code === 0) {\n          this.replies = data.data.list || []\n        }\n      }).catch((error) => {\n        console.error('加载回复失败:', error)\n      })\n    },\n\n    // 提交回复\n    submitReply() {\n      if (!this.replyContent.trim()) {\n        this.$message.warning('请输入回复内容')\n        return\n      }\n\n      this.replying = true\n\n      const userId = this.$storage.get('userId')\n\n      const replyData = {\n        forumContent: this.replyContent,\n        superIds: this.currentPost.id,\n        forumStateTypes: 2, // 回复状态\n        yonghuId: userId\n      }\n\n      console.log('提交回复数据:', replyData)\n\n      this.$http({\n        url: 'forum/save',\n        method: 'post',\n        data: replyData\n      }).then(({ data }) => {\n        console.log('回复响应:', data)\n        this.replying = false\n        if (data && data.code === 0) {\n          this.$message.success('回复成功！')\n          this.replyContent = ''\n          this.loadReplies(this.currentPost.id)\n          this.loadStats()\n        } else {\n          this.$message.error(data.msg || '回复失败')\n          console.error('回复失败详情:', data)\n        }\n      }).catch((error) => {\n        console.error('回复请求错误:', error)\n        this.replying = false\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n\n    // 获取类型名称\n    getTypeName(type) {\n      const typeMap = {\n        '1': '运动分享',\n        '2': '健身心得',\n        '3': '场地推荐',\n        '4': '其他讨论'\n      }\n      return typeMap[type] || '未分类'\n    },\n\n    // 获取类型样式\n    getTypeClass(type) {\n      const classMap = {\n        '1': 'type-sport',\n        '2': 'type-fitness',\n        '3': 'type-venue',\n        '4': 'type-other'\n      }\n      return classMap[type] || 'type-default'\n    },\n\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      const now = new Date()\n      const diff = now - date\n\n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'\n      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'\n      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'\n\n      return date.toLocaleDateString()\n    },\n\n    // 获取内容预览\n    getContentPreview(content) {\n      if (!content) return ''\n      const text = content.replace(/<[^>]*>/g, '') // 移除HTML标签\n      return text.length > 150 ? text.substring(0, 150) + '...' : text\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.forum-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n// 论坛头部\n.forum-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 30px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n\n  .header-content {\n    .forum-title {\n      font-size: 28px;\n      margin: 0 0 8px 0;\n      font-weight: 600;\n\n      i {\n        margin-right: 10px;\n        color: #ffd700;\n      }\n    }\n\n    .forum-subtitle {\n      font-size: 16px;\n      margin: 0;\n      opacity: 0.9;\n    }\n  }\n\n  .new-post-btn {\n    background: rgba(255, 255, 255, 0.2);\n    border: 1px solid rgba(255, 255, 255, 0.3);\n    color: white;\n\n    &:hover {\n      background: rgba(255, 255, 255, 0.3);\n    }\n  }\n}\n\n// 论坛统计\n.forum-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n\n  .stat-item {\n    background: white;\n    padding: 25px;\n    border-radius: 12px;\n    text-align: center;\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n    transition: transform 0.3s ease;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    .stat-number {\n      font-size: 32px;\n      font-weight: bold;\n      color: #409eff;\n      margin-bottom: 8px;\n    }\n\n    .stat-label {\n      color: #666;\n      font-size: 14px;\n    }\n  }\n}\n\n// 搜索和筛选\n.forum-filters {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 25px;\n\n  .search-input {\n    flex: 1;\n    max-width: 400px;\n  }\n}\n\n// 帖子容器\n.posts-container {\n  display: grid;\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n// 帖子卡片\n.post-card {\n  background: white;\n  border-radius: 12px;\n  padding: 25px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n\n  .post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .post-time {\n      color: #999;\n      font-size: 14px;\n    }\n  }\n\n  .post-title {\n    font-size: 20px;\n    font-weight: 600;\n    color: #2c3e50;\n    margin: 0 0 15px 0;\n    line-height: 1.4;\n  }\n\n  .post-content {\n    color: #666;\n    line-height: 1.6;\n    margin-bottom: 20px;\n  }\n\n  .post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .post-author {\n      display: flex;\n      align-items: center;\n      color: #666;\n      font-size: 14px;\n\n      i {\n        margin-right: 5px;\n      }\n    }\n\n    .post-stats {\n      .reply-count {\n        color: #409eff;\n        font-size: 14px;\n\n        i {\n          margin-right: 5px;\n        }\n      }\n    }\n  }\n}\n\n// 类型标签\n.post-type-tag {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n\n  &.type-sport {\n    background: #e8f5e8;\n    color: #52c41a;\n  }\n\n  &.type-fitness {\n    background: #fff2e8;\n    color: #fa8c16;\n  }\n\n  &.type-venue {\n    background: #e6f7ff;\n    color: #1890ff;\n  }\n\n  &.type-other {\n    background: #f6f6f6;\n    color: #666;\n  }\n}\n\n// 分页\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 30px;\n}\n\n// 对话框样式\n.new-post-dialog, .post-detail-dialog {\n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n// 帖子详情\n.post-detail {\n  .post-detail-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding-bottom: 20px;\n    border-bottom: 1px solid #eee;\n    margin-bottom: 20px;\n\n    .author-info {\n      display: flex;\n      align-items: center;\n      color: #666;\n\n      i {\n        margin-right: 8px;\n      }\n\n      .post-time {\n        margin-left: 15px;\n        font-size: 14px;\n      }\n    }\n  }\n\n  .post-detail-content {\n    line-height: 1.8;\n    margin-bottom: 30px;\n    color: #333;\n  }\n\n  .replies-section {\n    border-top: 1px solid #eee;\n    padding-top: 20px;\n    margin-bottom: 20px;\n\n    h4 {\n      margin-bottom: 20px;\n      color: #333;\n    }\n\n    .reply-item {\n      background: #f8f9fa;\n      padding: 15px;\n      border-radius: 8px;\n      margin-bottom: 15px;\n\n      .reply-author {\n        display: flex;\n        align-items: center;\n        color: #666;\n        font-size: 14px;\n        margin-bottom: 10px;\n\n        i {\n          margin-right: 5px;\n        }\n\n        .reply-time {\n          margin-left: 10px;\n        }\n      }\n\n      .reply-content {\n        color: #333;\n        line-height: 1.6;\n      }\n    }\n  }\n\n  .reply-form {\n    .reply-actions {\n      margin-top: 15px;\n      text-align: right;\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;AAkMA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACA;MACAC,QAAA;MACAC,UAAA;MACAC,WAAA;MACAC,QAAA;MAEA;MACAC,aAAA;MACAC,YAAA;MAEA;MACAC,UAAA;MACAC,UAAA;MACAC,YAAA;MAEA;MACAC,iBAAA;MACAC,OAAA;QACAC,SAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACAC,UAAA;MAEA;MACAC,cAAA;MACAC,WAAA;MACAC,OAAA;MACAC,YAAA;MACAC,QAAA;MAEA;MACAC,SAAA;QACAP,UAAA,GACA;UAAAQ,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,SAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAC,GAAA;UAAAH,OAAA;UAAAC,OAAA;QAAA,EACA;QACAX,YAAA,GACA;UAAAS,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,GAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;IACA;EACA;EAEAG,OAAA,WAAAA,QAAA;IACA;IACA,KAAAC,cAAA;IACA,KAAAC,SAAA;IACA,KAAAC,SAAA;EACA;EAEAC,OAAA;IACA;IACAH,cAAA,WAAAA,eAAA;MACA,IAAAI,UAAA,GAAAC,YAAA,CAAAC,OAAA;MACA,IAAAF,UAAA;QACA;UACA,IAAAG,KAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAL,UAAA;UACA,KAAA/B,QAAA,GAAAkC,KAAA;UACA,KAAAjC,UAAA,GAAAiC,KAAA,CAAAG,MAAA;UACA,KAAA/B,UAAA,GAAA4B,KAAA,CAAAG,MAAA;;UAEA;UACA,IAAAC,KAAA,OAAAC,IAAA,GAAAC,WAAA,GAAAC,KAAA;UACA,KAAAlC,UAAA,GAAA2B,KAAA,CAAAQ,MAAA,WAAAC,IAAA;YAAA,OACAA,IAAA,CAAAC,UAAA,IAAAD,IAAA,CAAAC,UAAA,CAAAC,UAAA,CAAAP,KAAA;UAAA,CACA,EAAAD,MAAA;;UAEA;UACA,KAAA7B,YAAA,GAAA0B,KAAA,CAAAQ,MAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAAG,QAAA;UAAA,GAAAT,MAAA;UAEAU,OAAA,CAAAC,GAAA,aAAAd,KAAA,CAAAG,MAAA;UACA;QACA,SAAAY,CAAA;UACAF,OAAA,CAAAG,KAAA,gBAAAD,CAAA;QACA;MACA;;MAEA;MACA,KAAAE,iBAAA;IACA;IAEA;IACAA,iBAAA,WAAAA,kBAAA;MACA,IAAAC,WAAA,IACA;QACAC,EAAA;QACA1C,SAAA;QACAC,YAAA;QACAC,UAAA;QACAyC,eAAA;QACAC,QAAA;QACAC,UAAA;QACAZ,UAAA,MAAAL,IAAA,GAAAC,WAAA;QACAiB,UAAA,MAAAlB,IAAA,GAAAC,WAAA;MACA,GACA;QACAa,EAAA;QACA1C,SAAA;QACAC,YAAA;QACAC,UAAA;QACAyC,eAAA;QACAC,QAAA;QACAC,UAAA;QACAZ,UAAA,MAAAL,IAAA,CAAAA,IAAA,CAAAmB,GAAA,cAAAlB,WAAA;QACAiB,UAAA,MAAAlB,IAAA,CAAAA,IAAA,CAAAmB,GAAA,cAAAlB,WAAA;MACA,GACA;QACAa,EAAA;QACA1C,SAAA;QACAC,YAAA;QACAC,UAAA;QACAyC,eAAA;QACAC,QAAA;QACAC,UAAA;QACAZ,UAAA,MAAAL,IAAA,CAAAA,IAAA,CAAAmB,GAAA,cAAAlB,WAAA;QACAiB,UAAA,MAAAlB,IAAA,CAAAA,IAAA,CAAAmB,GAAA,cAAAlB,WAAA;MACA,EACA;MAEA,KAAAxC,QAAA,GAAAoD,WAAA;MACA,KAAAnD,UAAA,GAAAmD,WAAA,CAAAf,MAAA;MACA,KAAA/B,UAAA,GAAA8C,WAAA,CAAAf,MAAA;MACA,KAAA9B,UAAA;MACA,KAAAC,YAAA;;MAEA;MACA,KAAAmD,gBAAA;MAEAZ,OAAA,CAAAC,GAAA;IACA;IAEA;IACAW,gBAAA,WAAAA,iBAAA;MACA;QACA3B,YAAA,CAAA4B,OAAA,gBAAAzB,IAAA,CAAA0B,SAAA,MAAA7D,QAAA;MACA,SAAAiD,CAAA;QACAF,OAAA,CAAAG,KAAA,iBAAAD,CAAA;MACA;IACA;IAEA;IACArB,SAAA,WAAAA,UAAA;MAAA,IAAAkC,KAAA;MACA,IAAAC,MAAA;QACAC,IAAA,OAAA9D,WAAA;QACA+D,KAAA,OAAA9D,QAAA;QACAmD,eAAA;MACA;MAEA,SAAAlD,aAAA;QACA2D,MAAA,CAAApD,SAAA,QAAAP,aAAA;MACA;MAEA,SAAAC,YAAA;QACA0D,MAAA,CAAAlD,UAAA,QAAAR,YAAA;MACA;MAEA0C,OAAA,CAAAC,GAAA,YAAAe,MAAA;MAEA,KAAAG,KAAA;QACAC,GAAA;QACAC,MAAA;QACAL,MAAA,EAAAA;MACA,GAAAM,IAAA,WAAAC,IAAA;QAAA,IAAAvE,IAAA,GAAAuE,IAAA,CAAAvE,IAAA;QACAgD,OAAA,CAAAC,GAAA,YAAAjD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAwE,IAAA;UACAT,KAAA,CAAA9D,QAAA,GAAAD,IAAA,CAAAA,IAAA,CAAAyE,IAAA;UACAV,KAAA,CAAA7D,UAAA,GAAAF,IAAA,CAAAA,IAAA,CAAA0E,KAAA;QACA;UACA1B,OAAA,CAAAG,KAAA,cAAAnD,IAAA;UACA+D,KAAA,CAAAY,QAAA,CAAAxB,KAAA,CAAAnD,IAAA,CAAA4E,GAAA;QACA;MACA,GAAAC,KAAA,WAAA1B,KAAA;QACAH,OAAA,CAAAG,KAAA,cAAAA,KAAA;QACA;QACAH,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAnB,SAAA,WAAAA,UAAA;MAAA,IAAAgD,MAAA;MACA;MACA,KAAAX,KAAA;QACAC,GAAA;QACAC,MAAA;QACAL,MAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAX,eAAA;QAAA;MACA,GAAAe,IAAA,WAAAS,KAAA;QAAA,IAAA/E,IAAA,GAAA+E,KAAA,CAAA/E,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAwE,IAAA;UACAM,MAAA,CAAAvE,UAAA,GAAAP,IAAA,CAAAA,IAAA,CAAA0E,KAAA;QACA;MACA,GAAAG,KAAA;QACA7B,OAAA,CAAAC,GAAA;MACA;;MAEA;MACA,IAAAV,KAAA,OAAAC,IAAA,GAAAC,WAAA,GAAAC,KAAA;MACA,KAAAyB,KAAA;QACAC,GAAA;QACAC,MAAA;QACAL,MAAA;UAAAC,IAAA;UAAAC,KAAA;UAAArB,UAAA,EAAAN;QAAA;MACA,GAAA+B,IAAA,WAAAU,KAAA;QAAA,IAAAhF,IAAA,GAAAgF,KAAA,CAAAhF,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAwE,IAAA;UACAM,MAAA,CAAAtE,UAAA,GAAAR,IAAA,CAAAA,IAAA,CAAA0E,KAAA;QACA;MACA,GAAAG,KAAA;QACA7B,OAAA,CAAAC,GAAA;MACA;;MAEA;MACA,KAAAkB,KAAA;QACAC,GAAA;QACAC,MAAA;QACAL,MAAA;UAAAC,IAAA;UAAAC,KAAA;UAAAX,eAAA;QAAA;MACA,GAAAe,IAAA,WAAAW,KAAA;QAAA,IAAAjF,IAAA,GAAAiF,KAAA,CAAAjF,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAwE,IAAA;UACAM,MAAA,CAAArE,YAAA,GAAAT,IAAA,CAAAA,IAAA,CAAA0E,KAAA;QACA;MACA,GAAAG,KAAA;QACA7B,OAAA,CAAAC,GAAA;MACA;IACA;IAEA;IACAiC,YAAA,WAAAA,aAAA;MACA,KAAA/E,WAAA;MACA,KAAA0B,SAAA;IACA;IAEA;IACAsD,gBAAA,WAAAA,iBAAA;MACA,KAAAhF,WAAA;MACA,KAAA0B,SAAA;IACA;IAEA;IACAuD,gBAAA,WAAAA,iBAAAC,GAAA;MACA,KAAAjF,QAAA,GAAAiF,GAAA;MACA,KAAAxD,SAAA;IACA;IAEAyD,mBAAA,WAAAA,oBAAAD,GAAA;MACA,KAAAlF,WAAA,GAAAkF,GAAA;MACA,KAAAxD,SAAA;IACA;IAEA;IACA0D,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAAC,WAAA,CAAAC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAJ,MAAA,CAAAzE,UAAA;UAEA,IAAA8E,MAAA,GAAAL,MAAA,CAAAM,QAAA,CAAAC,GAAA;UACA,IAAAC,YAAA,GAAAR,MAAA,CAAAM,QAAA,CAAAC,GAAA;UAEA/C,OAAA,CAAAC,GAAA;YACA4C,MAAA,EAAAA,MAAA;YACAG,YAAA,EAAAA,YAAA;YACArF,OAAA,EAAA6E,MAAA,CAAA7E;UACA;UAEA,IAAAsF,QAAA;YACArF,SAAA,EAAA4E,MAAA,CAAA7E,OAAA,CAAAC,SAAA;YACAC,YAAA,EAAA2E,MAAA,CAAA7E,OAAA,CAAAE,YAAA;YACAC,UAAA,EAAAoF,QAAA,CAAAV,MAAA,CAAA7E,OAAA,CAAAG,UAAA;YACAyC,eAAA;YAAA;YACAC,QAAA,EAAAqC;UACA;UAEA7C,OAAA,CAAAC,GAAA,aAAAgD,QAAA;UAEAT,MAAA,CAAArB,KAAA;YACAC,GAAA;YACAC,MAAA;YACArE,IAAA,EAAAiG;UACA,GAAA3B,IAAA,WAAA6B,KAAA;YAAA,IAAAnG,IAAA,GAAAmG,KAAA,CAAAnG,IAAA;YACAgD,OAAA,CAAAC,GAAA,UAAAjD,IAAA;YACAwF,MAAA,CAAAzE,UAAA;YACA,IAAAf,IAAA,IAAAA,IAAA,CAAAwE,IAAA;cACAgB,MAAA,CAAAb,QAAA,CAAAyB,OAAA;cACAZ,MAAA,CAAA9E,iBAAA;cACA8E,MAAA,CAAAa,YAAA;cACAb,MAAA,CAAA3D,SAAA;cACA2D,MAAA,CAAA1D,SAAA;YACA;cACA0D,MAAA,CAAAb,QAAA,CAAAxB,KAAA,CAAAnD,IAAA,CAAA4E,GAAA;cACA5B,OAAA,CAAAG,KAAA,YAAAnD,IAAA;YACA;UACA,GAAA6E,KAAA,WAAA1B,KAAA;YACAH,OAAA,CAAAG,KAAA,YAAAA,KAAA;YACA;YACAqC,MAAA,CAAAc,eAAA,CAAAL,QAAA;UACA;QACA;MACA;IACA;IAEA;IACAK,eAAA,WAAAA,gBAAAL,QAAA;MACA;QACA;QACA,IAAAtF,OAAA;UACA2C,EAAA,EAAAd,IAAA,CAAAmB,GAAA;UAAA;UACA/C,SAAA,EAAAqF,QAAA,CAAArF,SAAA;UACAC,YAAA,EAAAoF,QAAA,CAAApF,YAAA;UACAC,UAAA,EAAAmF,QAAA,CAAAnF,UAAA;UACAyC,eAAA;UACAC,QAAA,EAAAyC,QAAA,CAAAzC,QAAA;UACAC,UAAA,OAAAqC,QAAA,CAAAC,GAAA;UACAlD,UAAA,MAAAL,IAAA,GAAAC,WAAA;UACAiB,UAAA,MAAAlB,IAAA,GAAAC,WAAA;QACA;;QAEA;QACA,KAAAxC,QAAA,CAAAsG,OAAA,CAAA5F,OAAA;QACA,KAAAT,UAAA,QAAAD,QAAA,CAAAqC,MAAA;QACA,KAAA/B,UAAA,QAAAN,QAAA,CAAAqC,MAAA;QACA,KAAA9B,UAAA;;QAEA;QACA,KAAAoD,gBAAA;QAEA,KAAA7C,UAAA;QACA,KAAA4D,QAAA,CAAAyB,OAAA;QACA,KAAA1F,iBAAA;QACA,KAAA2F,YAAA;QAEArD,OAAA,CAAAC,GAAA;MACA,SAAAE,KAAA;QACAH,OAAA,CAAAG,KAAA,cAAAA,KAAA;QACA,KAAApC,UAAA;QACA,KAAA4D,QAAA,CAAAxB,KAAA;MACA;IACA;IAEA;IACAkD,YAAA,WAAAA,aAAA;MACA,KAAA1F,OAAA;QACAC,SAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACA,KAAA2E,KAAA,CAAAC,WAAA,SAAAD,KAAA,CAAAC,WAAA,CAAAc,WAAA;IACA;IAEA;IACAC,QAAA,WAAAA,SAAA7D,IAAA;MACA,KAAA3B,WAAA,GAAA2B,IAAA;MACA,KAAA5B,cAAA;MACA,KAAA0F,WAAA,CAAA9D,IAAA,CAAAU,EAAA;IACA;IAEA;IACAoD,WAAA,WAAAA,YAAAC,MAAA;MAAA,IAAAC,MAAA;MACA,KAAAzC,KAAA;QACAC,GAAA;QACAC,MAAA;QACAL,MAAA;UACAjB,QAAA,EAAA4D,MAAA;UACApD,eAAA;QACA;MACA,GAAAe,IAAA,WAAAuC,KAAA;QAAA,IAAA7G,IAAA,GAAA6G,KAAA,CAAA7G,IAAA;QACAgD,OAAA,CAAAC,GAAA,YAAAjD,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAwE,IAAA;UACAoC,MAAA,CAAA1F,OAAA,GAAAlB,IAAA,CAAAA,IAAA,CAAAyE,IAAA;QACA;MACA,GAAAI,KAAA,WAAA1B,KAAA;QACAH,OAAA,CAAAG,KAAA,YAAAA,KAAA;MACA;IACA;IAEA;IACA2D,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAA5F,YAAA,CAAA6F,IAAA;QACA,KAAArC,QAAA,CAAAsC,OAAA;QACA;MACA;MAEA,KAAA7F,QAAA;MAEA,IAAAyE,MAAA,QAAAC,QAAA,CAAAC,GAAA;MAEA,IAAAmB,SAAA;QACArG,YAAA,OAAAM,YAAA;QACA4B,QAAA,OAAA9B,WAAA,CAAAqC,EAAA;QACAC,eAAA;QAAA;QACAC,QAAA,EAAAqC;MACA;MAEA7C,OAAA,CAAAC,GAAA,YAAAiE,SAAA;MAEA,KAAA/C,KAAA;QACAC,GAAA;QACAC,MAAA;QACArE,IAAA,EAAAkH;MACA,GAAA5C,IAAA,WAAA6C,KAAA;QAAA,IAAAnH,IAAA,GAAAmH,KAAA,CAAAnH,IAAA;QACAgD,OAAA,CAAAC,GAAA,UAAAjD,IAAA;QACA+G,MAAA,CAAA3F,QAAA;QACA,IAAApB,IAAA,IAAAA,IAAA,CAAAwE,IAAA;UACAuC,MAAA,CAAApC,QAAA,CAAAyB,OAAA;UACAW,MAAA,CAAA5F,YAAA;UACA4F,MAAA,CAAAL,WAAA,CAAAK,MAAA,CAAA9F,WAAA,CAAAqC,EAAA;UACAyD,MAAA,CAAAjF,SAAA;QACA;UACAiF,MAAA,CAAApC,QAAA,CAAAxB,KAAA,CAAAnD,IAAA,CAAA4E,GAAA;UACA5B,OAAA,CAAAG,KAAA,YAAAnD,IAAA;QACA;MACA,GAAA6E,KAAA,WAAA1B,KAAA;QACAH,OAAA,CAAAG,KAAA,YAAAA,KAAA;QACA4D,MAAA,CAAA3F,QAAA;QACA2F,MAAA,CAAApC,QAAA,CAAAxB,KAAA;MACA;IACA;IAEA;IACAiE,WAAA,WAAAA,YAAAC,IAAA;MACA,IAAAC,OAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,OAAA,CAAAD,IAAA;IACA;IAEA;IACAE,YAAA,WAAAA,aAAAF,IAAA;MACA,IAAAG,QAAA;QACA;QACA;QACA;QACA;MACA;MACA,OAAAA,QAAA,CAAAH,IAAA;IACA;IAEA;IACAI,UAAA,WAAAA,WAAAC,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,IAAA,OAAAnF,IAAA,CAAAkF,IAAA;MACA,IAAA/D,GAAA,OAAAnB,IAAA;MACA,IAAAoF,IAAA,GAAAjE,GAAA,GAAAgE,IAAA;MAEA,IAAAC,IAAA;MACA,IAAAA,IAAA,mBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA,IAAAA,IAAA,oBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MACA,IAAAA,IAAA,qBAAAC,IAAA,CAAAC,KAAA,CAAAF,IAAA;MAEA,OAAAD,IAAA,CAAAI,kBAAA;IACA;IAEA;IACAC,iBAAA,WAAAA,kBAAAC,OAAA;MACA,KAAAA,OAAA;MACA,IAAAC,IAAA,GAAAD,OAAA,CAAAE,OAAA;MACA,OAAAD,IAAA,CAAA5F,MAAA,SAAA4F,IAAA,CAAAE,SAAA,mBAAAF,IAAA;IACA;EACA;AACA", "ignoreList": []}]}