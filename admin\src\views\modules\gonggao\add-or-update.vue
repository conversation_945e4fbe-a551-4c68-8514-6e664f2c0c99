







<template>
    <div class="gonggao-form-container">
        <div class="form-header">
            <h3 v-if="type === 'info'">公告详情</h3>
            <h3 v-else-if="!ruleForm.id">新增公告</h3>
            <h3 v-else>编辑公告</h3>
            <p>管理系统公告和轮播图信息</p>
        </div>

        <el-card class="form-card">
            <el-form
                class="gonggao-form"
                ref="ruleForm"
                :model="ruleForm"
                :rules="rules"
                label-width="120px">

                <el-row :gutter="20">
                    <input id="updateId" name="id" type="hidden">

                    <!-- 基本信息 -->
                    <el-col :span="24">
                        <div class="section-title">基本信息</div>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="公告名称" prop="gonggaoName">
                            <el-input
                                v-model="ruleForm.gonggaoName"
                                placeholder="请输入公告名称"
                                clearable
                                :readonly="ro.gonggaoName || type === 'info'"
                                prefix-icon="el-icon-edit-outline">
                            </el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="12">
                        <el-form-item label="公告类型" prop="gonggaoTypes">
                            <el-select
                                v-if="type !== 'info'"
                                v-model="ruleForm.gonggaoTypes"
                                placeholder="请选择公告类型"
                                style="width: 100%">
                                <el-option
                                    v-for="(item,index) in gonggaoTypesOptions"
                                    v-bind:key="item.codeIndex"
                                    :label="item.indexName"
                                    :value="item.codeIndex">
                                </el-option>
                            </el-select>
                            <el-input
                                v-else
                                v-model="ruleForm.gonggaoValue"
                                placeholder="公告类型"
                                readonly
                                prefix-icon="el-icon-menu">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <!-- 图片信息 -->
                    <el-col :span="24">
                        <div class="section-title">图片信息</div>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="公告图片" prop="gonggaoPhoto">
                            <file-upload
                                v-if="type !== 'info' && !ro.gonggaoPhoto"
                                tip="点击上传公告图片，建议尺寸：1920x600px"
                                action="file/upload"
                                :limit="5"
                                :multiple="true"
                                :fileUrls="ruleForm.gonggaoPhoto?ruleForm.gonggaoPhoto:''"
                                @change="gonggaoPhotoUploadChange"
                            ></file-upload>
                            <div v-else-if="ruleForm.gonggaoPhoto" class="photo-preview">
                                <img
                                    v-for="(item,index) in (ruleForm.gonggaoPhoto || '').split(',')"
                                    :key="index"
                                    :src="item"
                                    class="preview-image"
                                    @click="previewImage(item)">
                            </div>
                            <div v-else class="no-image">暂无图片</div>
                        </el-form-item>
                    </el-col>

                    <!-- 详细信息 -->
                    <el-col :span="24">
                        <div class="section-title">详细信息</div>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="公告详情" prop="gonggaoContent">
                            <editor
                                v-if="type !== 'info'"
                                v-model="ruleForm.gonggaoContent"
                                class="editor"
                                action="file/upload"
                                placeholder="请输入公告的详细内容...">
                            </editor>
                            <div v-else-if="ruleForm.gonggaoContent" class="content-preview">
                                <div v-html="ruleForm.gonggaoContent"></div>
                            </div>
                            <div v-else class="no-content">暂无详情</div>
                        </el-form-item>
                    </el-col>
                </el-row>

                <!-- 操作按钮 -->
                <div class="form-actions">
                    <el-button
                        v-if="type !== 'info'"
                        type="primary"
                        @click="onSubmit"
                        :loading="loading">
                        <i class="el-icon-check"></i>
                        {{ !ruleForm.id ? '新增公告' : '保存修改' }}
                    </el-button>
                    <el-button @click="back()">
                        <i class="el-icon-back"></i>
                        {{ type === 'info' ? '返回' : '取消' }}
                    </el-button>
                </div>
            </el-form>
        </el-card>
    </div>
</template>
<script>
    import styleJs from "../../../utils/style.js";
    // 数字，邮件，手机，url，身份证校验
    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from "@/utils/validate";
    export default {
        data() {
            return {
                addEditForm:null,
                id: '',
                type: '',
                sessionTable : "",//登录账户所在表名
                role : "",//权限
                loading: false,
                ro:{
                    gonggaoName: false,
                    gonggaoPhoto: false,
                    gonggaoTypes: false,
                    insertTime: false,
                    gonggaoContent: false,
                },
                ruleForm: {
                    gonggaoName: '',
                    gonggaoPhoto: '',
                    gonggaoTypes: '',
                    insertTime: '',
                    gonggaoContent: '',
                },
                gonggaoTypesOptions : [],
                rules: {
                   gonggaoName: [
                              { required: true, message: '公告名称不能为空', trigger: 'blur' },
                          ],
                   gonggaoPhoto: [
                              { required: true, message: '公告图片不能为空', trigger: 'blur' },
                          ],
                   gonggaoTypes: [
                              { required: true, message: '公告类型不能为空', trigger: 'blur' },
                              {  pattern: /^[1-9][0-9]*$/,
                                  message: '只允许输入整数',
                                  trigger: 'blur'
                              }
                          ],
                   insertTime: [
                              { required: true, message: '公告发布时间不能为空', trigger: 'blur' },
                          ],
                   gonggaoContent: [
                              { required: true, message: '公告详情不能为空', trigger: 'blur' },
                          ],
                }
            };
        },
        props: ["parent"],
        computed: {
        },
        created() {
            //获取当前登录用户的信息
            this.sessionTable = this.$storage.get("sessionTable");
            this.role = this.$storage.get("role");

            if (this.role != "管理员"){
            }
            this.addEditForm = styleJs.addStyle();
            this.addEditStyleChange()
            this.addEditUploadStyleChange()
            //获取下拉框信息
                this.$http({
                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=gonggao_types`,
                    method: "get"
                }).then(({ data }) => {
                    if (data && data.code === 0) {
                        this.gonggaoTypesOptions = data.data.list;
                    }
                });


        },
        mounted() {
        },
        methods: {
            // 下载
            download(file){
                window.open(`${file}`)
            },
            // 初始化
            init(id,type) {
                if (id) {
                    this.id = id;
                    this.type = type;
                }
                if(this.type=='info'||this.type=='else'){
                    this.info(id);
                }else if(this.type=='cross'){
                    var obj = this.$storage.getObj('crossObj');
                    for (var o in obj){

                      if(o=='gonggaoName'){
                          this.ruleForm.gonggaoName = obj[o];
                          this.ro.gonggaoName = true;
                          continue;
                      }
                      if(o=='gonggaoPhoto'){
                          this.ruleForm.gonggaoPhoto = obj[o];
                          this.ro.gonggaoPhoto = true;
                          continue;
                      }
                      if(o=='gonggaoTypes'){
                          this.ruleForm.gonggaoTypes = obj[o];
                          this.ro.gonggaoTypes = true;
                          continue;
                      }
                      if(o=='insertTime'){
                          this.ruleForm.insertTime = obj[o];
                          this.ro.insertTime = true;
                          continue;
                      }
                      if(o=='gonggaoContent'){
                          this.ruleForm.gonggaoContent = obj[o];
                          this.ro.gonggaoContent = true;
                          continue;
                      }
                    }
                }
                // 获取用户信息
                this.$http({
                    url:`${this.$storage.get("sessionTable")}/session`,
                    method: "get"
                }).then(({ data }) => {
                    if (data && data.code === 0) {
                        var json = data.data;
                    } else {
                        this.$message.error(data.msg);
                    }
                });
            },
            // 多级联动参数
            info(id) {
                this.$http({
                    url: `gonggao/info/${id}`,
                    method: 'get'
                }).then(({ data }) => {
                    if (data && data.code === 0) {
                        this.ruleForm = data.data;
                        //解决前台上传图片后台不显示的问题
                        let reg=new RegExp('../../../upload','g')//g代表全部
                    } else {
                        this.$message.error(data.msg);
                    }
                });
            },
            // 图片预览
            previewImage(url) {
                this.$alert(`<img src="${url}" style="width: 100%; max-width: 500px;">`, '图片预览', {
                    dangerouslyUseHTMLString: true,
                    showConfirmButton: false,
                    showCancelButton: true,
                    cancelButtonText: '关闭'
                });
            },

            // 提交
            onSubmit() {
                this.$refs["ruleForm"].validate(valid => {
                    if (valid) {
                        this.loading = true;
                        this.$http({
                            url:`gonggao/${!this.ruleForm.id ? "save" : "update"}`,
                            method: "post",
                            data: this.ruleForm
                        }).then(({ data }) => {
                            this.loading = false;
                            if (data && data.code === 0) {
                                this.$message({
                                    message: "操作成功",
                                    type: "success",
                                    duration: 1500,
                                    onClose: () => {
                                        this.parent.showFlag = true;
                                        this.parent.addOrUpdateFlag = false;
                                        this.parent.gonggaoCrossAddOrUpdateFlag = false;
                                        this.parent.search();
                                        this.parent.contentStyleChange();
                                    }
                                });
                            } else {
                                this.$message.error(data.msg);
                            }
                        }).catch(() => {
                            this.loading = false;
                        });
                    }
                });
            },
            // 获取uuid
            getUUID () {
                return new Date().getTime();
            },
            // 返回
            back() {
                this.parent.showFlag = true;
                this.parent.addOrUpdateFlag = false;
                this.parent.gonggaoCrossAddOrUpdateFlag = false;
                this.parent.contentStyleChange();
            },
            //图片
            gonggaoPhotoUploadChange(fileUrls){
                this.ruleForm.gonggaoPhoto = fileUrls;
                this.addEditUploadStyleChange()
            },

            addEditStyleChange() {
                this.$nextTick(()=>{
                    // input
                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{
                        el.style.height = this.addEditForm.inputHeight
                        el.style.color = this.addEditForm.inputFontColor
                        el.style.fontSize = this.addEditForm.inputFontSize
                        el.style.borderWidth = this.addEditForm.inputBorderWidth
                        el.style.borderStyle = this.addEditForm.inputBorderStyle
                        el.style.borderColor = this.addEditForm.inputBorderColor
                        el.style.borderRadius = this.addEditForm.inputBorderRadius
                        el.style.backgroundColor = this.addEditForm.inputBgColor
                    })
                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{
                        el.style.lineHeight = this.addEditForm.inputHeight
                        el.style.color = this.addEditForm.inputLableColor
                        el.style.fontSize = this.addEditForm.inputLableFontSize
                    })
                    // select
                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{
                        el.style.height = this.addEditForm.selectHeight
                        el.style.color = this.addEditForm.selectFontColor
                        el.style.fontSize = this.addEditForm.selectFontSize
                        el.style.borderWidth = this.addEditForm.selectBorderWidth
                        el.style.borderStyle = this.addEditForm.selectBorderStyle
                        el.style.borderColor = this.addEditForm.selectBorderColor
                        el.style.borderRadius = this.addEditForm.selectBorderRadius
                        el.style.backgroundColor = this.addEditForm.selectBgColor
                    })
                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{
                        el.style.lineHeight = this.addEditForm.selectHeight
                        el.style.color = this.addEditForm.selectLableColor
                        el.style.fontSize = this.addEditForm.selectLableFontSize
                    })
                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{
                        el.style.color = this.addEditForm.selectIconFontColor
                        el.style.fontSize = this.addEditForm.selectIconFontSize
                    })
                    // date
                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{
                        el.style.height = this.addEditForm.dateHeight
                        el.style.color = this.addEditForm.dateFontColor
                        el.style.fontSize = this.addEditForm.dateFontSize
                        el.style.borderWidth = this.addEditForm.dateBorderWidth
                        el.style.borderStyle = this.addEditForm.dateBorderStyle
                        el.style.borderColor = this.addEditForm.dateBorderColor
                        el.style.borderRadius = this.addEditForm.dateBorderRadius
                        el.style.backgroundColor = this.addEditForm.dateBgColor
                    })
                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{
                        el.style.lineHeight = this.addEditForm.dateHeight
                        el.style.color = this.addEditForm.dateLableColor
                        el.style.fontSize = this.addEditForm.dateLableFontSize
                    })
                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{
                        el.style.color = this.addEditForm.dateIconFontColor
                        el.style.fontSize = this.addEditForm.dateIconFontSize
                        el.style.lineHeight = this.addEditForm.dateHeight
                    })
                    // upload
                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'
                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{
                        el.style.width = this.addEditForm.uploadHeight
                        el.style.height = this.addEditForm.uploadHeight
                        el.style.borderWidth = this.addEditForm.uploadBorderWidth
                        el.style.borderStyle = this.addEditForm.uploadBorderStyle
                        el.style.borderColor = this.addEditForm.uploadBorderColor
                        el.style.borderRadius = this.addEditForm.uploadBorderRadius
                        el.style.backgroundColor = this.addEditForm.uploadBgColor
                    })
                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{
                        el.style.lineHeight = this.addEditForm.uploadHeight
                        el.style.color = this.addEditForm.uploadLableColor
                        el.style.fontSize = this.addEditForm.uploadLableFontSize
                    })
                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{
                        el.style.color = this.addEditForm.uploadIconFontColor
                        el.style.fontSize = this.addEditForm.uploadIconFontSize
                        el.style.lineHeight = iconLineHeight
                        el.style.display = 'block'
                    })
                    // 多文本输入框
                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{
                        el.style.height = this.addEditForm.textareaHeight
                        el.style.color = this.addEditForm.textareaFontColor
                        el.style.fontSize = this.addEditForm.textareaFontSize
                        el.style.borderWidth = this.addEditForm.textareaBorderWidth
                        el.style.borderStyle = this.addEditForm.textareaBorderStyle
                        el.style.borderColor = this.addEditForm.textareaBorderColor
                        el.style.borderRadius = this.addEditForm.textareaBorderRadius
                        el.style.backgroundColor = this.addEditForm.textareaBgColor
                    })
                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{
                        // el.style.lineHeight = this.addEditForm.textareaHeight
                        el.style.color = this.addEditForm.textareaLableColor
                        el.style.fontSize = this.addEditForm.textareaLableFontSize
                    })
                    // 保存
                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{
                        el.style.width = this.addEditForm.btnSaveWidth
                        el.style.height = this.addEditForm.btnSaveHeight
                        el.style.color = this.addEditForm.btnSaveFontColor
                        el.style.fontSize = this.addEditForm.btnSaveFontSize
                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth
                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle
                        el.style.borderColor = this.addEditForm.btnSaveBorderColor
                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius
                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor
                    })
                    // 返回
                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{
                        el.style.width = this.addEditForm.btnCancelWidth
                        el.style.height = this.addEditForm.btnCancelHeight
                        el.style.color = this.addEditForm.btnCancelFontColor
                        el.style.fontSize = this.addEditForm.btnCancelFontSize
                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth
                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle
                        el.style.borderColor = this.addEditForm.btnCancelBorderColor
                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius
                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor
                    })
                })
            },
            addEditUploadStyleChange() {
                this.$nextTick(()=>{
                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{
                        el.style.width = this.addEditForm.uploadHeight
                        el.style.height = this.addEditForm.uploadHeight
                        el.style.borderWidth = this.addEditForm.uploadBorderWidth
                        el.style.borderStyle = this.addEditForm.uploadBorderStyle
                        el.style.borderColor = this.addEditForm.uploadBorderColor
                        el.style.borderRadius = this.addEditForm.uploadBorderRadius
                        el.style.backgroundColor = this.addEditForm.uploadBgColor
                    })
                })
            },
        }
    };
</script>
<style lang="scss" scoped>
.gonggao-form-container {
  padding: 20px;

  .form-header {
    text-align: center;
    margin-bottom: 30px;

    h3 {
      font-size: 24px;
      color: #2c3e50;
      margin: 0 0 10px 0;
    }

    p {
      font-size: 14px;
      color: #909399;
      margin: 0;
    }
  }

  .form-card {
    ::v-deep .el-card__body {
      padding: 30px;
    }

    .gonggao-form {
      .section-title {
        font-size: 16px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #00c292;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 50px;
          height: 2px;
          background: #00c292;
        }
      }

      .el-form-item {
        margin-bottom: 25px;

        ::v-deep .el-form-item__label {
          font-weight: 600;
          color: #2c3e50;
        }

        ::v-deep .el-input__inner {
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;

          &:focus {
            border-color: #00c292;
            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);
          }
        }

        ::v-deep .el-select {
          width: 100%;
        }
      }

      .photo-preview {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;

        .preview-image {
          width: 120px;
          height: 120px;
          object-fit: cover;
          border-radius: 8px;
          cursor: pointer;
          border: 2px solid #dcdfe6;
          transition: all 0.3s ease;

          &:hover {
            border-color: #00c292;
            transform: scale(1.05);
          }
        }
      }

      .no-image, .no-content {
        color: #909399;
        font-style: italic;
        text-align: center;
        padding: 20px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px dashed #dcdfe6;
      }

      .content-preview {
        padding: 15px;
        background: #f8f9fa;
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        min-height: 100px;

        ::v-deep img {
          max-width: 100%;
          height: auto;
        }
      }
    }

    .form-actions {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid #ebeef5;

      .el-button {
        border-radius: 6px;
        padding: 12px 30px;
        font-weight: 600;
        margin: 0 10px;

        &.el-button--primary {
          background: linear-gradient(135deg, #00c292 0%, #00a085 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #00a085 0%, #008f75 100%);
          }
        }
      }
    }
  }
}

.editor {
  height: 400px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #dcdfe6;

  ::v-deep .ql-container {
    height: 310px;
  }

  ::v-deep .ql-toolbar {
    border-bottom: 1px solid #dcdfe6;
  }
}

::v-deep .el-upload {
  .el-upload-dragger {
    border-radius: 8px;
    border: 2px dashed #d9d9d9;
    transition: all 0.3s ease;

    &:hover {
      border-color: #00c292;
    }
  }
}
</style>



