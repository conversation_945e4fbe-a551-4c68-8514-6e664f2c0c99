<template>
  <div class="quick-actions">
    <div class="action-item" v-for="(action, index) in actions" :key="index" @click="handleAction(action)">
      <div class="action-icon">
        <i :class="action.icon"></i>
      </div>
      <div class="action-content">
        <div class="action-title">{{ action.title }}</div>
        <div class="action-desc">{{ action.description }}</div>
      </div>
      <div class="action-arrow">
        <i class="el-icon-arrow-right"></i>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  data() {
    return {
      actions: [
        {
          title: '场地管理',
          description: '管理体育场地信息',
          icon: 'el-icon-office-building',
          route: '/changdi'
        },
        {
          title: '预约管理',
          description: '查看和管理预约订单',
          icon: 'el-icon-date',
          route: '/changdiOrder'
        },
        {
          title: '用户管理',
          description: '管理系统用户',
          icon: 'el-icon-user',
          route: '/yonghu'
        },
        {
          title: '公告管理',
          description: '发布和管理公告',
          icon: 'el-icon-bell',
          route: '/gonggao'
        },
        {
          title: '论坛管理',
          description: '管理论坛内容',
          icon: 'el-icon-chat-dot-round',
          route: '/forum'
        }
      ]
    }
  },
  methods: {
    handleAction(action) {
      this.$router.push(action.route)
    }
  }
};
</script>
<style lang="scss" scoped>
.quick-actions {
  .action-item {
    display: flex;
    align-items: center;
    padding: 15px;
    margin-bottom: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      background: #f8f9fa;
    }

    &:last-child {
      margin-bottom: 0;
    }

    .action-icon {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #00c292 0%, #00a085 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;

      i {
        font-size: 18px;
        color: white;
      }
    }

    .action-content {
      flex: 1;

      .action-title {
        font-size: 16px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 4px;
      }

      .action-desc {
        font-size: 12px;
        color: #909399;
      }
    }

    .action-arrow {
      color: #c0c4cc;
      font-size: 14px;
    }
  }
}
</style>

