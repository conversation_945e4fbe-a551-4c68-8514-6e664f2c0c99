{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue?vue&type=style&index=0&id=563fbaf7&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\forum.vue", "mtime": 1750601879409}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5mb3J1bS1jb250YWluZXIgewogIG1heC13aWR0aDogMTIwMHB4OwogIG1hcmdpbjogMCBhdXRvOwogIHBhZGRpbmc6IDIwcHg7CiAgYmFja2dyb3VuZDogI2Y1ZjdmYTsKICBtaW4taGVpZ2h0OiAxMDB2aDsKfQoKLy8g6K665Z2b5aS06YOoCi5mb3J1bS1oZWFkZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSAwJSwgIzc2NGJhMiAxMDAlKTsKICBjb2xvcjogd2hpdGU7CiAgcGFkZGluZzogMzBweDsKICBib3JkZXItcmFkaXVzOiAxMnB4OwogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgYm94LXNoYWRvdzogMCA0cHggMjBweCByZ2JhKDAsIDAsIDAsIDAuMSk7CgogIC5oZWFkZXItY29udGVudCB7CiAgICAuZm9ydW0tdGl0bGUgewogICAgICBmb250LXNpemU6IDI4cHg7CiAgICAgIG1hcmdpbjogMCAwIDhweCAwOwogICAgICBmb250LXdlaWdodDogNjAwOwoKICAgICAgaSB7CiAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMHB4OwogICAgICAgIGNvbG9yOiAjZmZkNzAwOwogICAgICB9CiAgICB9CgogICAgLmZvcnVtLXN1YnRpdGxlIHsKICAgICAgZm9udC1zaXplOiAxNnB4OwogICAgICBtYXJnaW46IDA7CiAgICAgIG9wYWNpdHk6IDAuOTsKICAgIH0KICB9CgogIC5uZXctcG9zdC1idG4gewogICAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpOwogICAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpOwogICAgY29sb3I6IHdoaXRlOwoKICAgICY6aG92ZXIgewogICAgICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMyk7CiAgICB9CiAgfQp9CgovLyDorrrlnZvnu5/orqEKLmZvcnVtLXN0YXRzIHsKICBkaXNwbGF5OiBncmlkOwogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMjAwcHgsIDFmcikpOwogIGdhcDogMjBweDsKICBtYXJnaW4tYm90dG9tOiAzMHB4OwoKICAuc3RhdC1pdGVtIHsKICAgIGJhY2tncm91bmQ6IHdoaXRlOwogICAgcGFkZGluZzogMjVweDsKICAgIGJvcmRlci1yYWRpdXM6IDEycHg7CiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7CiAgICBib3gtc2hhZG93OiAwIDJweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTsKICAgIHRyYW5zaXRpb246IHRyYW5zZm9ybSAwLjNzIGVhc2U7CgogICAgJjpob3ZlciB7CiAgICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICAgIH0KCiAgICAuc3RhdC1udW1iZXIgewogICAgICBmb250LXNpemU6IDMycHg7CiAgICAgIGZvbnQtd2VpZ2h0OiBib2xkOwogICAgICBjb2xvcjogIzQwOWVmZjsKICAgICAgbWFyZ2luLWJvdHRvbTogOHB4OwogICAgfQoKICAgIC5zdGF0LWxhYmVsIHsKICAgICAgY29sb3I6ICM2NjY7CiAgICAgIGZvbnQtc2l6ZTogMTRweDsKICAgIH0KICB9Cn0KCi8vIOaQnOe0ouWSjOetm+mAiQouZm9ydW0tZmlsdGVycyB7CiAgZGlzcGxheTogZmxleDsKICBnYXA6IDE1cHg7CiAgbWFyZ2luLWJvdHRvbTogMjVweDsKCiAgLnNlYXJjaC1pbnB1dCB7CiAgICBmbGV4OiAxOwogICAgbWF4LXdpZHRoOiA0MDBweDsKICB9Cn0KCi8vIOW4luWtkOWuueWZqAoucG9zdHMtY29udGFpbmVyIHsKICBkaXNwbGF5OiBncmlkOwogIGdhcDogMjBweDsKICBtYXJnaW4tYm90dG9tOiAzMHB4Owp9CgovLyDluJblrZDljaHniYcKLnBvc3QtY2FyZCB7CiAgYmFja2dyb3VuZDogd2hpdGU7CiAgYm9yZGVyLXJhZGl1czogMTJweDsKICBwYWRkaW5nOiAyNXB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpOwogIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7CiAgY3Vyc29yOiBwb2ludGVyOwoKICAmOmhvdmVyIHsKICAgIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtMnB4KTsKICAgIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTsKICB9CgogIC5wb3N0LWhlYWRlciB7CiAgICBkaXNwbGF5OiBmbGV4OwogICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CgogICAgLnBvc3QtdGltZSB7CiAgICAgIGNvbG9yOiAjOTk5OwogICAgICBmb250LXNpemU6IDE0cHg7CiAgICB9CiAgfQoKICAucG9zdC10aXRsZSB7CiAgICBmb250LXNpemU6IDIwcHg7CiAgICBmb250LXdlaWdodDogNjAwOwogICAgY29sb3I6ICMyYzNlNTA7CiAgICBtYXJnaW46IDAgMCAxNXB4IDA7CiAgICBsaW5lLWhlaWdodDogMS40OwogIH0KCiAgLnBvc3QtY29udGVudCB7CiAgICBjb2xvcjogIzY2NjsKICAgIGxpbmUtaGVpZ2h0OiAxLjY7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwogIH0KCiAgLnBvc3QtZm9vdGVyIHsKICAgIGRpc3BsYXk6IGZsZXg7CiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgICBhbGlnbi1pdGVtczogY2VudGVyOwoKICAgIC5wb3N0LWF1dGhvciB7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGNvbG9yOiAjNjY2OwogICAgICBmb250LXNpemU6IDE0cHg7CgogICAgICBpIHsKICAgICAgICBtYXJnaW4tcmlnaHQ6IDVweDsKICAgICAgfQogICAgfQoKICAgIC5wb3N0LXN0YXRzIHsKICAgICAgLnJlcGx5LWNvdW50IHsKICAgICAgICBjb2xvcjogIzQwOWVmZjsKICAgICAgICBmb250LXNpemU6IDE0cHg7CgogICAgICAgIGkgewogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgICAgICAgfQogICAgICB9CiAgICB9CiAgfQp9CgovLyDnsbvlnovmoIfnrb4KLnBvc3QtdHlwZS10YWcgewogIHBhZGRpbmc6IDRweCAxMnB4OwogIGJvcmRlci1yYWRpdXM6IDIwcHg7CiAgZm9udC1zaXplOiAxMnB4OwogIGZvbnQtd2VpZ2h0OiA1MDA7CgogICYudHlwZS1zcG9ydCB7CiAgICBiYWNrZ3JvdW5kOiAjZThmNWU4OwogICAgY29sb3I6ICM1MmM0MWE7CiAgfQoKICAmLnR5cGUtZml0bmVzcyB7CiAgICBiYWNrZ3JvdW5kOiAjZmZmMmU4OwogICAgY29sb3I6ICNmYThjMTY7CiAgfQoKICAmLnR5cGUtdmVudWUgewogICAgYmFja2dyb3VuZDogI2U2ZjdmZjsKICAgIGNvbG9yOiAjMTg5MGZmOwogIH0KCiAgJi50eXBlLW90aGVyIHsKICAgIGJhY2tncm91bmQ6ICNmNmY2ZjY7CiAgICBjb2xvcjogIzY2NjsKICB9Cn0KCi8vIOWIhumhtQoucGFnaW5hdGlvbi1jb250YWluZXIgewogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgbWFyZ2luLXRvcDogMzBweDsKfQoKLy8g5a+56K+d5qGG5qC35byPCi5uZXctcG9zdC1kaWFsb2csIC5wb3N0LWRldGFpbC1kaWFsb2cgewogIC5lbC1kaWFsb2dfX2JvZHkgewogICAgcGFkZGluZzogMzBweDsKICB9Cn0KCi8vIOW4luWtkOivpuaDhQoucG9zdC1kZXRhaWwgewogIC5wb3N0LWRldGFpbC1oZWFkZXIgewogICAgZGlzcGxheTogZmxleDsKICAgIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICBwYWRkaW5nLWJvdHRvbTogMjBweDsKICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlOwogICAgbWFyZ2luLWJvdHRvbTogMjBweDsKCiAgICAuYXV0aG9yLWluZm8gewogICAgICBkaXNwbGF5OiBmbGV4OwogICAgICBhbGlnbi1pdGVtczogY2VudGVyOwogICAgICBjb2xvcjogIzY2NjsKCiAgICAgIGkgewogICAgICAgIG1hcmdpbi1yaWdodDogOHB4OwogICAgICB9CgogICAgICAucG9zdC10aW1lIHsKICAgICAgICBtYXJnaW4tbGVmdDogMTVweDsKICAgICAgICBmb250LXNpemU6IDE0cHg7CiAgICAgIH0KICAgIH0KICB9CgogIC5wb3N0LWRldGFpbC1jb250ZW50IHsKICAgIGxpbmUtaGVpZ2h0OiAxLjg7CiAgICBtYXJnaW4tYm90dG9tOiAzMHB4OwogICAgY29sb3I6ICMzMzM7CiAgfQoKICAucmVwbGllcy1zZWN0aW9uIHsKICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZWVlOwogICAgcGFkZGluZy10b3A6IDIwcHg7CiAgICBtYXJnaW4tYm90dG9tOiAyMHB4OwoKICAgIGg0IHsKICAgICAgbWFyZ2luLWJvdHRvbTogMjBweDsKICAgICAgY29sb3I6ICMzMzM7CiAgICB9CgogICAgLnJlcGx5LWl0ZW0gewogICAgICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogICAgICBwYWRkaW5nOiAxNXB4OwogICAgICBib3JkZXItcmFkaXVzOiA4cHg7CiAgICAgIG1hcmdpbi1ib3R0b206IDE1cHg7CgogICAgICAucmVwbHktYXV0aG9yIHsKICAgICAgICBkaXNwbGF5OiBmbGV4OwogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgICAgY29sb3I6ICM2NjY7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIG1hcmdpbi1ib3R0b206IDEwcHg7CgogICAgICAgIGkgewogICAgICAgICAgbWFyZ2luLXJpZ2h0OiA1cHg7CiAgICAgICAgfQoKICAgICAgICAucmVwbHktdGltZSB7CiAgICAgICAgICBtYXJnaW4tbGVmdDogMTBweDsKICAgICAgICB9CiAgICAgIH0KCiAgICAgIC5yZXBseS1jb250ZW50IHsKICAgICAgICBjb2xvcjogIzMzMzsKICAgICAgICBsaW5lLWhlaWdodDogMS42OwogICAgICB9CiAgICB9CiAgfQoKICAucmVwbHktZm9ybSB7CiAgICAucmVwbHktYWN0aW9ucyB7CiAgICAgIG1hcmdpbi10b3A6IDE1cHg7CiAgICAgIHRleHQtYWxpZ246IHJpZ2h0OwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["forum.vue"], "names": [], "mappings": ";AAupBA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "forum.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\n  <div class=\"forum-container\">\n    <!-- 论坛头部 -->\n    <div class=\"forum-header\">\n      <div class=\"header-content\">\n        <h1 class=\"forum-title\">\n          <i class=\"el-icon-chat-dot-round\"></i>\n          体育馆论坛\n        </h1>\n        <p class=\"forum-subtitle\">分享运动心得，交流健身经验</p>\n      </div>\n      <div class=\"header-actions\">\n        <el-button \n          type=\"primary\" \n          icon=\"el-icon-edit\" \n          @click=\"showNewPostDialog = true\"\n          class=\"new-post-btn\">\n          发表新帖\n        </el-button>\n      </div>\n    </div>\n\n    <!-- 论坛统计 -->\n    <div class=\"forum-stats\">\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalPosts }}</div>\n        <div class=\"stat-label\">总帖子数</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ todayPosts }}</div>\n        <div class=\"stat-label\">今日发帖</div>\n      </div>\n      <div class=\"stat-item\">\n        <div class=\"stat-number\">{{ totalReplies }}</div>\n        <div class=\"stat-label\">总回复数</div>\n      </div>\n    </div>\n\n    <!-- 搜索和筛选 -->\n    <div class=\"forum-filters\">\n      <el-input\n        v-model=\"searchKeyword\"\n        placeholder=\"搜索帖子标题或内容...\"\n        prefix-icon=\"el-icon-search\"\n        @input=\"handleSearch\"\n        class=\"search-input\">\n      </el-input>\n      <el-select v-model=\"selectedType\" placeholder=\"帖子类型\" @change=\"handleTypeChange\">\n        <el-option label=\"全部类型\" value=\"\"></el-option>\n        <el-option label=\"运动分享\" value=\"1\"></el-option>\n        <el-option label=\"健身心得\" value=\"2\"></el-option>\n        <el-option label=\"场地推荐\" value=\"3\"></el-option>\n        <el-option label=\"其他讨论\" value=\"4\"></el-option>\n      </el-select>\n    </div>\n\n    <!-- 帖子列表 -->\n    <div class=\"posts-container\">\n      <div \n        v-for=\"post in postList\" \n        :key=\"post.id\" \n        class=\"post-card\"\n        @click=\"viewPost(post)\">\n        <div class=\"post-header\">\n          <div class=\"post-type-tag\" :class=\"getTypeClass(post.forumTypes)\">\n            {{ getTypeName(post.forumTypes) }}\n          </div>\n          <div class=\"post-time\">{{ formatTime(post.insertTime) }}</div>\n        </div>\n        \n        <h3 class=\"post-title\">{{ post.forumName }}</h3>\n        \n        <div class=\"post-content\">\n          {{ getContentPreview(post.forumContent) }}\n        </div>\n        \n        <div class=\"post-footer\">\n          <div class=\"post-author\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"post.yonghuId\">{{ post.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n          </div>\n          <div class=\"post-stats\">\n            <span class=\"reply-count\">\n              <i class=\"el-icon-chat-dot-round\"></i>\n              {{ post.replyCount || 0 }} 回复\n            </span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <div class=\"pagination-container\">\n      <el-pagination\n        @size-change=\"handleSizeChange\"\n        @current-change=\"handleCurrentChange\"\n        :current-page=\"currentPage\"\n        :page-sizes=\"[10, 20, 50]\"\n        :page-size=\"pageSize\"\n        layout=\"total, sizes, prev, pager, next, jumper\"\n        :total=\"totalCount\">\n      </el-pagination>\n    </div>\n\n    <!-- 发帖对话框 -->\n    <el-dialog\n      title=\"发表新帖\"\n      :visible.sync=\"showNewPostDialog\"\n      width=\"800px\"\n      class=\"new-post-dialog\">\n      <el-form :model=\"newPost\" :rules=\"postRules\" ref=\"newPostForm\" label-width=\"80px\">\n        <el-form-item label=\"帖子类型\" prop=\"forumTypes\">\n          <el-select v-model=\"newPost.forumTypes\" placeholder=\"请选择帖子类型\">\n            <el-option label=\"运动分享\" value=\"1\"></el-option>\n            <el-option label=\"健身心得\" value=\"2\"></el-option>\n            <el-option label=\"场地推荐\" value=\"3\"></el-option>\n            <el-option label=\"其他讨论\" value=\"4\"></el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子标题\" prop=\"forumName\">\n          <el-input v-model=\"newPost.forumName\" placeholder=\"请输入帖子标题\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"帖子内容\" prop=\"forumContent\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"newPost.forumContent\"\n            :rows=\"8\"\n            placeholder=\"请输入帖子内容...\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n      \n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"showNewPostDialog = false\">取消</el-button>\n        <el-button type=\"primary\" @click=\"submitPost\" :loading=\"submitting\">发布</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 帖子详情对话框 -->\n    <el-dialog\n      :title=\"currentPost.forumName\"\n      :visible.sync=\"showPostDialog\"\n      width=\"900px\"\n      class=\"post-detail-dialog\">\n      <div class=\"post-detail\">\n        <div class=\"post-detail-header\">\n          <div class=\"author-info\">\n            <i class=\"el-icon-user\"></i>\n            <span v-if=\"currentPost.yonghuId\">{{ currentPost.yonghuName || '用户' }}</span>\n            <span v-else>管理员</span>\n            <span class=\"post-time\">{{ formatTime(currentPost.insertTime) }}</span>\n          </div>\n          <div class=\"post-type-tag\" :class=\"getTypeClass(currentPost.forumTypes)\">\n            {{ getTypeName(currentPost.forumTypes) }}\n          </div>\n        </div>\n        \n        <div class=\"post-detail-content\" v-html=\"currentPost.forumContent\"></div>\n        \n        <!-- 回复列表 -->\n        <div class=\"replies-section\">\n          <h4>回复 ({{ replies.length }})</h4>\n          <div v-for=\"reply in replies\" :key=\"reply.id\" class=\"reply-item\">\n            <div class=\"reply-author\">\n              <i class=\"el-icon-user\"></i>\n              <span v-if=\"reply.yonghuId\">{{ reply.yonghuName || '用户' }}</span>\n              <span v-else>管理员</span>\n              <span class=\"reply-time\">{{ formatTime(reply.insertTime) }}</span>\n            </div>\n            <div class=\"reply-content\">{{ reply.forumContent }}</div>\n          </div>\n        </div>\n        \n        <!-- 回复表单 -->\n        <div class=\"reply-form\">\n          <el-input\n            type=\"textarea\"\n            v-model=\"replyContent\"\n            :rows=\"3\"\n            placeholder=\"写下你的回复...\">\n          </el-input>\n          <div class=\"reply-actions\">\n            <el-button type=\"primary\" @click=\"submitReply\" :loading=\"replying\">回复</el-button>\n          </div>\n        </div>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'Forum',\n  data() {\n    return {\n      // 帖子列表\n      postList: [],\n      totalCount: 0,\n      currentPage: 1,\n      pageSize: 10,\n      \n      // 搜索和筛选\n      searchKeyword: '',\n      selectedType: '',\n      \n      // 统计数据\n      totalPosts: 0,\n      todayPosts: 0,\n      totalReplies: 0,\n      \n      // 发帖\n      showNewPostDialog: false,\n      newPost: {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      },\n      submitting: false,\n      \n      // 帖子详情\n      showPostDialog: false,\n      currentPost: {},\n      replies: [],\n      replyContent: '',\n      replying: false,\n      \n      // 表单验证\n      postRules: {\n        forumTypes: [\n          { required: true, message: '请选择帖子类型', trigger: 'change' }\n        ],\n        forumName: [\n          { required: true, message: '请输入帖子标题', trigger: 'blur' },\n          { min: 5, max: 100, message: '标题长度在 5 到 100 个字符', trigger: 'blur' }\n        ],\n        forumContent: [\n          { required: true, message: '请输入帖子内容', trigger: 'blur' },\n          { min: 10, message: '内容至少 10 个字符', trigger: 'blur' }\n        ]\n      }\n    }\n  },\n  \n  mounted() {\n    // 检查是否有本地存储的帖子数据\n    this.loadLocalPosts()\n    this.loadPosts()\n    this.loadStats()\n  },\n  \n  methods: {\n    // 加载本地存储的帖子\n    loadLocalPosts() {\n      const localPosts = localStorage.getItem('forum_posts')\n      if (localPosts) {\n        try {\n          const posts = JSON.parse(localPosts)\n          this.postList = posts\n          this.totalCount = posts.length\n          this.totalPosts = posts.length\n\n          // 计算今日发帖数\n          const today = new Date().toISOString().split('T')[0]\n          this.todayPosts = posts.filter(post =>\n            post.insertTime && post.insertTime.startsWith(today)\n          ).length\n\n          // 计算回复数（这里简化处理）\n          this.totalReplies = posts.filter(post => post.superIds).length\n\n          console.log('从本地存储加载了', posts.length, '个帖子')\n          return\n        } catch (e) {\n          console.error('解析本地帖子数据失败:', e)\n        }\n      }\n\n      // 如果没有本地数据，创建一些示例数据\n      this.createSamplePosts()\n    },\n\n    // 创建示例帖子数据\n    createSamplePosts() {\n      const samplePosts = [\n        {\n          id: 1,\n          forumName: '欢迎来到体育馆论坛！',\n          forumContent: '这里是大家交流运动心得、分享健身经验的地方。欢迎大家积极参与讨论！',\n          forumTypes: 4,\n          forumStateTypes: 1,\n          yonghuId: 1,\n          yonghuName: '管理员',\n          insertTime: new Date().toISOString(),\n          createTime: new Date().toISOString()\n        },\n        {\n          id: 2,\n          forumName: '篮球场地推荐',\n          forumContent: '推荐几个不错的篮球场地，设施齐全，环境优美，适合各种水平的球友。',\n          forumTypes: 3,\n          forumStateTypes: 1,\n          yonghuId: 2,\n          yonghuName: '篮球爱好者',\n          insertTime: new Date(Date.now() - 3600000).toISOString(),\n          createTime: new Date(Date.now() - 3600000).toISOString()\n        },\n        {\n          id: 3,\n          forumName: '健身房训练心得',\n          forumContent: '分享一些健身房训练的心得体会，包括器械使用技巧和训练计划安排。',\n          forumTypes: 2,\n          forumStateTypes: 1,\n          yonghuId: 3,\n          yonghuName: '健身达人',\n          insertTime: new Date(Date.now() - 7200000).toISOString(),\n          createTime: new Date(Date.now() - 7200000).toISOString()\n        }\n      ]\n\n      this.postList = samplePosts\n      this.totalCount = samplePosts.length\n      this.totalPosts = samplePosts.length\n      this.todayPosts = 1\n      this.totalReplies = 0\n\n      // 保存到本地存储\n      this.savePostsToLocal()\n\n      console.log('创建了示例帖子数据')\n    },\n\n    // 保存帖子到本地存储\n    savePostsToLocal() {\n      try {\n        localStorage.setItem('forum_posts', JSON.stringify(this.postList))\n      } catch (e) {\n        console.error('保存帖子到本地存储失败:', e)\n      }\n    },\n\n    // 加载帖子列表\n    loadPosts() {\n      const params = {\n        page: this.currentPage,\n        limit: this.pageSize,\n        forumStateTypes: 1 // 只显示已审核的帖子\n      }\n\n      if (this.searchKeyword) {\n        params.forumName = this.searchKeyword\n      }\n\n      if (this.selectedType) {\n        params.forumTypes = this.selectedType\n      }\n\n      console.log('加载帖子参数:', params)\n\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params\n      }).then(({ data }) => {\n        console.log('帖子列表响应:', data)\n        if (data && data.code === 0) {\n          this.postList = data.data.list || []\n          this.totalCount = data.data.total || 0\n        } else {\n          console.error('获取帖子列表失败:', data)\n          this.$message.error(data.msg || '获取帖子列表失败')\n        }\n      }).catch((error) => {\n        console.error('加载帖子网络错误:', error)\n        // 后端不可用时，不显示错误消息，使用本地数据\n        console.log('使用本地存储的帖子数据')\n      })\n    },\n    \n    // 加载统计数据\n    loadStats() {\n      // 获取总帖子数\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalPosts = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取总帖子数失败')\n      })\n\n      // 获取今日发帖数\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, insertTime: today }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.todayPosts = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取今日发帖数失败')\n      })\n\n      // 获取回复数\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: { page: 1, limit: 1, forumStateTypes: 2 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.totalReplies = data.data.total || 0\n        }\n      }).catch(() => {\n        console.log('获取回复数失败')\n      })\n    },\n\n    // 搜索处理\n    handleSearch() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 类型筛选\n    handleTypeChange() {\n      this.currentPage = 1\n      this.loadPosts()\n    },\n\n    // 分页处理\n    handleSizeChange(val) {\n      this.pageSize = val\n      this.loadPosts()\n    },\n\n    handleCurrentChange(val) {\n      this.currentPage = val\n      this.loadPosts()\n    },\n\n    // 发帖\n    submitPost() {\n      this.$refs.newPostForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n\n          const userId = this.$storage.get('userId')\n          const sessionTable = this.$storage.get('sessionTable')\n\n          console.log('发帖数据准备:', {\n            userId,\n            sessionTable,\n            newPost: this.newPost\n          })\n\n          const postData = {\n            forumName: this.newPost.forumName,\n            forumContent: this.newPost.forumContent,\n            forumTypes: parseInt(this.newPost.forumTypes),\n            forumStateTypes: 1, // 直接审核通过\n            yonghuId: userId\n          }\n\n          console.log('提交的帖子数据:', postData)\n\n          this.$http({\n            url: 'forum/save',\n            method: 'post',\n            data: postData\n          }).then(({ data }) => {\n            console.log('发帖响应:', data)\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('发帖成功！')\n              this.showNewPostDialog = false\n              this.resetNewPost()\n              this.loadPosts()\n              this.loadStats()\n            } else {\n              this.$message.error(data.msg || '发帖失败，请稍后重试')\n              console.error('发帖失败详情:', data)\n            }\n          }).catch((error) => {\n            console.error('发帖请求错误:', error)\n            // 后端不可用时，使用本地存储\n            this.savePostLocally(postData)\n          })\n        }\n      })\n    },\n\n    // 本地保存帖子\n    savePostLocally(postData) {\n      try {\n        // 创建新帖子对象\n        const newPost = {\n          id: Date.now(), // 使用时间戳作为ID\n          forumName: postData.forumName,\n          forumContent: postData.forumContent,\n          forumTypes: postData.forumTypes,\n          forumStateTypes: 1,\n          yonghuId: postData.yonghuId,\n          yonghuName: this.$storage.get('username') || '用户',\n          insertTime: new Date().toISOString(),\n          createTime: new Date().toISOString()\n        }\n\n        // 添加到帖子列表\n        this.postList.unshift(newPost)\n        this.totalCount = this.postList.length\n        this.totalPosts = this.postList.length\n        this.todayPosts += 1\n\n        // 保存到本地存储\n        this.savePostsToLocal()\n\n        this.submitting = false\n        this.$message.success('发帖成功！（已保存到本地）')\n        this.showNewPostDialog = false\n        this.resetNewPost()\n\n        console.log('帖子已保存到本地存储')\n      } catch (error) {\n        console.error('本地保存帖子失败:', error)\n        this.submitting = false\n        this.$message.error('发帖失败')\n      }\n    },\n\n    // 重置发帖表单\n    resetNewPost() {\n      this.newPost = {\n        forumName: '',\n        forumContent: '',\n        forumTypes: ''\n      }\n      this.$refs.newPostForm && this.$refs.newPostForm.resetFields()\n    },\n\n    // 查看帖子详情\n    viewPost(post) {\n      this.currentPost = post\n      this.showPostDialog = true\n      this.loadReplies(post.id)\n    },\n\n    // 加载回复\n    loadReplies(postId) {\n      this.$http({\n        url: 'forum/list',\n        method: 'get',\n        params: {\n          superIds: postId,\n          forumStateTypes: 2\n        }\n      }).then(({ data }) => {\n        console.log('回复列表响应:', data)\n        if (data && data.code === 0) {\n          this.replies = data.data.list || []\n        }\n      }).catch((error) => {\n        console.error('加载回复失败:', error)\n      })\n    },\n\n    // 提交回复\n    submitReply() {\n      if (!this.replyContent.trim()) {\n        this.$message.warning('请输入回复内容')\n        return\n      }\n\n      this.replying = true\n\n      const userId = this.$storage.get('userId')\n\n      const replyData = {\n        forumContent: this.replyContent,\n        superIds: this.currentPost.id,\n        forumStateTypes: 2, // 回复状态\n        yonghuId: userId\n      }\n\n      console.log('提交回复数据:', replyData)\n\n      this.$http({\n        url: 'forum/save',\n        method: 'post',\n        data: replyData\n      }).then(({ data }) => {\n        console.log('回复响应:', data)\n        this.replying = false\n        if (data && data.code === 0) {\n          this.$message.success('回复成功！')\n          this.replyContent = ''\n          this.loadReplies(this.currentPost.id)\n          this.loadStats()\n        } else {\n          this.$message.error(data.msg || '回复失败')\n          console.error('回复失败详情:', data)\n        }\n      }).catch((error) => {\n        console.error('回复请求错误:', error)\n        this.replying = false\n        this.$message.error('网络错误，请稍后重试')\n      })\n    },\n\n    // 获取类型名称\n    getTypeName(type) {\n      const typeMap = {\n        '1': '运动分享',\n        '2': '健身心得',\n        '3': '场地推荐',\n        '4': '其他讨论'\n      }\n      return typeMap[type] || '未分类'\n    },\n\n    // 获取类型样式\n    getTypeClass(type) {\n      const classMap = {\n        '1': 'type-sport',\n        '2': 'type-fitness',\n        '3': 'type-venue',\n        '4': 'type-other'\n      }\n      return classMap[type] || 'type-default'\n    },\n\n    // 格式化时间\n    formatTime(time) {\n      if (!time) return ''\n      const date = new Date(time)\n      const now = new Date()\n      const diff = now - date\n\n      if (diff < 60000) return '刚刚'\n      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'\n      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'\n      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'\n\n      return date.toLocaleDateString()\n    },\n\n    // 获取内容预览\n    getContentPreview(content) {\n      if (!content) return ''\n      const text = content.replace(/<[^>]*>/g, '') // 移除HTML标签\n      return text.length > 150 ? text.substring(0, 150) + '...' : text\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.forum-container {\n  max-width: 1200px;\n  margin: 0 auto;\n  padding: 20px;\n  background: #f5f7fa;\n  min-height: 100vh;\n}\n\n// 论坛头部\n.forum-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 30px;\n  border-radius: 12px;\n  margin-bottom: 20px;\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n\n  .header-content {\n    .forum-title {\n      font-size: 28px;\n      margin: 0 0 8px 0;\n      font-weight: 600;\n\n      i {\n        margin-right: 10px;\n        color: #ffd700;\n      }\n    }\n\n    .forum-subtitle {\n      font-size: 16px;\n      margin: 0;\n      opacity: 0.9;\n    }\n  }\n\n  .new-post-btn {\n    background: rgba(255, 255, 255, 0.2);\n    border: 1px solid rgba(255, 255, 255, 0.3);\n    color: white;\n\n    &:hover {\n      background: rgba(255, 255, 255, 0.3);\n    }\n  }\n}\n\n// 论坛统计\n.forum-stats {\n  display: grid;\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\n  gap: 20px;\n  margin-bottom: 30px;\n\n  .stat-item {\n    background: white;\n    padding: 25px;\n    border-radius: 12px;\n    text-align: center;\n    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n    transition: transform 0.3s ease;\n\n    &:hover {\n      transform: translateY(-2px);\n    }\n\n    .stat-number {\n      font-size: 32px;\n      font-weight: bold;\n      color: #409eff;\n      margin-bottom: 8px;\n    }\n\n    .stat-label {\n      color: #666;\n      font-size: 14px;\n    }\n  }\n}\n\n// 搜索和筛选\n.forum-filters {\n  display: flex;\n  gap: 15px;\n  margin-bottom: 25px;\n\n  .search-input {\n    flex: 1;\n    max-width: 400px;\n  }\n}\n\n// 帖子容器\n.posts-container {\n  display: grid;\n  gap: 20px;\n  margin-bottom: 30px;\n}\n\n// 帖子卡片\n.post-card {\n  background: white;\n  border-radius: 12px;\n  padding: 25px;\n  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  cursor: pointer;\n\n  &:hover {\n    transform: translateY(-2px);\n    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n  }\n\n  .post-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 15px;\n\n    .post-time {\n      color: #999;\n      font-size: 14px;\n    }\n  }\n\n  .post-title {\n    font-size: 20px;\n    font-weight: 600;\n    color: #2c3e50;\n    margin: 0 0 15px 0;\n    line-height: 1.4;\n  }\n\n  .post-content {\n    color: #666;\n    line-height: 1.6;\n    margin-bottom: 20px;\n  }\n\n  .post-footer {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n\n    .post-author {\n      display: flex;\n      align-items: center;\n      color: #666;\n      font-size: 14px;\n\n      i {\n        margin-right: 5px;\n      }\n    }\n\n    .post-stats {\n      .reply-count {\n        color: #409eff;\n        font-size: 14px;\n\n        i {\n          margin-right: 5px;\n        }\n      }\n    }\n  }\n}\n\n// 类型标签\n.post-type-tag {\n  padding: 4px 12px;\n  border-radius: 20px;\n  font-size: 12px;\n  font-weight: 500;\n\n  &.type-sport {\n    background: #e8f5e8;\n    color: #52c41a;\n  }\n\n  &.type-fitness {\n    background: #fff2e8;\n    color: #fa8c16;\n  }\n\n  &.type-venue {\n    background: #e6f7ff;\n    color: #1890ff;\n  }\n\n  &.type-other {\n    background: #f6f6f6;\n    color: #666;\n  }\n}\n\n// 分页\n.pagination-container {\n  display: flex;\n  justify-content: center;\n  margin-top: 30px;\n}\n\n// 对话框样式\n.new-post-dialog, .post-detail-dialog {\n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n// 帖子详情\n.post-detail {\n  .post-detail-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding-bottom: 20px;\n    border-bottom: 1px solid #eee;\n    margin-bottom: 20px;\n\n    .author-info {\n      display: flex;\n      align-items: center;\n      color: #666;\n\n      i {\n        margin-right: 8px;\n      }\n\n      .post-time {\n        margin-left: 15px;\n        font-size: 14px;\n      }\n    }\n  }\n\n  .post-detail-content {\n    line-height: 1.8;\n    margin-bottom: 30px;\n    color: #333;\n  }\n\n  .replies-section {\n    border-top: 1px solid #eee;\n    padding-top: 20px;\n    margin-bottom: 20px;\n\n    h4 {\n      margin-bottom: 20px;\n      color: #333;\n    }\n\n    .reply-item {\n      background: #f8f9fa;\n      padding: 15px;\n      border-radius: 8px;\n      margin-bottom: 15px;\n\n      .reply-author {\n        display: flex;\n        align-items: center;\n        color: #666;\n        font-size: 14px;\n        margin-bottom: 10px;\n\n        i {\n          margin-right: 5px;\n        }\n\n        .reply-time {\n          margin-left: 10px;\n        }\n      }\n\n      .reply-content {\n        color: #333;\n        line-height: 1.6;\n      }\n    }\n  }\n\n  .reply-form {\n    .reply-actions {\n      margin-top: 15px;\n      text-align: right;\n    }\n  }\n}\n</style>\n"]}]}