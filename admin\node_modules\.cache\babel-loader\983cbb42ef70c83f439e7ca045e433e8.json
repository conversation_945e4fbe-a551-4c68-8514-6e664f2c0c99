{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue", "mtime": 1750593551799}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["data", "_this", "validatePassword", "rule", "value", "callback", "Error", "validateNewPassword", "length", "ruleForm", "password", "repassword", "$refs", "validateField", "validateRePassword", "newpassword", "loading", "user", "passwordStrength", "width", "class", "text", "rules", "validator", "trigger", "mounted", "getUserInfo", "methods", "_this2", "$http", "url", "concat", "$storage", "get", "method", "then", "_ref", "code", "$message", "error", "msg", "catch", "checkPasswordStrength", "score", "feedback", "push", "test", "resetForm", "resetFields", "onUpdateHandler", "_this3", "validate", "valid", "currentPassword", "mima", "$confirm", "confirmButtonText", "cancelButtonText", "type", "updatePassword", "info", "_this4", "updateData", "_objectSpread", "_ref2", "message", "duration", "onClose", "remove", "$router", "replace", "name"], "sources": ["src/views/update-password.vue"], "sourcesContent": ["<template>\r\n  <div class=\"update-password-container\">\r\n    <div class=\"password-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2><i class=\"el-icon-lock\"></i> 修改密码</h2>\r\n          <p>为了您的账户安全，请定期更换密码</p>\r\n        </div>\r\n        <div class=\"security-tips\">\r\n          <el-alert\r\n            title=\"安全提示\"\r\n            type=\"info\"\r\n            :closable=\"false\"\r\n            show-icon>\r\n            <template slot=\"description\">\r\n              <ul>\r\n                <li>密码长度至少6位，建议包含字母、数字和特殊字符</li>\r\n                <li>不要使用过于简单的密码，如123456、password等</li>\r\n                <li>修改密码后需要重新登录</li>\r\n              </ul>\r\n            </template>\r\n          </el-alert>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card class=\"password-card\">\r\n      <div class=\"user-info\">\r\n        <el-avatar :size=\"60\" :src=\"user.yonghuPhoto || user.touxiang\" icon=\"el-icon-user-solid\"></el-avatar>\r\n        <div class=\"user-details\">\r\n          <h3>{{ user.yonghuName || user.username || '用户' }}</h3>\r\n          <p>{{ user.yonghuPhone || user.phone || '暂无手机号' }}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <el-form\r\n        class=\"password-form\"\r\n        ref=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        :model=\"ruleForm\"\r\n        label-width=\"120px\">\r\n\r\n        <el-form-item label=\"当前密码\" prop=\"password\">\r\n          <el-input\r\n            v-model=\"ruleForm.password\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请输入当前密码\"\r\n            prefix-icon=\"el-icon-lock\"\r\n            clearable>\r\n          </el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"新密码\" prop=\"newpassword\">\r\n          <el-input\r\n            v-model=\"ruleForm.newpassword\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请输入新密码\"\r\n            prefix-icon=\"el-icon-key\"\r\n            clearable\r\n            @input=\"checkPasswordStrength\">\r\n          </el-input>\r\n          <div class=\"password-strength\" v-if=\"ruleForm.newpassword\">\r\n            <div class=\"strength-label\">密码强度：</div>\r\n            <div class=\"strength-bar\">\r\n              <div\r\n                class=\"strength-fill\"\r\n                :class=\"passwordStrength.class\"\r\n                :style=\"{width: passwordStrength.width}\">\r\n              </div>\r\n            </div>\r\n            <span class=\"strength-text\" :class=\"passwordStrength.class\">\r\n              {{ passwordStrength.text }}\r\n            </span>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"确认新密码\" prop=\"repassword\">\r\n          <el-input\r\n            v-model=\"ruleForm.repassword\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请再次输入新密码\"\r\n            prefix-icon=\"el-icon-check\"\r\n            clearable>\r\n          </el-input>\r\n        </el-form-item>\r\n\r\n        <div class=\"form-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"onUpdateHandler\"\r\n            :loading=\"loading\"\r\n            size=\"large\">\r\n            <i class=\"el-icon-check\"></i>\r\n            确认修改\r\n          </el-button>\r\n          <el-button\r\n            @click=\"resetForm\"\r\n            size=\"large\">\r\n            <i class=\"el-icon-refresh\"></i>\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    // 自定义验证规则\r\n    const validatePassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请输入当前密码'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const validateNewPassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请输入新密码'));\r\n      } else if (value.length < 6) {\r\n        callback(new Error('密码长度至少6位'));\r\n      } else if (value === this.ruleForm.password) {\r\n        callback(new Error('新密码不能与当前密码相同'));\r\n      } else {\r\n        // 触发确认密码的重新验证\r\n        if (this.ruleForm.repassword !== '') {\r\n          this.$refs.ruleForm.validateField('repassword');\r\n        }\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const validateRePassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请确认新密码'));\r\n      } else if (value !== this.ruleForm.newpassword) {\r\n        callback(new Error('两次输入的密码不一致'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      loading: false,\r\n      ruleForm: {\r\n        password: '',\r\n        newpassword: '',\r\n        repassword: ''\r\n      },\r\n      user: {},\r\n      passwordStrength: {\r\n        width: '0%',\r\n        class: '',\r\n        text: ''\r\n      },\r\n      rules: {\r\n        password: [{ validator: validatePassword, trigger: 'blur' }],\r\n        newpassword: [{ validator: validateNewPassword, trigger: 'blur' }],\r\n        repassword: [{ validator: validateRePassword, trigger: 'blur' }]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getUserInfo();\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getUserInfo() {\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.user = data.data;\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取用户信息失败');\r\n      });\r\n    },\r\n\r\n    // 检查密码强度\r\n    checkPasswordStrength() {\r\n      const password = this.ruleForm.newpassword;\r\n      if (!password) {\r\n        this.passwordStrength = { width: '0%', class: '', text: '' };\r\n        return;\r\n      }\r\n\r\n      let score = 0;\r\n      let feedback = [];\r\n\r\n      // 长度检查\r\n      if (password.length >= 8) score += 25;\r\n      else if (password.length >= 6) score += 15;\r\n      else feedback.push('至少6位');\r\n\r\n      // 包含小写字母\r\n      if (/[a-z]/.test(password)) score += 25;\r\n      else feedback.push('包含小写字母');\r\n\r\n      // 包含大写字母\r\n      if (/[A-Z]/.test(password)) score += 25;\r\n      else feedback.push('包含大写字母');\r\n\r\n      // 包含数字\r\n      if (/\\d/.test(password)) score += 15;\r\n      else feedback.push('包含数字');\r\n\r\n      // 包含特殊字符\r\n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) score += 10;\r\n      else feedback.push('包含特殊字符');\r\n\r\n      // 设置强度显示\r\n      if (score < 30) {\r\n        this.passwordStrength = {\r\n          width: '25%',\r\n          class: 'weak',\r\n          text: '弱'\r\n        };\r\n      } else if (score < 60) {\r\n        this.passwordStrength = {\r\n          width: '50%',\r\n          class: 'medium',\r\n          text: '中等'\r\n        };\r\n      } else if (score < 80) {\r\n        this.passwordStrength = {\r\n          width: '75%',\r\n          class: 'strong',\r\n          text: '强'\r\n        };\r\n      } else {\r\n        this.passwordStrength = {\r\n          width: '100%',\r\n          class: 'very-strong',\r\n          text: '很强'\r\n        };\r\n      }\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$refs.ruleForm.resetFields();\r\n      this.ruleForm = {\r\n        password: '',\r\n        newpassword: '',\r\n        repassword: ''\r\n      };\r\n      this.passwordStrength = { width: '0%', class: '', text: '' };\r\n    },\r\n\r\n    // 修改密码\r\n    onUpdateHandler() {\r\n      this.$refs[\"ruleForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 验证原密码\r\n          const currentPassword = this.user.mima || this.user.password || '';\r\n          if (this.ruleForm.password !== currentPassword) {\r\n            this.$message.error(\"当前密码错误\");\r\n            return;\r\n          }\r\n\r\n          // 确认对话框\r\n          this.$confirm('确定要修改密码吗？修改后需要重新登录。', '确认修改', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.updatePassword();\r\n          }).catch(() => {\r\n            this.$message.info('已取消修改');\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 执行密码更新\r\n    updatePassword() {\r\n      this.loading = true;\r\n\r\n      // 更新用户密码\r\n      const updateData = { ...this.user };\r\n      updateData.password = this.ruleForm.newpassword;\r\n      updateData.mima = this.ruleForm.newpassword;\r\n\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: updateData\r\n      }).then(({ data }) => {\r\n        this.loading = false;\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"密码修改成功！请重新登录\",\r\n            type: \"success\",\r\n            duration: 2000,\r\n            onClose: () => {\r\n              // 清除登录信息并跳转到登录页\r\n              this.$storage.remove(\"Token\");\r\n              this.$storage.remove(\"role\");\r\n              this.$storage.remove(\"sessionTable\");\r\n              this.$storage.remove(\"adminName\");\r\n              this.$router.replace({ name: \"login\" });\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg || '修改密码失败');\r\n        }\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('网络错误，请稍后重试');\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.update-password-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n\r\n  .password-header {\r\n    margin-bottom: 30px;\r\n\r\n    .header-content {\r\n      .title-section {\r\n        text-align: center;\r\n        margin-bottom: 20px;\r\n\r\n        h2 {\r\n          font-size: 28px;\r\n          color: #2c3e50;\r\n          margin: 0 0 10px 0;\r\n\r\n          i {\r\n            color: #00c292;\r\n            margin-right: 10px;\r\n          }\r\n        }\r\n\r\n        p {\r\n          color: #909399;\r\n          margin: 0;\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .security-tips {\r\n        max-width: 600px;\r\n        margin: 0 auto;\r\n\r\n        ::v-deep .el-alert {\r\n          border-radius: 8px;\r\n\r\n          .el-alert__description {\r\n            ul {\r\n              margin: 0;\r\n              padding-left: 20px;\r\n\r\n              li {\r\n                margin: 5px 0;\r\n                color: #606266;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .password-card {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n\r\n    ::v-deep .el-card__body {\r\n      padding: 40px;\r\n    }\r\n\r\n    .user-info {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 40px;\r\n      padding: 20px;\r\n      background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n      border-radius: 8px;\r\n      color: white;\r\n\r\n      .user-details {\r\n        margin-left: 20px;\r\n\r\n        h3 {\r\n          margin: 0 0 5px 0;\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        p {\r\n          margin: 0;\r\n          opacity: 0.9;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .password-form {\r\n      .el-form-item {\r\n        margin-bottom: 30px;\r\n\r\n        ::v-deep .el-form-item__label {\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n          font-size: 16px;\r\n        }\r\n\r\n        ::v-deep .el-input__inner {\r\n          height: 50px;\r\n          line-height: 50px;\r\n          border-radius: 8px;\r\n          border: 2px solid #dcdfe6;\r\n          font-size: 16px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:focus {\r\n            border-color: #00c292;\r\n            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n          }\r\n        }\r\n\r\n        ::v-deep .el-input__prefix {\r\n          left: 15px;\r\n\r\n          .el-input__icon {\r\n            line-height: 50px;\r\n            color: #909399;\r\n          }\r\n        }\r\n      }\r\n\r\n      .password-strength {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n\r\n        .strength-label {\r\n          font-size: 14px;\r\n          color: #606266;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .strength-bar {\r\n          flex: 1;\r\n          height: 6px;\r\n          background: #f0f0f0;\r\n          border-radius: 3px;\r\n          overflow: hidden;\r\n\r\n          .strength-fill {\r\n            height: 100%;\r\n            transition: all 0.3s ease;\r\n            border-radius: 3px;\r\n\r\n            &.weak {\r\n              background: #f56c6c;\r\n            }\r\n\r\n            &.medium {\r\n              background: #e6a23c;\r\n            }\r\n\r\n            &.strong {\r\n              background: #67c23a;\r\n            }\r\n\r\n            &.very-strong {\r\n              background: #00c292;\r\n            }\r\n          }\r\n        }\r\n\r\n        .strength-text {\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          white-space: nowrap;\r\n\r\n          &.weak {\r\n            color: #f56c6c;\r\n          }\r\n\r\n          &.medium {\r\n            color: #e6a23c;\r\n          }\r\n\r\n          &.strong {\r\n            color: #67c23a;\r\n          }\r\n\r\n          &.very-strong {\r\n            color: #00c292;\r\n          }\r\n        }\r\n      }\r\n\r\n      .form-actions {\r\n        text-align: center;\r\n        margin-top: 40px;\r\n        padding-top: 30px;\r\n        border-top: 1px solid #ebeef5;\r\n\r\n        .el-button {\r\n          border-radius: 8px;\r\n          padding: 15px 40px;\r\n          font-weight: 600;\r\n          font-size: 16px;\r\n          margin: 0 10px;\r\n\r\n          &.el-button--primary {\r\n            background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n            border: none;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .update-password-container {\r\n    padding: 10px;\r\n\r\n    .password-card {\r\n      ::v-deep .el-card__body {\r\n        padding: 20px;\r\n      }\r\n\r\n      .user-info {\r\n        flex-direction: column;\r\n        text-align: center;\r\n\r\n        .user-details {\r\n          margin-left: 0;\r\n          margin-top: 15px;\r\n        }\r\n      }\r\n\r\n      .password-form {\r\n        .form-actions {\r\n          .el-button {\r\n            display: block;\r\n            width: 100%;\r\n            margin: 10px 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";;;;;;;AA8GA;EACAA,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;IACA,IAAAC,gBAAA,YAAAA,iBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IAEA,IAAAE,mBAAA,YAAAA,oBAAAJ,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA,WAAAF,KAAA,CAAAI,MAAA;QACAH,QAAA,KAAAC,KAAA;MACA,WAAAF,KAAA,KAAAH,KAAA,CAAAQ,QAAA,CAAAC,QAAA;QACAL,QAAA,KAAAC,KAAA;MACA;QACA;QACA,IAAAL,KAAA,CAAAQ,QAAA,CAAAE,UAAA;UACAV,KAAA,CAAAW,KAAA,CAAAH,QAAA,CAAAI,aAAA;QACA;QACAR,QAAA;MACA;IACA;IAEA,IAAAS,kBAAA,YAAAA,mBAAAX,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACA,KAAAD,KAAA;QACAC,QAAA,KAAAC,KAAA;MACA,WAAAF,KAAA,KAAAH,KAAA,CAAAQ,QAAA,CAAAM,WAAA;QACAV,QAAA,KAAAC,KAAA;MACA;QACAD,QAAA;MACA;IACA;IAEA;MACAW,OAAA;MACAP,QAAA;QACAC,QAAA;QACAK,WAAA;QACAJ,UAAA;MACA;MACAM,IAAA;MACAC,gBAAA;QACAC,KAAA;QACAC,KAAA;QACAC,IAAA;MACA;MACAC,KAAA;QACAZ,QAAA;UAAAa,SAAA,EAAArB,gBAAA;UAAAsB,OAAA;QAAA;QACAT,WAAA;UAAAQ,SAAA,EAAAhB,mBAAA;UAAAiB,OAAA;QAAA;QACAb,UAAA;UAAAY,SAAA,EAAAT,kBAAA;UAAAU,OAAA;QAAA;MACA;IACA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,WAAA;EACA;EACAC,OAAA;IACA;IACAD,WAAA,WAAAA,YAAA;MAAA,IAAAE,MAAA;MACA,KAAAC,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAAC,QAAA,CAAAC,GAAA;QACAC,MAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAApC,IAAA,GAAAoC,IAAA,CAAApC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAqC,IAAA;UACAT,MAAA,CAAAX,IAAA,GAAAjB,IAAA,CAAAA,IAAA;QACA;UACA4B,MAAA,CAAAU,QAAA,CAAAC,KAAA,CAAAvC,IAAA,CAAAwC,GAAA;QACA;MACA,GAAAC,KAAA;QACAb,MAAA,CAAAU,QAAA,CAAAC,KAAA;MACA;IACA;IAEA;IACAG,qBAAA,WAAAA,sBAAA;MACA,IAAAhC,QAAA,QAAAD,QAAA,CAAAM,WAAA;MACA,KAAAL,QAAA;QACA,KAAAQ,gBAAA;UAAAC,KAAA;UAAAC,KAAA;UAAAC,IAAA;QAAA;QACA;MACA;MAEA,IAAAsB,KAAA;MACA,IAAAC,QAAA;;MAEA;MACA,IAAAlC,QAAA,CAAAF,MAAA,OAAAmC,KAAA,YACA,IAAAjC,QAAA,CAAAF,MAAA,OAAAmC,KAAA,YACAC,QAAA,CAAAC,IAAA;;MAEA;MACA,YAAAC,IAAA,CAAApC,QAAA,GAAAiC,KAAA,YACAC,QAAA,CAAAC,IAAA;;MAEA;MACA,YAAAC,IAAA,CAAApC,QAAA,GAAAiC,KAAA,YACAC,QAAA,CAAAC,IAAA;;MAEA;MACA,SAAAC,IAAA,CAAApC,QAAA,GAAAiC,KAAA,YACAC,QAAA,CAAAC,IAAA;;MAEA;MACA,6BAAAC,IAAA,CAAApC,QAAA,GAAAiC,KAAA,YACAC,QAAA,CAAAC,IAAA;;MAEA;MACA,IAAAF,KAAA;QACA,KAAAzB,gBAAA;UACAC,KAAA;UACAC,KAAA;UACAC,IAAA;QACA;MACA,WAAAsB,KAAA;QACA,KAAAzB,gBAAA;UACAC,KAAA;UACAC,KAAA;UACAC,IAAA;QACA;MACA,WAAAsB,KAAA;QACA,KAAAzB,gBAAA;UACAC,KAAA;UACAC,KAAA;UACAC,IAAA;QACA;MACA;QACA,KAAAH,gBAAA;UACAC,KAAA;UACAC,KAAA;UACAC,IAAA;QACA;MACA;IACA;IAEA;IACA0B,SAAA,WAAAA,UAAA;MACA,KAAAnC,KAAA,CAAAH,QAAA,CAAAuC,WAAA;MACA,KAAAvC,QAAA;QACAC,QAAA;QACAK,WAAA;QACAJ,UAAA;MACA;MACA,KAAAO,gBAAA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,IAAA;MAAA;IACA;IAEA;IACA4B,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAAtC,KAAA,aAAAuC,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAC,eAAA,GAAAH,MAAA,CAAAjC,IAAA,CAAAqC,IAAA,IAAAJ,MAAA,CAAAjC,IAAA,CAAAP,QAAA;UACA,IAAAwC,MAAA,CAAAzC,QAAA,CAAAC,QAAA,KAAA2C,eAAA;YACAH,MAAA,CAAAZ,QAAA,CAAAC,KAAA;YACA;UACA;;UAEA;UACAW,MAAA,CAAAK,QAAA;YACAC,iBAAA;YACAC,gBAAA;YACAC,IAAA;UACA,GAAAvB,IAAA;YACAe,MAAA,CAAAS,cAAA;UACA,GAAAlB,KAAA;YACAS,MAAA,CAAAZ,QAAA,CAAAsB,IAAA;UACA;QACA;MACA;IACA;IAEA;IACAD,cAAA,WAAAA,eAAA;MAAA,IAAAE,MAAA;MACA,KAAA7C,OAAA;;MAEA;MACA,IAAA8C,UAAA,GAAAC,aAAA,UAAA9C,IAAA;MACA6C,UAAA,CAAApD,QAAA,QAAAD,QAAA,CAAAM,WAAA;MACA+C,UAAA,CAAAR,IAAA,QAAA7C,QAAA,CAAAM,WAAA;MAEA,KAAAc,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAAC,QAAA,CAAAC,GAAA;QACAC,MAAA;QACAlC,IAAA,EAAA8D;MACA,GAAA3B,IAAA,WAAA6B,KAAA;QAAA,IAAAhE,IAAA,GAAAgE,KAAA,CAAAhE,IAAA;QACA6D,MAAA,CAAA7C,OAAA;QACA,IAAAhB,IAAA,IAAAA,IAAA,CAAAqC,IAAA;UACAwB,MAAA,CAAAvB,QAAA;YACA2B,OAAA;YACAP,IAAA;YACAQ,QAAA;YACAC,OAAA,WAAAA,QAAA;cACA;cACAN,MAAA,CAAA7B,QAAA,CAAAoC,MAAA;cACAP,MAAA,CAAA7B,QAAA,CAAAoC,MAAA;cACAP,MAAA,CAAA7B,QAAA,CAAAoC,MAAA;cACAP,MAAA,CAAA7B,QAAA,CAAAoC,MAAA;cACAP,MAAA,CAAAQ,OAAA,CAAAC,OAAA;gBAAAC,IAAA;cAAA;YACA;UACA;QACA;UACAV,MAAA,CAAAvB,QAAA,CAAAC,KAAA,CAAAvC,IAAA,CAAAwC,GAAA;QACA;MACA,GAAAC,KAAA;QACAoB,MAAA,CAAA7C,OAAA;QACA6C,MAAA,CAAAvB,QAAA,CAAAC,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}