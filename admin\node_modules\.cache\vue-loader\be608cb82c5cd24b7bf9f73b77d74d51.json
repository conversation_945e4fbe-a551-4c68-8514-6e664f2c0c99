{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue?vue&type=style&index=0&id=14b1c880&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue", "mtime": 1750589767885}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ci5zdGF0cy1jb250YWluZXIgewogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgCiAgLnN0YXQtY2FyZCB7CiAgICBiYWNrZ3JvdW5kOiB3aGl0ZTsKICAgIGJvcmRlci1yYWRpdXM6IDhweDsKICAgIHBhZGRpbmc6IDIwcHg7CiAgICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpOwogICAgZGlzcGxheTogZmxleDsKICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlOwogICAgCiAgICAmOmhvdmVyIHsKICAgICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpOwogICAgICBib3gtc2hhZG93OiAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7CiAgICB9CiAgICAKICAgIC5zdGF0LWljb24gewogICAgICB3aWR0aDogNjBweDsKICAgICAgaGVpZ2h0OiA2MHB4OwogICAgICBib3JkZXItcmFkaXVzOiA1MCU7CiAgICAgIGRpc3BsYXk6IGZsZXg7CiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyOwogICAgICBtYXJnaW4tcmlnaHQ6IDE1cHg7CiAgICAgIAogICAgICBpIHsKICAgICAgICBmb250LXNpemU6IDI0cHg7CiAgICAgICAgY29sb3I6IHdoaXRlOwogICAgICB9CiAgICB9CiAgICAKICAgIC5zdGF0LWNvbnRlbnQgewogICAgICBmbGV4OiAxOwogICAgICAKICAgICAgLnN0YXQtbnVtYmVyIHsKICAgICAgICBmb250LXNpemU6IDI0cHg7CiAgICAgICAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgICAgICAgY29sb3I6ICMyYzNlNTA7CiAgICAgICAgbWFyZ2luLWJvdHRvbTogNXB4OwogICAgICB9CiAgICAgIAogICAgICAuc3RhdC1sYWJlbCB7CiAgICAgICAgZm9udC1zaXplOiAxNHB4OwogICAgICAgIGNvbG9yOiAjOTA5Mzk5OwogICAgICB9CiAgICB9CiAgICAKICAgICYuc3RhdC0xIC5zdGF0LWljb24gewogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNjY3ZWVhIDAlLCAjNzY0YmEyIDEwMCUpOwogICAgfQogICAgCiAgICAmLnN0YXQtMiAuc3RhdC1pY29uIHsKICAgICAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiAwJSwgI2Y1NTc2YyAxMDAlKTsKICAgIH0KICAgIAogICAgJi5zdGF0LTMgLnN0YXQtaWNvbiB7CiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM0ZmFjZmUgMCUsICMwMGYyZmUgMTAwJSk7CiAgICB9CiAgICAKICAgICYuc3RhdC00IC5zdGF0LWljb24gewogICAgICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDNlOTdiIDAlLCAjMzhmOWQ3IDEwMCUpOwogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["HomeStats.vue"], "names": [], "mappings": ";AA6FA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "HomeStats.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\n  <div class=\"stats-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" v-for=\"(stat, index) in statsData\" :key=\"index\">\n        <div class=\"stat-card\" :class=\"`stat-${index + 1}`\">\n          <div class=\"stat-icon\">\n            <i :class=\"stat.icon\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stat.value }}</div>\n            <div class=\"stat-label\">{{ stat.label }}</div>\n          </div>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeStats',\n  data() {\n    return {\n      statsData: [\n        {\n          label: '总用户数',\n          value: 0,\n          icon: 'el-icon-user'\n        },\n        {\n          label: '场地数量',\n          value: 0,\n          icon: 'el-icon-office-building'\n        },\n        {\n          label: '今日预约',\n          value: 0,\n          icon: 'el-icon-date'\n        },\n        {\n          label: '总收入',\n          value: 0,\n          icon: 'el-icon-money'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadStatsData()\n  },\n  methods: {\n    loadStatsData() {\n      // 获取用户统计\n      this.$http({\n        url: 'users/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[0].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 获取场地统计\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[1].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 获取预约统计\n      this.$http({\n        url: 'yuyuexinxi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[2].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 模拟收入数据\n      this.statsData[3].value = '¥' + (Math.random() * 100000).toFixed(0)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.stats-container {\n  margin-bottom: 20px;\n  \n  .stat-card {\n    background: white;\n    border-radius: 8px;\n    padding: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    }\n    \n    .stat-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 15px;\n      \n      i {\n        font-size: 24px;\n        color: white;\n      }\n    }\n    \n    .stat-content {\n      flex: 1;\n      \n      .stat-number {\n        font-size: 24px;\n        font-weight: bold;\n        color: #2c3e50;\n        margin-bottom: 5px;\n      }\n      \n      .stat-label {\n        font-size: 14px;\n        color: #909399;\n      }\n    }\n    \n    &.stat-1 .stat-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n    \n    &.stat-2 .stat-icon {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n    }\n    \n    &.stat-3 .stat-icon {\n      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n    }\n    \n    &.stat-4 .stat-icon {\n      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n    }\n  }\n}\n</style>\n"]}]}