{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCarousel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCarousel.vue", "mtime": 1750589744900}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "carouselD<PERSON>", "title", "description", "image", "mounted", "loadCarouselData", "methods", "_this", "$http", "url", "method", "params", "page", "limit", "then", "_ref", "code", "list", "length", "map", "item", "index", "concat", "value", "catch", "err", "console", "log"], "sources": ["src/components/home/<USER>"], "sourcesContent": ["<template>\n  <div class=\"carousel-container\">\n    <el-carousel :interval=\"4000\" type=\"card\" height=\"300px\" indicator-position=\"outside\">\n      <el-carousel-item v-for=\"(item, index) in carouselData\" :key=\"index\">\n        <div class=\"carousel-item\" :style=\"{ backgroundImage: `url(${item.image})` }\">\n          <div class=\"carousel-content\">\n            <h3>{{ item.title }}</h3>\n            <p>{{ item.description }}</p>\n          </div>\n        </div>\n      </el-carousel-item>\n    </el-carousel>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeCarousel',\n  data() {\n    return {\n      carouselData: [\n        {\n          title: '体育馆管理系统',\n          description: '现代化的体育场馆管理解决方案',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        },\n        {\n          title: '场地预约',\n          description: '便捷的在线场地预约服务',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        },\n        {\n          title: '智能管理',\n          description: '高效的场馆运营管理平台',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadCarouselData()\n  },\n  methods: {\n    loadCarouselData() {\n      // 从后端获取轮播图数据\n      this.$http({\n        url: 'config/list',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 5\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0 && data.list && data.list.length > 0) {\n          this.carouselData = data.list.map((item, index) => ({\n            title: item.name || `轮播图 ${index + 1}`,\n            description: item.value ? '精彩内容等您发现' : '欢迎使用体育馆管理系统',\n            image: item.value || '/tiyuguan/img/img/back-img-bg.jpg'\n          }))\n        }\n      }).catch(err => {\n        console.log('获取轮播图数据失败:', err)\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.carousel-container {\n  margin-bottom: 20px;\n  \n  .carousel-item {\n    width: 100%;\n    height: 300px;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n    border-radius: 8px;\n    position: relative;\n    overflow: hidden;\n    \n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));\n      z-index: 1;\n    }\n    \n    .carousel-content {\n      position: absolute;\n      bottom: 30px;\n      left: 30px;\n      color: white;\n      z-index: 2;\n      \n      h3 {\n        font-size: 24px;\n        font-weight: bold;\n        margin: 0 0 10px 0;\n        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n      }\n      \n      p {\n        font-size: 16px;\n        margin: 0;\n        opacity: 0.9;\n        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n      }\n    }\n  }\n}\n\n::v-deep .el-carousel__indicator {\n  .el-carousel__button {\n    background-color: rgba(255, 255, 255, 0.5);\n    \n    &.is-active {\n      background-color: #00c292;\n    }\n  }\n}\n</style>\n"], "mappings": ";;AAgBA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,YAAA,GACA;QACAC,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,WAAA;QACAC,KAAA;MACA,GACA;QACAF,KAAA;QACAC,WAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,gBAAA;EACA;EACAC,OAAA;IACAD,gBAAA,WAAAA,iBAAA;MAAA,IAAAE,KAAA;MACA;MACA,KAAAC,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UACAC,IAAA;UACAC,KAAA;QACA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAhB,IAAA,GAAAgB,IAAA,CAAAhB,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA,UAAAjB,IAAA,CAAAkB,IAAA,IAAAlB,IAAA,CAAAkB,IAAA,CAAAC,MAAA;UACAX,KAAA,CAAAP,YAAA,GAAAD,IAAA,CAAAkB,IAAA,CAAAE,GAAA,WAAAC,IAAA,EAAAC,KAAA;YAAA;cACApB,KAAA,EAAAmB,IAAA,CAAAtB,IAAA,0BAAAwB,MAAA,CAAAD,KAAA;cACAnB,WAAA,EAAAkB,IAAA,CAAAG,KAAA;cACApB,KAAA,EAAAiB,IAAA,CAAAG,KAAA;YACA;UAAA;QACA;MACA,GAAAC,KAAA,WAAAC,GAAA;QACAC,OAAA,CAAAC,GAAA,eAAAF,GAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}