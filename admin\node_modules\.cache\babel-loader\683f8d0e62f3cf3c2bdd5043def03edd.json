{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??ref--6!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\components\\VenueDetailsDialog.vue?vue&type=template&id=53869e0a&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\components\\VenueDetailsDialog.vue", "mtime": 1750596240454}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["render", "_vm", "_c", "_self", "staticClass", "attrs", "title", "visible", "dialogVisible", "width", "handleClose", "on", "updateVisible", "$event", "src", "venue", "changdiPhoto", "alt", "changdiName", "_v", "_s", "changdiUuidNumber", "type", "shangxiaTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changdi<PERSON>ldMoney", "_e", "gutter", "span", "changdiValue", "banquanValue", "s<PERSON><PERSON><PERSON><PERSON>", "changdiClicknum", "t<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "domProps", "innerHTML", "_l", "timeSlots", "slot", "key", "value", "class", "disabled", "label", "size", "click", "bookVenue", "staticRenderFns", "_withStripped"], "sources": ["D:/1/tiyuguan/admin/src/views/components/VenueDetailsDialog.vue"], "sourcesContent": ["var render = function render() {\n  var _vm = this,\n    _c = _vm._self._c\n  return _c(\n    \"el-dialog\",\n    {\n      staticClass: \"details-dialog\",\n      attrs: {\n        title: \"场地详情\",\n        visible: _vm.dialogVisible,\n        width: \"800px\",\n        \"before-close\": _vm.handleClose,\n      },\n      on: {\n        \"update:visible\": function ($event) {\n          _vm.dialogVisible = $event\n        },\n      },\n    },\n    [\n      _c(\"div\", { staticClass: \"details-content\" }, [\n        _c(\"div\", { staticClass: \"venue-header\" }, [\n          _c(\"div\", { staticClass: \"venue-image\" }, [\n            _c(\"img\", {\n              attrs: {\n                src: _vm.venue.changdiPhoto || \"/tiyuguan/img/noimg.jpg\",\n                alt: _vm.venue.changdiName,\n              },\n            }),\n          ]),\n          _c(\"div\", { staticClass: \"venue-basic-info\" }, [\n            _c(\"h2\", [_vm._v(_vm._s(_vm.venue.changdiName))]),\n            _c(\"div\", { staticClass: \"venue-code\" }, [\n              _vm._v(\"场地编号：\" + _vm._s(_vm.venue.changdiUuidNumber)),\n            ]),\n            _c(\n              \"div\",\n              { staticClass: \"venue-status\" },\n              [\n                _c(\n                  \"el-tag\",\n                  {\n                    attrs: {\n                      type:\n                        _vm.venue.shangxiaTypes === 1 ? \"success\" : \"danger\",\n                    },\n                  },\n                  [\n                    _vm._v(\n                      \" \" +\n                        _vm._s(\n                          _vm.venue.shangxiaTypes === 1 ? \"可预约\" : \"暂停预约\"\n                        ) +\n                        \" \"\n                    ),\n                  ]\n                ),\n              ],\n              1\n            ),\n            _c(\"div\", { staticClass: \"venue-price\" }, [\n              _c(\"span\", { staticClass: \"current-price\" }, [\n                _vm._v(\"¥\" + _vm._s(_vm.venue.changdiNewMoney)),\n              ]),\n              _vm.venue.changdiOldMoney !== _vm.venue.changdiNewMoney\n                ? _c(\"span\", { staticClass: \"original-price\" }, [\n                    _vm._v(\" ¥\" + _vm._s(_vm.venue.changdiOldMoney) + \" \"),\n                  ])\n                : _vm._e(),\n              _c(\"span\", { staticClass: \"price-unit\" }, [_vm._v(\"/时段\")]),\n            ]),\n          ]),\n        ]),\n        _c(\n          \"div\",\n          { staticClass: \"venue-details\" },\n          [\n            _c(\n              \"el-row\",\n              { attrs: { gutter: 30 } },\n              [\n                _c(\"el-col\", { attrs: { span: 12 } }, [\n                  _c(\"div\", { staticClass: \"detail-section\" }, [\n                    _c(\"h3\", [\n                      _c(\"i\", { staticClass: \"el-icon-info\" }),\n                      _vm._v(\" 基本信息\"),\n                    ]),\n                    _c(\"div\", { staticClass: \"detail-list\" }, [\n                      _c(\"div\", { staticClass: \"detail-item\" }, [\n                        _c(\"span\", { staticClass: \"label\" }, [\n                          _vm._v(\"场地类型：\"),\n                        ]),\n                        _c(\"span\", { staticClass: \"value\" }, [\n                          _vm._v(_vm._s(_vm.venue.changdiValue)),\n                        ]),\n                      ]),\n                      _c(\"div\", { staticClass: \"detail-item\" }, [\n                        _c(\"span\", { staticClass: \"label\" }, [\n                          _vm._v(\"半全场：\"),\n                        ]),\n                        _c(\"span\", { staticClass: \"value\" }, [\n                          _vm._v(_vm._s(_vm.venue.banquanValue)),\n                        ]),\n                      ]),\n                      _c(\"div\", { staticClass: \"detail-item\" }, [\n                        _c(\"span\", { staticClass: \"label\" }, [\n                          _vm._v(\"开放时间：\"),\n                        ]),\n                        _c(\"span\", { staticClass: \"value\" }, [\n                          _vm._v(\n                            _vm._s(_vm.venue.shijianduan || \"08:00-22:00\")\n                          ),\n                        ]),\n                      ]),\n                      _c(\"div\", { staticClass: \"detail-item\" }, [\n                        _c(\"span\", { staticClass: \"label\" }, [\n                          _vm._v(\"点击次数：\"),\n                        ]),\n                        _c(\"span\", { staticClass: \"value\" }, [\n                          _vm._v(_vm._s(_vm.venue.changdiClicknum) + \"次\"),\n                        ]),\n                      ]),\n                    ]),\n                  ]),\n                ]),\n                _c(\"el-col\", { attrs: { span: 12 } }, [\n                  _c(\"div\", { staticClass: \"detail-section\" }, [\n                    _c(\"h3\", [\n                      _c(\"i\", { staticClass: \"el-icon-star-on\" }),\n                      _vm._v(\" 推荐信息\"),\n                    ]),\n                    _c(\"div\", { staticClass: \"recommendation\" }, [\n                      _c(\"p\", [\n                        _vm._v(_vm._s(_vm.venue.tuijian || \"暂无推荐信息\")),\n                      ]),\n                    ]),\n                  ]),\n                ]),\n              ],\n              1\n            ),\n          ],\n          1\n        ),\n        _vm.venue.changdiContent\n          ? _c(\"div\", { staticClass: \"venue-description\" }, [\n              _c(\"h3\", [\n                _c(\"i\", { staticClass: \"el-icon-document\" }),\n                _vm._v(\" 场地介绍\"),\n              ]),\n              _c(\"div\", {\n                staticClass: \"description-content\",\n                domProps: { innerHTML: _vm._s(_vm.venue.changdiContent) },\n              }),\n            ])\n          : _vm._e(),\n        _c(\"div\", { staticClass: \"available-slots\" }, [\n          _c(\"h3\", [\n            _c(\"i\", { staticClass: \"el-icon-time\" }),\n            _vm._v(\" 可预约时间段\"),\n          ]),\n          _c(\n            \"div\",\n            { staticClass: \"time-slots\" },\n            _vm._l(_vm.timeSlots, function (slot) {\n              return _c(\n                \"div\",\n                {\n                  key: slot.value,\n                  staticClass: \"time-slot\",\n                  class: { disabled: slot.disabled },\n                },\n                [\n                  _c(\"div\", { staticClass: \"slot-time\" }, [\n                    _vm._v(_vm._s(slot.label)),\n                  ]),\n                  _c(\n                    \"div\",\n                    { staticClass: \"slot-status\" },\n                    [\n                      _c(\n                        \"el-tag\",\n                        {\n                          attrs: {\n                            type: slot.disabled ? \"danger\" : \"success\",\n                            size: \"mini\",\n                          },\n                        },\n                        [\n                          _vm._v(\n                            \" \" +\n                              _vm._s(slot.disabled ? \"已预约\" : \"可预约\") +\n                              \" \"\n                          ),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                ]\n              )\n            }),\n            0\n          ),\n        ]),\n        _c(\"div\", { staticClass: \"booking-notice\" }, [\n          _c(\"h3\", [\n            _c(\"i\", { staticClass: \"el-icon-warning\" }),\n            _vm._v(\" 预约须知\"),\n          ]),\n          _c(\"ul\", { staticClass: \"notice-list\" }, [\n            _c(\"li\", [_vm._v(\"请提前至少1小时预约，当天预约需要电话确认\")]),\n            _c(\"li\", [\n              _vm._v(\"预约成功后请按时到场，迟到超过15分钟将自动取消\"),\n            ]),\n            _c(\"li\", [_vm._v(\"如需取消预约，请提前2小时联系客服\")]),\n            _c(\"li\", [_vm._v(\"场地内禁止吸烟，请爱护场地设施\")]),\n            _c(\"li\", [_vm._v(\"运动时请注意安全，建议穿着运动服装和运动鞋\")]),\n          ]),\n        ]),\n      ]),\n      _c(\n        \"div\",\n        {\n          staticClass: \"dialog-footer\",\n          attrs: { slot: \"footer\" },\n          slot: \"footer\",\n        },\n        [\n          _c(\"el-button\", { on: { click: _vm.handleClose } }, [_vm._v(\"关闭\")]),\n          _c(\n            \"el-button\",\n            {\n              attrs: {\n                type: \"primary\",\n                disabled: _vm.venue.shangxiaTypes !== 1,\n              },\n              on: { click: _vm.bookVenue },\n            },\n            [_c(\"i\", { staticClass: \"el-icon-date\" }), _vm._v(\" 立即预约 \")]\n          ),\n        ],\n        1\n      ),\n    ]\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"], "mappings": "AAAA,IAAIA,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EAC7B,IAAIC,GAAG,GAAG,IAAI;IACZC,EAAE,GAAGD,GAAG,CAACE,KAAK,CAACD,EAAE;EACnB,OAAOA,EAAE,CACP,WAAW,EACX;IACEE,WAAW,EAAE,gBAAgB;IAC7BC,KAAK,EAAE;MACLC,KAAK,EAAE,MAAM;MACbC,OAAO,EAAEN,GAAG,CAACO,aAAa;MAC1BC,KAAK,EAAE,OAAO;MACd,cAAc,EAAER,GAAG,CAACS;IACtB,CAAC;IACDC,EAAE,EAAE;MACF,gBAAgB,EAAE,SAAlBC,aAAgBA,CAAYC,MAAM,EAAE;QAClCZ,GAAG,CAACO,aAAa,GAAGK,MAAM;MAC5B;IACF;EACF,CAAC,EACD,CACEX,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,EAAE,CACzCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IACRG,KAAK,EAAE;MACLS,GAAG,EAAEb,GAAG,CAACc,KAAK,CAACC,YAAY,IAAI,yBAAyB;MACxDC,GAAG,EAAEhB,GAAG,CAACc,KAAK,CAACG;IACjB;EACF,CAAC,CAAC,CACH,CAAC,EACFhB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,EAAE,CAC7CF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACG,WAAW,CAAC,CAAC,CAAC,CAAC,EACjDhB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CACvCH,GAAG,CAACkB,EAAE,CAAC,OAAO,GAAGlB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACM,iBAAiB,CAAC,CAAC,CACtD,CAAC,EACFnB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAe,CAAC,EAC/B,CACEF,EAAE,CACA,QAAQ,EACR;IACEG,KAAK,EAAE;MACLiB,IAAI,EACFrB,GAAG,CAACc,KAAK,CAACQ,aAAa,KAAK,CAAC,GAAG,SAAS,GAAG;IAChD;EACF,CAAC,EACD,CACEtB,GAAG,CAACkB,EAAE,CACJ,GAAG,GACDlB,GAAG,CAACmB,EAAE,CACJnB,GAAG,CAACc,KAAK,CAACQ,aAAa,KAAK,CAAC,GAAG,KAAK,GAAG,MAC1C,CAAC,GACD,GACJ,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,EACDrB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAAE,CAC3CH,GAAG,CAACkB,EAAE,CAAC,GAAG,GAAGlB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACS,eAAe,CAAC,CAAC,CAChD,CAAC,EACFvB,GAAG,CAACc,KAAK,CAACU,eAAe,KAAKxB,GAAG,CAACc,KAAK,CAACS,eAAe,GACnDtB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC5CH,GAAG,CAACkB,EAAE,CAAC,IAAI,GAAGlB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACU,eAAe,CAAC,GAAG,GAAG,CAAC,CACvD,CAAC,GACFxB,GAAG,CAACyB,EAAE,CAAC,CAAC,EACZxB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAa,CAAC,EAAE,CAACH,GAAG,CAACkB,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAC3D,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAgB,CAAC,EAChC,CACEF,EAAE,CACA,QAAQ,EACR;IAAEG,KAAK,EAAE;MAAEsB,MAAM,EAAE;IAAG;EAAE,CAAC,EACzB,CACEzB,EAAE,CAAC,QAAQ,EAAE;IAAEG,KAAK,EAAE;MAAEuB,IAAI,EAAE;IAAG;EAAE,CAAC,EAAE,CACpC1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCH,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACc,YAAY,CAAC,CAAC,CACvC,CAAC,CACH,CAAC,EACF3B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CAAC,MAAM,CAAC,CACf,CAAC,EACFjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACe,YAAY,CAAC,CAAC,CACvC,CAAC,CACH,CAAC,EACF5B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CACJlB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACgB,WAAW,IAAI,aAAa,CAC/C,CAAC,CACF,CAAC,CACH,CAAC,EACF7B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACxCF,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFjB,EAAE,CAAC,MAAM,EAAE;IAAEE,WAAW,EAAE;EAAQ,CAAC,EAAE,CACnCH,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACiB,eAAe,CAAC,GAAG,GAAG,CAAC,CAChD,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,EACF9B,EAAE,CAAC,QAAQ,EAAE;IAAEG,KAAK,EAAE;MAAEuB,IAAI,EAAE;IAAG;EAAE,CAAC,EAAE,CACpC1B,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC3CH,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,GAAG,EAAE,CACND,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACkB,OAAO,IAAI,QAAQ,CAAC,CAAC,CAC9C,CAAC,CACH,CAAC,CACH,CAAC,CACH,CAAC,CACH,EACD,CACF,CAAC,CACF,EACD,CACF,CAAC,EACDhC,GAAG,CAACc,KAAK,CAACmB,cAAc,GACpBhC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAoB,CAAC,EAAE,CAC9CF,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAmB,CAAC,CAAC,EAC5CH,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFjB,EAAE,CAAC,KAAK,EAAE;IACRE,WAAW,EAAE,qBAAqB;IAClC+B,QAAQ,EAAE;MAAEC,SAAS,EAAEnC,GAAG,CAACmB,EAAE,CAACnB,GAAG,CAACc,KAAK,CAACmB,cAAc;IAAE;EAC1D,CAAC,CAAC,CACH,CAAC,GACFjC,GAAG,CAACyB,EAAE,CAAC,CAAC,EACZxB,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,EAAE,CAC5CF,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EACxCH,GAAG,CAACkB,EAAE,CAAC,SAAS,CAAC,CAClB,CAAC,EACFjB,EAAE,CACA,KAAK,EACL;IAAEE,WAAW,EAAE;EAAa,CAAC,EAC7BH,GAAG,CAACoC,EAAE,CAACpC,GAAG,CAACqC,SAAS,EAAE,UAAUC,IAAI,EAAE;IACpC,OAAOrC,EAAE,CACP,KAAK,EACL;MACEsC,GAAG,EAAED,IAAI,CAACE,KAAK;MACfrC,WAAW,EAAE,WAAW;MACxBsC,KAAK,EAAE;QAAEC,QAAQ,EAAEJ,IAAI,CAACI;MAAS;IACnC,CAAC,EACD,CACEzC,EAAE,CAAC,KAAK,EAAE;MAAEE,WAAW,EAAE;IAAY,CAAC,EAAE,CACtCH,GAAG,CAACkB,EAAE,CAAClB,GAAG,CAACmB,EAAE,CAACmB,IAAI,CAACK,KAAK,CAAC,CAAC,CAC3B,CAAC,EACF1C,EAAE,CACA,KAAK,EACL;MAAEE,WAAW,EAAE;IAAc,CAAC,EAC9B,CACEF,EAAE,CACA,QAAQ,EACR;MACEG,KAAK,EAAE;QACLiB,IAAI,EAAEiB,IAAI,CAACI,QAAQ,GAAG,QAAQ,GAAG,SAAS;QAC1CE,IAAI,EAAE;MACR;IACF,CAAC,EACD,CACE5C,GAAG,CAACkB,EAAE,CACJ,GAAG,GACDlB,GAAG,CAACmB,EAAE,CAACmB,IAAI,CAACI,QAAQ,GAAG,KAAK,GAAG,KAAK,CAAC,GACrC,GACJ,CAAC,CAEL,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC;EACH,CAAC,CAAC,EACF,CACF,CAAC,CACF,CAAC,EACFzC,EAAE,CAAC,KAAK,EAAE;IAAEE,WAAW,EAAE;EAAiB,CAAC,EAAE,CAC3CF,EAAE,CAAC,IAAI,EAAE,CACPA,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAkB,CAAC,CAAC,EAC3CH,GAAG,CAACkB,EAAE,CAAC,OAAO,CAAC,CAChB,CAAC,EACFjB,EAAE,CAAC,IAAI,EAAE;IAAEE,WAAW,EAAE;EAAc,CAAC,EAAE,CACvCF,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,uBAAuB,CAAC,CAAC,CAAC,EAC3CjB,EAAE,CAAC,IAAI,EAAE,CACPD,GAAG,CAACkB,EAAE,CAAC,0BAA0B,CAAC,CACnC,CAAC,EACFjB,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,mBAAmB,CAAC,CAAC,CAAC,EACvCjB,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,iBAAiB,CAAC,CAAC,CAAC,EACrCjB,EAAE,CAAC,IAAI,EAAE,CAACD,GAAG,CAACkB,EAAE,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAC5C,CAAC,CACH,CAAC,CACH,CAAC,EACFjB,EAAE,CACA,KAAK,EACL;IACEE,WAAW,EAAE,eAAe;IAC5BC,KAAK,EAAE;MAAEkC,IAAI,EAAE;IAAS,CAAC;IACzBA,IAAI,EAAE;EACR,CAAC,EACD,CACErC,EAAE,CAAC,WAAW,EAAE;IAAES,EAAE,EAAE;MAAEmC,KAAK,EAAE7C,GAAG,CAACS;IAAY;EAAE,CAAC,EAAE,CAACT,GAAG,CAACkB,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,EACnEjB,EAAE,CACA,WAAW,EACX;IACEG,KAAK,EAAE;MACLiB,IAAI,EAAE,SAAS;MACfqB,QAAQ,EAAE1C,GAAG,CAACc,KAAK,CAACQ,aAAa,KAAK;IACxC,CAAC;IACDZ,EAAE,EAAE;MAAEmC,KAAK,EAAE7C,GAAG,CAAC8C;IAAU;EAC7B,CAAC,EACD,CAAC7C,EAAE,CAAC,GAAG,EAAE;IAAEE,WAAW,EAAE;EAAe,CAAC,CAAC,EAAEH,GAAG,CAACkB,EAAE,CAAC,QAAQ,CAAC,CAC7D,CAAC,CACF,EACD,CACF,CAAC,CAEL,CAAC;AACH,CAAC;AACD,IAAI6B,eAAe,GAAG,EAAE;AACxBhD,MAAM,CAACiD,aAAa,GAAG,IAAI;AAE3B,SAASjD,MAAM,EAAEgD,eAAe", "ignoreList": []}]}