{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue", "mtime": 1750589767885}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "statsData", "label", "value", "icon", "mounted", "loadStatsData", "methods", "_this", "$http", "url", "method", "params", "page", "limit", "then", "_ref", "code", "total", "catch", "_ref2", "_ref3", "Math", "random", "toFixed"], "sources": ["src/components/home/<USER>"], "sourcesContent": ["<template>\n  <div class=\"stats-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" v-for=\"(stat, index) in statsData\" :key=\"index\">\n        <div class=\"stat-card\" :class=\"`stat-${index + 1}`\">\n          <div class=\"stat-icon\">\n            <i :class=\"stat.icon\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stat.value }}</div>\n            <div class=\"stat-label\">{{ stat.label }}</div>\n          </div>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeStats',\n  data() {\n    return {\n      statsData: [\n        {\n          label: '总用户数',\n          value: 0,\n          icon: 'el-icon-user'\n        },\n        {\n          label: '场地数量',\n          value: 0,\n          icon: 'el-icon-office-building'\n        },\n        {\n          label: '今日预约',\n          value: 0,\n          icon: 'el-icon-date'\n        },\n        {\n          label: '总收入',\n          value: 0,\n          icon: 'el-icon-money'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadStatsData()\n  },\n  methods: {\n    loadStatsData() {\n      // 获取用户统计\n      this.$http({\n        url: 'users/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[0].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 获取场地统计\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[1].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 获取预约统计\n      this.$http({\n        url: 'yuyuexinxi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[2].value = data.total || 0\n        }\n      }).catch(() => {})\n\n      // 模拟收入数据\n      this.statsData[3].value = '¥' + (Math.random() * 100000).toFixed(0)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.stats-container {\n  margin-bottom: 20px;\n  \n  .stat-card {\n    background: white;\n    border-radius: 8px;\n    padding: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    }\n    \n    .stat-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 15px;\n      \n      i {\n        font-size: 24px;\n        color: white;\n      }\n    }\n    \n    .stat-content {\n      flex: 1;\n      \n      .stat-number {\n        font-size: 24px;\n        font-weight: bold;\n        color: #2c3e50;\n        margin-bottom: 5px;\n      }\n      \n      .stat-label {\n        font-size: 14px;\n        color: #909399;\n      }\n    }\n    \n    &.stat-1 .stat-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n    \n    &.stat-2 .stat-icon {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n    }\n    \n    &.stat-3 .stat-icon {\n      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n    }\n    \n    &.stat-4 .stat-icon {\n      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n    }\n  }\n}\n</style>\n"], "mappings": ";AAmBA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,IAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACA;MACA,KAAAC,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAhB,IAAA,GAAAgB,IAAA,CAAAhB,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;UACAT,KAAA,CAAAP,SAAA,IAAAE,KAAA,GAAAH,IAAA,CAAAkB,KAAA;QACA;MACA,GAAAC,KAAA;;MAEA;MACA,KAAAV,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAAK,KAAA;QAAA,IAAApB,IAAA,GAAAoB,KAAA,CAAApB,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;UACAT,KAAA,CAAAP,SAAA,IAAAE,KAAA,GAAAH,IAAA,CAAAkB,KAAA;QACA;MACA,GAAAC,KAAA;;MAEA;MACA,KAAAV,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAAM,KAAA;QAAA,IAAArB,IAAA,GAAAqB,KAAA,CAAArB,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;UACAT,KAAA,CAAAP,SAAA,IAAAE,KAAA,GAAAH,IAAA,CAAAkB,KAAA;QACA;MACA,GAAAC,KAAA;;MAEA;MACA,KAAAlB,SAAA,IAAAE,KAAA,UAAAmB,IAAA,CAAAC,MAAA,aAAAC,OAAA;IACA;EACA;AACA", "ignoreList": []}]}