{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeStats.vue", "mtime": 1750598680704}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "data", "statsData", "label", "value", "icon", "mounted", "loadStatsData", "methods", "_this", "$http", "url", "method", "params", "page", "limit", "then", "_ref", "code", "total", "catch", "console", "error", "_ref2", "today", "Date", "toISOString", "split", "buyTime", "_ref3", "getTotalRevenue", "_this2", "changdiOrderTypes", "_ref4", "totalRevenue", "orders", "list", "for<PERSON>ach", "order", "changdiOrderTruePrice", "parseFloat", "toFixed"], "sources": ["src/components/home/<USER>"], "sourcesContent": ["<template>\n  <div class=\"stats-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"6\" v-for=\"(stat, index) in statsData\" :key=\"index\">\n        <div class=\"stat-card\" :class=\"`stat-${index + 1}`\">\n          <div class=\"stat-icon\">\n            <i :class=\"stat.icon\"></i>\n          </div>\n          <div class=\"stat-content\">\n            <div class=\"stat-number\">{{ stat.value }}</div>\n            <div class=\"stat-label\">{{ stat.label }}</div>\n          </div>\n        </div>\n      </el-col>\n    </el-row>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeStats',\n  data() {\n    return {\n      statsData: [\n        {\n          label: '总用户数',\n          value: 0,\n          icon: 'el-icon-user'\n        },\n        {\n          label: '场地数量',\n          value: 0,\n          icon: 'el-icon-office-building'\n        },\n        {\n          label: '今日预约',\n          value: 0,\n          icon: 'el-icon-date'\n        },\n        {\n          label: '总收入',\n          value: 0,\n          icon: 'el-icon-money'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadStatsData()\n  },\n  methods: {\n    loadStatsData() {\n      // 获取用户统计\n      this.$http({\n        url: 'yonghu/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[0].value = data.data.total || 0\n        }\n      }).catch(() => {\n        console.error('获取用户统计失败')\n      })\n\n      // 获取场地统计\n      this.$http({\n        url: 'changdi/page',\n        method: 'get',\n        params: { page: 1, limit: 1 }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[1].value = data.data.total || 0\n        }\n      }).catch(() => {\n        console.error('获取场地统计失败')\n      })\n\n      // 获取今日预约统计\n      const today = new Date().toISOString().split('T')[0]\n      this.$http({\n        url: 'changdiOrder/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 1,\n          buyTime: today\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.statsData[2].value = data.data.total || 0\n        }\n      }).catch(() => {\n        console.error('获取今日预约统计失败')\n      })\n\n      // 获取总收入统计\n      this.getTotalRevenue()\n    },\n\n    // 获取总收入\n    getTotalRevenue() {\n      this.$http({\n        url: 'changdiOrder/page',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 1000, // 获取更多数据来计算总收入\n          changdiOrderTypes: 1 // 只统计已确认的预约\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          let totalRevenue = 0\n          const orders = data.data.list || []\n\n          orders.forEach(order => {\n            if (order.changdiOrderTruePrice) {\n              totalRevenue += parseFloat(order.changdiOrderTruePrice) || 0\n            }\n          })\n\n          this.statsData[3].value = '¥' + totalRevenue.toFixed(2)\n        }\n      }).catch(() => {\n        console.error('获取收入统计失败')\n        this.statsData[3].value = '¥0.00'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.stats-container {\n  margin-bottom: 20px;\n  \n  .stat-card {\n    background: white;\n    border-radius: 8px;\n    padding: 20px;\n    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\n    display: flex;\n    align-items: center;\n    transition: all 0.3s ease;\n    \n    &:hover {\n      transform: translateY(-2px);\n      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\n    }\n    \n    .stat-icon {\n      width: 60px;\n      height: 60px;\n      border-radius: 50%;\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      margin-right: 15px;\n      \n      i {\n        font-size: 24px;\n        color: white;\n      }\n    }\n    \n    .stat-content {\n      flex: 1;\n      \n      .stat-number {\n        font-size: 24px;\n        font-weight: bold;\n        color: #2c3e50;\n        margin-bottom: 5px;\n      }\n      \n      .stat-label {\n        font-size: 14px;\n        color: #909399;\n      }\n    }\n    \n    &.stat-1 .stat-icon {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    }\n    \n    &.stat-2 .stat-icon {\n      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\n    }\n    \n    &.stat-3 .stat-icon {\n      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);\n    }\n    \n    &.stat-4 .stat-icon {\n      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;AAmBA;EACAA,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,SAAA,GACA;QACAC,KAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,IAAA;MACA,GACA;QACAF,KAAA;QACAC,KAAA;QACAC,IAAA;MACA;IAEA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,aAAA;EACA;EACAC,OAAA;IACAD,aAAA,WAAAA,cAAA;MAAA,IAAAE,KAAA;MACA;MACA,KAAAC,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAhB,IAAA,GAAAgB,IAAA,CAAAhB,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;UACAT,KAAA,CAAAP,SAAA,IAAAE,KAAA,GAAAH,IAAA,CAAAA,IAAA,CAAAkB,KAAA;QACA;MACA,GAAAC,KAAA;QACAC,OAAA,CAAAC,KAAA;MACA;;MAEA;MACA,KAAAZ,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UAAAC,IAAA;UAAAC,KAAA;QAAA;MACA,GAAAC,IAAA,WAAAO,KAAA;QAAA,IAAAtB,IAAA,GAAAsB,KAAA,CAAAtB,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;UACAT,KAAA,CAAAP,SAAA,IAAAE,KAAA,GAAAH,IAAA,CAAAA,IAAA,CAAAkB,KAAA;QACA;MACA,GAAAC,KAAA;QACAC,OAAA,CAAAC,KAAA;MACA;;MAEA;MACA,IAAAE,KAAA,OAAAC,IAAA,GAAAC,WAAA,GAAAC,KAAA;MACA,KAAAjB,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UACAC,IAAA;UACAC,KAAA;UACAa,OAAA,EAAAJ;QACA;MACA,GAAAR,IAAA,WAAAa,KAAA;QAAA,IAAA5B,IAAA,GAAA4B,KAAA,CAAA5B,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;UACAT,KAAA,CAAAP,SAAA,IAAAE,KAAA,GAAAH,IAAA,CAAAA,IAAA,CAAAkB,KAAA;QACA;MACA,GAAAC,KAAA;QACAC,OAAA,CAAAC,KAAA;MACA;;MAEA;MACA,KAAAQ,eAAA;IACA;IAEA;IACAA,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAArB,KAAA;QACAC,GAAA;QACAC,MAAA;QACAC,MAAA;UACAC,IAAA;UACAC,KAAA;UAAA;UACAiB,iBAAA;QACA;MACA,GAAAhB,IAAA,WAAAiB,KAAA;QAAA,IAAAhC,IAAA,GAAAgC,KAAA,CAAAhC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAiB,IAAA;UACA,IAAAgB,YAAA;UACA,IAAAC,MAAA,GAAAlC,IAAA,CAAAA,IAAA,CAAAmC,IAAA;UAEAD,MAAA,CAAAE,OAAA,WAAAC,KAAA;YACA,IAAAA,KAAA,CAAAC,qBAAA;cACAL,YAAA,IAAAM,UAAA,CAAAF,KAAA,CAAAC,qBAAA;YACA;UACA;UAEAR,MAAA,CAAA7B,SAAA,IAAAE,KAAA,SAAA8B,YAAA,CAAAO,OAAA;QACA;MACA,GAAArB,KAAA;QACAC,OAAA,CAAAC,KAAA;QACAS,MAAA,CAAA7B,SAAA,IAAAE,KAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}