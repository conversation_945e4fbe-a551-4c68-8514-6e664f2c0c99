{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue?vue&type=style&index=0&id=467fc075&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue", "mtime": 1750593551799}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["update-password.vue"], "names": [], "mappings": ";AAoUA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "update-password.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"update-password-container\">\r\n    <div class=\"password-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2><i class=\"el-icon-lock\"></i> 修改密码</h2>\r\n          <p>为了您的账户安全，请定期更换密码</p>\r\n        </div>\r\n        <div class=\"security-tips\">\r\n          <el-alert\r\n            title=\"安全提示\"\r\n            type=\"info\"\r\n            :closable=\"false\"\r\n            show-icon>\r\n            <template slot=\"description\">\r\n              <ul>\r\n                <li>密码长度至少6位，建议包含字母、数字和特殊字符</li>\r\n                <li>不要使用过于简单的密码，如123456、password等</li>\r\n                <li>修改密码后需要重新登录</li>\r\n              </ul>\r\n            </template>\r\n          </el-alert>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card class=\"password-card\">\r\n      <div class=\"user-info\">\r\n        <el-avatar :size=\"60\" :src=\"user.yonghuPhoto || user.touxiang\" icon=\"el-icon-user-solid\"></el-avatar>\r\n        <div class=\"user-details\">\r\n          <h3>{{ user.yonghuName || user.username || '用户' }}</h3>\r\n          <p>{{ user.yonghuPhone || user.phone || '暂无手机号' }}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <el-form\r\n        class=\"password-form\"\r\n        ref=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        :model=\"ruleForm\"\r\n        label-width=\"120px\">\r\n\r\n        <el-form-item label=\"当前密码\" prop=\"password\">\r\n          <el-input\r\n            v-model=\"ruleForm.password\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请输入当前密码\"\r\n            prefix-icon=\"el-icon-lock\"\r\n            clearable>\r\n          </el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"新密码\" prop=\"newpassword\">\r\n          <el-input\r\n            v-model=\"ruleForm.newpassword\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请输入新密码\"\r\n            prefix-icon=\"el-icon-key\"\r\n            clearable\r\n            @input=\"checkPasswordStrength\">\r\n          </el-input>\r\n          <div class=\"password-strength\" v-if=\"ruleForm.newpassword\">\r\n            <div class=\"strength-label\">密码强度：</div>\r\n            <div class=\"strength-bar\">\r\n              <div\r\n                class=\"strength-fill\"\r\n                :class=\"passwordStrength.class\"\r\n                :style=\"{width: passwordStrength.width}\">\r\n              </div>\r\n            </div>\r\n            <span class=\"strength-text\" :class=\"passwordStrength.class\">\r\n              {{ passwordStrength.text }}\r\n            </span>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"确认新密码\" prop=\"repassword\">\r\n          <el-input\r\n            v-model=\"ruleForm.repassword\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请再次输入新密码\"\r\n            prefix-icon=\"el-icon-check\"\r\n            clearable>\r\n          </el-input>\r\n        </el-form-item>\r\n\r\n        <div class=\"form-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"onUpdateHandler\"\r\n            :loading=\"loading\"\r\n            size=\"large\">\r\n            <i class=\"el-icon-check\"></i>\r\n            确认修改\r\n          </el-button>\r\n          <el-button\r\n            @click=\"resetForm\"\r\n            size=\"large\">\r\n            <i class=\"el-icon-refresh\"></i>\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    // 自定义验证规则\r\n    const validatePassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请输入当前密码'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const validateNewPassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请输入新密码'));\r\n      } else if (value.length < 6) {\r\n        callback(new Error('密码长度至少6位'));\r\n      } else if (value === this.ruleForm.password) {\r\n        callback(new Error('新密码不能与当前密码相同'));\r\n      } else {\r\n        // 触发确认密码的重新验证\r\n        if (this.ruleForm.repassword !== '') {\r\n          this.$refs.ruleForm.validateField('repassword');\r\n        }\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const validateRePassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请确认新密码'));\r\n      } else if (value !== this.ruleForm.newpassword) {\r\n        callback(new Error('两次输入的密码不一致'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      loading: false,\r\n      ruleForm: {\r\n        password: '',\r\n        newpassword: '',\r\n        repassword: ''\r\n      },\r\n      user: {},\r\n      passwordStrength: {\r\n        width: '0%',\r\n        class: '',\r\n        text: ''\r\n      },\r\n      rules: {\r\n        password: [{ validator: validatePassword, trigger: 'blur' }],\r\n        newpassword: [{ validator: validateNewPassword, trigger: 'blur' }],\r\n        repassword: [{ validator: validateRePassword, trigger: 'blur' }]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getUserInfo();\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getUserInfo() {\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.user = data.data;\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取用户信息失败');\r\n      });\r\n    },\r\n\r\n    // 检查密码强度\r\n    checkPasswordStrength() {\r\n      const password = this.ruleForm.newpassword;\r\n      if (!password) {\r\n        this.passwordStrength = { width: '0%', class: '', text: '' };\r\n        return;\r\n      }\r\n\r\n      let score = 0;\r\n      let feedback = [];\r\n\r\n      // 长度检查\r\n      if (password.length >= 8) score += 25;\r\n      else if (password.length >= 6) score += 15;\r\n      else feedback.push('至少6位');\r\n\r\n      // 包含小写字母\r\n      if (/[a-z]/.test(password)) score += 25;\r\n      else feedback.push('包含小写字母');\r\n\r\n      // 包含大写字母\r\n      if (/[A-Z]/.test(password)) score += 25;\r\n      else feedback.push('包含大写字母');\r\n\r\n      // 包含数字\r\n      if (/\\d/.test(password)) score += 15;\r\n      else feedback.push('包含数字');\r\n\r\n      // 包含特殊字符\r\n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) score += 10;\r\n      else feedback.push('包含特殊字符');\r\n\r\n      // 设置强度显示\r\n      if (score < 30) {\r\n        this.passwordStrength = {\r\n          width: '25%',\r\n          class: 'weak',\r\n          text: '弱'\r\n        };\r\n      } else if (score < 60) {\r\n        this.passwordStrength = {\r\n          width: '50%',\r\n          class: 'medium',\r\n          text: '中等'\r\n        };\r\n      } else if (score < 80) {\r\n        this.passwordStrength = {\r\n          width: '75%',\r\n          class: 'strong',\r\n          text: '强'\r\n        };\r\n      } else {\r\n        this.passwordStrength = {\r\n          width: '100%',\r\n          class: 'very-strong',\r\n          text: '很强'\r\n        };\r\n      }\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$refs.ruleForm.resetFields();\r\n      this.ruleForm = {\r\n        password: '',\r\n        newpassword: '',\r\n        repassword: ''\r\n      };\r\n      this.passwordStrength = { width: '0%', class: '', text: '' };\r\n    },\r\n\r\n    // 修改密码\r\n    onUpdateHandler() {\r\n      this.$refs[\"ruleForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 验证原密码\r\n          const currentPassword = this.user.mima || this.user.password || '';\r\n          if (this.ruleForm.password !== currentPassword) {\r\n            this.$message.error(\"当前密码错误\");\r\n            return;\r\n          }\r\n\r\n          // 确认对话框\r\n          this.$confirm('确定要修改密码吗？修改后需要重新登录。', '确认修改', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.updatePassword();\r\n          }).catch(() => {\r\n            this.$message.info('已取消修改');\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 执行密码更新\r\n    updatePassword() {\r\n      this.loading = true;\r\n\r\n      // 更新用户密码\r\n      const updateData = { ...this.user };\r\n      updateData.password = this.ruleForm.newpassword;\r\n      updateData.mima = this.ruleForm.newpassword;\r\n\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: updateData\r\n      }).then(({ data }) => {\r\n        this.loading = false;\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"密码修改成功！请重新登录\",\r\n            type: \"success\",\r\n            duration: 2000,\r\n            onClose: () => {\r\n              // 清除登录信息并跳转到登录页\r\n              this.$storage.remove(\"Token\");\r\n              this.$storage.remove(\"role\");\r\n              this.$storage.remove(\"sessionTable\");\r\n              this.$storage.remove(\"adminName\");\r\n              this.$router.replace({ name: \"login\" });\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg || '修改密码失败');\r\n        }\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('网络错误，请稍后重试');\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.update-password-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n\r\n  .password-header {\r\n    margin-bottom: 30px;\r\n\r\n    .header-content {\r\n      .title-section {\r\n        text-align: center;\r\n        margin-bottom: 20px;\r\n\r\n        h2 {\r\n          font-size: 28px;\r\n          color: #2c3e50;\r\n          margin: 0 0 10px 0;\r\n\r\n          i {\r\n            color: #00c292;\r\n            margin-right: 10px;\r\n          }\r\n        }\r\n\r\n        p {\r\n          color: #909399;\r\n          margin: 0;\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .security-tips {\r\n        max-width: 600px;\r\n        margin: 0 auto;\r\n\r\n        ::v-deep .el-alert {\r\n          border-radius: 8px;\r\n\r\n          .el-alert__description {\r\n            ul {\r\n              margin: 0;\r\n              padding-left: 20px;\r\n\r\n              li {\r\n                margin: 5px 0;\r\n                color: #606266;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .password-card {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n\r\n    ::v-deep .el-card__body {\r\n      padding: 40px;\r\n    }\r\n\r\n    .user-info {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 40px;\r\n      padding: 20px;\r\n      background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n      border-radius: 8px;\r\n      color: white;\r\n\r\n      .user-details {\r\n        margin-left: 20px;\r\n\r\n        h3 {\r\n          margin: 0 0 5px 0;\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        p {\r\n          margin: 0;\r\n          opacity: 0.9;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .password-form {\r\n      .el-form-item {\r\n        margin-bottom: 30px;\r\n\r\n        ::v-deep .el-form-item__label {\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n          font-size: 16px;\r\n        }\r\n\r\n        ::v-deep .el-input__inner {\r\n          height: 50px;\r\n          line-height: 50px;\r\n          border-radius: 8px;\r\n          border: 2px solid #dcdfe6;\r\n          font-size: 16px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:focus {\r\n            border-color: #00c292;\r\n            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n          }\r\n        }\r\n\r\n        ::v-deep .el-input__prefix {\r\n          left: 15px;\r\n\r\n          .el-input__icon {\r\n            line-height: 50px;\r\n            color: #909399;\r\n          }\r\n        }\r\n      }\r\n\r\n      .password-strength {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n\r\n        .strength-label {\r\n          font-size: 14px;\r\n          color: #606266;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .strength-bar {\r\n          flex: 1;\r\n          height: 6px;\r\n          background: #f0f0f0;\r\n          border-radius: 3px;\r\n          overflow: hidden;\r\n\r\n          .strength-fill {\r\n            height: 100%;\r\n            transition: all 0.3s ease;\r\n            border-radius: 3px;\r\n\r\n            &.weak {\r\n              background: #f56c6c;\r\n            }\r\n\r\n            &.medium {\r\n              background: #e6a23c;\r\n            }\r\n\r\n            &.strong {\r\n              background: #67c23a;\r\n            }\r\n\r\n            &.very-strong {\r\n              background: #00c292;\r\n            }\r\n          }\r\n        }\r\n\r\n        .strength-text {\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          white-space: nowrap;\r\n\r\n          &.weak {\r\n            color: #f56c6c;\r\n          }\r\n\r\n          &.medium {\r\n            color: #e6a23c;\r\n          }\r\n\r\n          &.strong {\r\n            color: #67c23a;\r\n          }\r\n\r\n          &.very-strong {\r\n            color: #00c292;\r\n          }\r\n        }\r\n      }\r\n\r\n      .form-actions {\r\n        text-align: center;\r\n        margin-top: 40px;\r\n        padding-top: 30px;\r\n        border-top: 1px solid #ebeef5;\r\n\r\n        .el-button {\r\n          border-radius: 8px;\r\n          padding: 15px 40px;\r\n          font-weight: 600;\r\n          font-size: 16px;\r\n          margin: 0 10px;\r\n\r\n          &.el-button--primary {\r\n            background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n            border: none;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .update-password-container {\r\n    padding: 10px;\r\n\r\n    .password-card {\r\n      ::v-deep .el-card__body {\r\n        padding: 20px;\r\n      }\r\n\r\n      .user-info {\r\n        flex-direction: column;\r\n        text-align: center;\r\n\r\n        .user-details {\r\n          margin-left: 0;\r\n          margin-top: 15px;\r\n        }\r\n      }\r\n\r\n      .password-form {\r\n        .form-actions {\r\n          .el-button {\r\n            display: block;\r\n            width: 100%;\r\n            margin: 10px 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}