<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>简单轮播图测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../layui/css/layui.css">
    <style>
        body { padding: 20px; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .status { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
    </style>
</head>
<body>

<h1>轮播图功能测试</h1>

<div class="test-section">
    <h3>1. 默认轮播图测试</h3>
    <div class="layui-carousel" id="carousel1" style="width: 100%; height: 300px;">
        <div carousel-item>
            <div><img src="../../xznstatic/img/1.jpg" style="width: 100%;height: 100%;object-fit:cover;" alt="轮播图1"/></div>
            <div><img src="../../xznstatic/img/2.jpg" style="width: 100%;height: 100%;object-fit:cover;" alt="轮播图2"/></div>
            <div><img src="../../xznstatic/img/3.jpg" style="width: 100%;height: 100%;object-fit:cover;" alt="轮播图3"/></div>
        </div>
    </div>
    <div class="status">状态: 使用本地默认图片</div>
</div>

<div class="test-section">
    <h3>2. API数据轮播图测试</h3>
    <div class="layui-carousel" id="carousel2" style="width: 100%; height: 300px;">
        <div carousel-item id="carousel2-content">
            <div><img src="../../xznstatic/img/1.jpg" style="width: 100%;height: 100%;object-fit:cover;" alt="加载中..."/></div>
        </div>
    </div>
    <div class="status" id="status2">状态: 正在加载API数据...</div>
    <button class="layui-btn" onclick="loadApiData()">重新加载API数据</button>
</div>

<div class="test-section">
    <h3>3. 测试信息</h3>
    <div id="testInfo">
        <p><strong>API地址:</strong> http://localhost:8080/tiyuguan/config/list</p>
        <p><strong>测试时间:</strong> <span id="testTime"></span></p>
        <p><strong>浏览器:</strong> <span id="browserInfo"></span></p>
    </div>
    <div id="apiResponse" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border-radius: 3px; max-height: 200px; overflow-y: auto;">
        <strong>API响应数据:</strong><br>
        <pre id="responseData">等待加载...</pre>
    </div>
</div>

<script src="../../layui/layui.js"></script>
<script>
// 初始化页面信息
document.getElementById('testTime').textContent = new Date().toLocaleString();
document.getElementById('browserInfo').textContent = navigator.userAgent;

// 初始化默认轮播图
layui.use('carousel', function(){
    var carousel = layui.carousel;
    
    // 初始化第一个轮播图
    carousel.render({
        elem: '#carousel1',
        width: '100%',
        height: '300px',
        arrow: 'hover',
        anim: 'default',
        autoplay: true,
        interval: 3000,
        indicator: 'inside'
    });
    
    // 初始化第二个轮播图
    carousel.render({
        elem: '#carousel2',
        width: '100%',
        height: '300px',
        arrow: 'hover',
        anim: 'default',
        autoplay: true,
        interval: 3000,
        indicator: 'inside'
    });
});

// 加载API数据
function loadApiData() {
    const status = document.getElementById('status2');
    const responseData = document.getElementById('responseData');
    
    status.textContent = '状态: 正在请求API数据...';
    responseData.textContent = '请求中...';
    
    fetch('http://localhost:8080/tiyuguan/config/list?page=1&limit=10', {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('API响应数据:', data);
        responseData.textContent = JSON.stringify(data, null, 2);
        
        if (data && data.data && data.data.list && data.data.list.length > 0) {
            // 更新轮播图内容
            const content = document.getElementById('carousel2-content');
            content.innerHTML = '';
            
            data.data.list.forEach(item => {
                if (item.value && item.value.trim() !== '') {
                    const div = document.createElement('div');
                    div.innerHTML = `<img src="${item.value}" style="width: 100%;height: 100%;object-fit:cover;" alt="${item.name || '轮播图'}"/>`;
                    content.appendChild(div);
                }
            });
            
            status.textContent = `状态: 成功加载 ${data.data.list.length} 张轮播图`;
            
            // 重新初始化轮播图
            layui.use('carousel', function(){
                var carousel = layui.carousel;
                carousel.render({
                    elem: '#carousel2',
                    width: '100%',
                    height: '300px',
                    arrow: 'hover',
                    anim: 'default',
                    autoplay: true,
                    interval: 3000,
                    indicator: 'inside'
                });
            });
        } else {
            status.textContent = '状态: API返回数据为空，使用默认图片';
        }
    })
    .catch(error => {
        console.error('API请求失败:', error);
        status.textContent = `状态: API请求失败 - ${error.message}`;
        responseData.textContent = `错误: ${error.message}`;
    });
}

// 页面加载完成后自动加载API数据
window.onload = function() {
    setTimeout(loadApiData, 1000);
};
</script>

</body>
</html>
