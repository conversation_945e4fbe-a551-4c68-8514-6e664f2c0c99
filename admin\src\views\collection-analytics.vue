<template>
  <div class="collection-analytics">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">
        <i class="el-icon-data-analysis"></i>
        收藏数据分析
      </h1>
      <p class="page-description">分析用户收藏行为，了解热门场地和收藏趋势</p>
    </div>

    <!-- 核心指标卡片 -->
    <div class="metrics-section">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-card primary">
            <div class="metric-icon">
              <i class="el-icon-star-on"></i>
            </div>
            <div class="metric-content">
              <h3>{{ totalCollections }}</h3>
              <p>总收藏数</p>
              <span class="metric-trend up">
                <i class="el-icon-top"></i>
                +{{ todayIncrease }}
              </span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card success">
            <div class="metric-icon">
              <i class="el-icon-location"></i>
            </div>
            <div class="metric-content">
              <h3>{{ popularVenues }}</h3>
              <p>热门场地数</p>
              <span class="metric-trend">
                收藏>10次
              </span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card warning">
            <div class="metric-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="metric-content">
              <h3>{{ activeCollectors }}</h3>
              <p>活跃收藏用户</p>
              <span class="metric-trend">
                本月活跃
              </span>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="metric-card info">
            <div class="metric-icon">
              <i class="el-icon-pie-chart"></i>
            </div>
            <div class="metric-content">
              <h3>{{ avgCollectionsPerUser.toFixed(1) }}</h3>
              <p>人均收藏数</p>
              <span class="metric-trend">
                平均值
              </span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-row :gutter="20">
        <!-- 收藏趋势图 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="chart-header">
              <span>收藏趋势</span>
              <el-select v-model="trendPeriod" size="small" @change="loadTrendData">
                <el-option label="最近7天" value="7days"></el-option>
                <el-option label="最近30天" value="30days"></el-option>
                <el-option label="最近3个月" value="3months"></el-option>
              </el-select>
            </div>
            <div id="trendChart" class="chart-container"></div>
          </el-card>
        </el-col>
        
        <!-- 场地类型分布 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="chart-header">
              <span>场地类型收藏分布</span>
            </div>
            <div id="typeChart" class="chart-container"></div>
          </el-card>
        </el-col>
      </el-row>
      
      <el-row :gutter="20" style="margin-top: 20px;">
        <!-- 热门场地排行 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="chart-header">
              <span>热门场地排行</span>
            </div>
            <div class="ranking-list">
              <div 
                v-for="(venue, index) in topVenues" 
                :key="venue.id"
                class="ranking-item"
                :class="{ 'top-three': index < 3 }">
                <div class="ranking-number" :class="getRankingClass(index)">
                  {{ index + 1 }}
                </div>
                <div class="venue-info">
                  <img 
                    v-if="venue.photo" 
                    :src="venue.photo.split(',')[0]" 
                    class="venue-thumb"
                    @error="handleImageError">
                  <div class="venue-details">
                    <h4>{{ venue.name }}</h4>
                    <p>{{ venue.type }}</p>
                  </div>
                </div>
                <div class="collection-count">
                  <span class="count">{{ venue.collections }}</span>
                  <span class="label">收藏</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
        
        <!-- 用户收藏行为分析 -->
        <el-col :span="12">
          <el-card class="chart-card">
            <div slot="header" class="chart-header">
              <span>用户收藏行为</span>
            </div>
            <div class="behavior-stats">
              <div class="behavior-item">
                <div class="behavior-label">收藏高峰时段</div>
                <div class="behavior-value">14:00 - 16:00</div>
              </div>
              <div class="behavior-item">
                <div class="behavior-label">平均收藏间隔</div>
                <div class="behavior-value">2.3天</div>
              </div>
              <div class="behavior-item">
                <div class="behavior-label">取消收藏率</div>
                <div class="behavior-value">12.5%</div>
              </div>
              <div class="behavior-item">
                <div class="behavior-label">收藏转预约率</div>
                <div class="behavior-value">35.8%</div>
              </div>
            </div>
            
            <div class="time-distribution">
              <h4>收藏时间分布</h4>
              <div class="time-bars">
                <div 
                  v-for="hour in timeDistribution" 
                  :key="hour.time"
                  class="time-bar">
                  <div class="bar" :style="{ height: hour.percentage + '%' }"></div>
                  <span class="time-label">{{ hour.time }}</span>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-table-section">
      <el-card>
        <div slot="header" class="table-header">
          <span>收藏详细数据</span>
          <div class="table-actions">
            <el-button size="small" @click="exportData" icon="el-icon-download">
              导出数据
            </el-button>
            <el-button size="small" @click="refreshData" icon="el-icon-refresh">
              刷新
            </el-button>
          </div>
        </div>
        
        <el-table :data="detailData" stripe style="width: 100%">
          <el-table-column prop="venueName" label="场地名称" min-width="150"></el-table-column>
          <el-table-column prop="venueType" label="场地类型" width="120"></el-table-column>
          <el-table-column prop="totalCollections" label="总收藏数" width="100" sortable></el-table-column>
          <el-table-column prop="todayCollections" label="今日新增" width="100" sortable></el-table-column>
          <el-table-column prop="weekCollections" label="本周新增" width="100" sortable></el-table-column>
          <el-table-column prop="conversionRate" label="转预约率" width="100" sortable>
            <template slot-scope="scope">
              {{ scope.row.conversionRate }}%
            </template>
          </el-table-column>
          <el-table-column prop="avgRating" label="平均评分" width="100" sortable>
            <template slot-scope="scope">
              <el-rate 
                v-model="scope.row.avgRating" 
                disabled 
                show-score 
                text-color="#ff9900"
                score-template="{value}">
              </el-rate>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollectionAnalytics',
  data() {
    return {
      // 核心指标
      totalCollections: 1256,
      todayIncrease: 23,
      popularVenues: 15,
      activeCollectors: 89,
      avgCollectionsPerUser: 4.2,
      
      // 图表配置
      trendPeriod: '7days',
      
      // 热门场地数据
      topVenues: [
        {
          id: 1,
          name: '中央体育馆篮球场',
          type: '篮球场',
          photo: '/static/images/basketball.jpg',
          collections: 156
        },
        {
          id: 2,
          name: '游泳馆标准池',
          type: '游泳池',
          photo: '/static/images/swimming.jpg',
          collections: 134
        },
        {
          id: 3,
          name: '网球场A区',
          type: '网球场',
          photo: '/static/images/tennis.jpg',
          collections: 98
        },
        {
          id: 4,
          name: '羽毛球馆1号场',
          type: '羽毛球场',
          photo: '/static/images/badminton.jpg',
          collections: 87
        },
        {
          id: 5,
          name: '足球场草坪',
          type: '足球场',
          photo: '/static/images/football.jpg',
          collections: 76
        }
      ],
      
      // 时间分布数据
      timeDistribution: [
        { time: '6:00', percentage: 5 },
        { time: '8:00', percentage: 15 },
        { time: '10:00', percentage: 25 },
        { time: '12:00', percentage: 35 },
        { time: '14:00', percentage: 85 },
        { time: '16:00', percentage: 90 },
        { time: '18:00', percentage: 70 },
        { time: '20:00', percentage: 45 },
        { time: '22:00', percentage: 20 }
      ],
      
      // 详细数据
      detailData: [
        {
          venueName: '中央体育馆篮球场',
          venueType: '篮球场',
          totalCollections: 156,
          todayCollections: 8,
          weekCollections: 23,
          conversionRate: 42.3,
          avgRating: 4.5
        },
        {
          venueName: '游泳馆标准池',
          venueType: '游泳池',
          totalCollections: 134,
          todayCollections: 5,
          weekCollections: 18,
          conversionRate: 38.7,
          avgRating: 4.3
        },
        {
          venueName: '网球场A区',
          venueType: '网球场',
          totalCollections: 98,
          todayCollections: 3,
          weekCollections: 12,
          conversionRate: 35.2,
          avgRating: 4.2
        }
      ]
    }
  },
  
  mounted() {
    this.initCharts()
    this.loadAnalyticsData()
  },
  
  methods: {
    // 初始化图表
    initCharts() {
      this.$nextTick(() => {
        this.initTrendChart()
        this.initTypeChart()
      })
    },
    
    // 初始化趋势图
    initTrendChart() {
      // 这里应该使用真实的图表库如ECharts
      console.log('初始化收藏趋势图')
    },
    
    // 初始化类型分布图
    initTypeChart() {
      // 这里应该使用真实的图表库如ECharts
      console.log('初始化场地类型分布图')
    },
    
    // 加载趋势数据
    loadTrendData() {
      console.log('加载趋势数据:', this.trendPeriod)
    },
    
    // 加载分析数据
    loadAnalyticsData() {
      // 模拟数据加载
      console.log('加载收藏分析数据')
    },
    
    // 获取排名样式
    getRankingClass(index) {
      if (index === 0) return 'gold'
      if (index === 1) return 'silver'
      if (index === 2) return 'bronze'
      return 'normal'
    },
    
    // 导出数据
    exportData() {
      this.$message.info('导出功能开发中...')
    },
    
    // 刷新数据
    refreshData() {
      this.loadAnalyticsData()
      this.$message.success('数据已刷新')
    },
    
    // 图片错误处理
    handleImageError(event) {
      event.target.src = '/static/images/default-venue.png'
    }
  }
}
</script>

<style scoped>
.collection-analytics {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
}

.page-title i {
  margin-right: 12px;
  color: #409eff;
}

.page-description {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

/* 核心指标卡片 */
.metrics-section {
  margin-bottom: 24px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.metric-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.metric-card.primary::before {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.success::before {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-card.warning::before {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-card.info::before {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.metric-card.primary .metric-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.success .metric-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.metric-card.warning .metric-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.metric-card.info .metric-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.metric-icon i {
  font-size: 24px;
  color: white;
}

.metric-content h3 {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 4px 0;
  color: #303133;
}

.metric-content p {
  margin: 0 0 8px 0;
  color: #909399;
  font-size: 14px;
}

.metric-trend {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background: #f0f9ff;
  color: #0369a1;
}

.metric-trend.up {
  background: #f0fdf4;
  color: #166534;
}

.metric-trend i {
  margin-right: 2px;
}

/* 图表区域 */
.charts-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 400px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-radius: 12px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
  font-size: 14px;
}

/* 排行榜 */
.ranking-list {
  max-height: 320px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.3s ease;
}

.ranking-item:hover {
  background-color: #f8f9fa;
}

.ranking-item.top-three {
  background: linear-gradient(135deg, #fff7ed 0%, #fef3c7 100%);
  border-radius: 8px;
  margin-bottom: 8px;
  padding: 16px 12px;
}

.ranking-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  margin-right: 12px;
  color: white;
  font-size: 14px;
}

.ranking-number.gold {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
}

.ranking-number.silver {
  background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
}

.ranking-number.bronze {
  background: linear-gradient(135deg, #cd7f32 0%, #b8860b 100%);
}

.ranking-number.normal {
  background: #e5e7eb;
  color: #6b7280;
}

.venue-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.venue-thumb {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  object-fit: cover;
  margin-right: 12px;
  border: 1px solid #ebeef5;
}

.venue-details h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.venue-details p {
  margin: 0;
  font-size: 12px;
  color: #909399;
}

.collection-count {
  text-align: center;
}

.collection-count .count {
  display: block;
  font-size: 18px;
  font-weight: 700;
  color: #f56c6c;
}

.collection-count .label {
  font-size: 12px;
  color: #909399;
}

/* 用户行为分析 */
.behavior-stats {
  margin-bottom: 24px;
}

.behavior-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.behavior-label {
  color: #606266;
  font-size: 14px;
}

.behavior-value {
  font-weight: 600;
  color: #303133;
  font-size: 16px;
}

.time-distribution h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.time-bars {
  display: flex;
  align-items: end;
  justify-content: space-between;
  height: 120px;
  padding: 0 8px;
}

.time-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.bar {
  width: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 2px 2px 0 0;
  margin-bottom: 8px;
  min-height: 4px;
  transition: all 0.3s ease;
}

.time-bar:hover .bar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.time-label {
  font-size: 12px;
  color: #909399;
}

/* 数据表格 */
.data-table-section {
  margin-bottom: 24px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .collection-analytics {
    padding: 12px;
  }

  .metrics-section .el-col {
    margin-bottom: 12px;
  }

  .charts-section .el-col {
    margin-bottom: 20px;
  }

  .metric-card {
    flex-direction: column;
    text-align: center;
  }

  .metric-icon {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .venue-info {
    flex-direction: column;
    align-items: flex-start;
  }

  .venue-thumb {
    margin-bottom: 8px;
  }
}
</style>
