{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCarousel.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCarousel.vue", "mtime": 1750589744900}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CmV4cG9ydCBkZWZhdWx0IHsKICBuYW1lOiAnSG9tZUNhcm91c2VsJywKICBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgY2Fyb3VzZWxEYXRhOiBbCiAgICAgICAgewogICAgICAgICAgdGl0bGU6ICfkvZPogrLppobnrqHnkIbns7vnu58nLAogICAgICAgICAgZGVzY3JpcHRpb246ICfnjrDku6PljJbnmoTkvZPogrLlnLrppobnrqHnkIbop6PlhrPmlrnmoYgnLAogICAgICAgICAgaW1hZ2U6ICcvdGl5dWd1YW4vaW1nL2ltZy9iYWNrLWltZy1iZy5qcGcnCiAgICAgICAgfSwKICAgICAgICB7CiAgICAgICAgICB0aXRsZTogJ+WcuuWcsOmihOe6picsCiAgICAgICAgICBkZXNjcmlwdGlvbjogJ+S+v+aNt+eahOWcqOe6v+WcuuWcsOmihOe6puacjeWKoScsCiAgICAgICAgICBpbWFnZTogJy90aXl1Z3Vhbi9pbWcvaW1nL2JhY2staW1nLWJnLmpwZycKICAgICAgICB9LAogICAgICAgIHsKICAgICAgICAgIHRpdGxlOiAn5pm66IO9566h55CGJywKICAgICAgICAgIGRlc2NyaXB0aW9uOiAn6auY5pWI55qE5Zy66aaG6L+Q6JCl566h55CG5bmz5Y+wJywKICAgICAgICAgIGltYWdlOiAnL3RpeXVndWFuL2ltZy9pbWcvYmFjay1pbWctYmcuanBnJwogICAgICAgIH0KICAgICAgXQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMubG9hZENhcm91c2VsRGF0YSgpCiAgfSwKICBtZXRob2RzOiB7CiAgICBsb2FkQ2Fyb3VzZWxEYXRhKCkgewogICAgICAvLyDku47lkI7nq6/ojrflj5bova7mkq3lm77mlbDmja4KICAgICAgdGhpcy4kaHR0cCh7CiAgICAgICAgdXJsOiAnY29uZmlnL2xpc3QnLAogICAgICAgIG1ldGhvZDogJ2dldCcsCiAgICAgICAgcGFyYW1zOiB7CiAgICAgICAgICBwYWdlOiAxLAogICAgICAgICAgbGltaXQ6IDUKICAgICAgICB9CiAgICAgIH0pLnRoZW4oKHsgZGF0YSB9KSA9PiB7CiAgICAgICAgaWYgKGRhdGEgJiYgZGF0YS5jb2RlID09PSAwICYmIGRhdGEubGlzdCAmJiBkYXRhLmxpc3QubGVuZ3RoID4gMCkgewogICAgICAgICAgdGhpcy5jYXJvdXNlbERhdGEgPSBkYXRhLmxpc3QubWFwKChpdGVtLCBpbmRleCkgPT4gKHsKICAgICAgICAgICAgdGl0bGU6IGl0ZW0ubmFtZSB8fCBg6L2u5pKt5Zu+ICR7aW5kZXggKyAxfWAsCiAgICAgICAgICAgIGRlc2NyaXB0aW9uOiBpdGVtLnZhbHVlID8gJ+eyvuW9qeWGheWuueetieaCqOWPkeeOsCcgOiAn5qyi6L+O5L2/55So5L2T6IKy6aaG566h55CG57O757ufJywKICAgICAgICAgICAgaW1hZ2U6IGl0ZW0udmFsdWUgfHwgJy90aXl1Z3Vhbi9pbWcvaW1nL2JhY2staW1nLWJnLmpwZycKICAgICAgICAgIH0pKQogICAgICAgIH0KICAgICAgfSkuY2F0Y2goZXJyID0+IHsKICAgICAgICBjb25zb2xlLmxvZygn6I635Y+W6L2u5pKt5Zu+5pWw5o2u5aSx6LSlOicsIGVycikKICAgICAgfSkKICAgIH0KICB9Cn0K"}, {"version": 3, "sources": ["HomeCarousel.vue"], "names": [], "mappings": ";AAgBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "HomeCarousel.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\n  <div class=\"carousel-container\">\n    <el-carousel :interval=\"4000\" type=\"card\" height=\"300px\" indicator-position=\"outside\">\n      <el-carousel-item v-for=\"(item, index) in carouselData\" :key=\"index\">\n        <div class=\"carousel-item\" :style=\"{ backgroundImage: `url(${item.image})` }\">\n          <div class=\"carousel-content\">\n            <h3>{{ item.title }}</h3>\n            <p>{{ item.description }}</p>\n          </div>\n        </div>\n      </el-carousel-item>\n    </el-carousel>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'HomeCarousel',\n  data() {\n    return {\n      carouselData: [\n        {\n          title: '体育馆管理系统',\n          description: '现代化的体育场馆管理解决方案',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        },\n        {\n          title: '场地预约',\n          description: '便捷的在线场地预约服务',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        },\n        {\n          title: '智能管理',\n          description: '高效的场馆运营管理平台',\n          image: '/tiyuguan/img/img/back-img-bg.jpg'\n        }\n      ]\n    }\n  },\n  mounted() {\n    this.loadCarouselData()\n  },\n  methods: {\n    loadCarouselData() {\n      // 从后端获取轮播图数据\n      this.$http({\n        url: 'config/list',\n        method: 'get',\n        params: {\n          page: 1,\n          limit: 5\n        }\n      }).then(({ data }) => {\n        if (data && data.code === 0 && data.list && data.list.length > 0) {\n          this.carouselData = data.list.map((item, index) => ({\n            title: item.name || `轮播图 ${index + 1}`,\n            description: item.value ? '精彩内容等您发现' : '欢迎使用体育馆管理系统',\n            image: item.value || '/tiyuguan/img/img/back-img-bg.jpg'\n          }))\n        }\n      }).catch(err => {\n        console.log('获取轮播图数据失败:', err)\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.carousel-container {\n  margin-bottom: 20px;\n  \n  .carousel-item {\n    width: 100%;\n    height: 300px;\n    background-size: cover;\n    background-position: center;\n    background-repeat: no-repeat;\n    border-radius: 8px;\n    position: relative;\n    overflow: hidden;\n    \n    &::before {\n      content: '';\n      position: absolute;\n      top: 0;\n      left: 0;\n      right: 0;\n      bottom: 0;\n      background: linear-gradient(45deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));\n      z-index: 1;\n    }\n    \n    .carousel-content {\n      position: absolute;\n      bottom: 30px;\n      left: 30px;\n      color: white;\n      z-index: 2;\n      \n      h3 {\n        font-size: 24px;\n        font-weight: bold;\n        margin: 0 0 10px 0;\n        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);\n      }\n      \n      p {\n        font-size: 16px;\n        margin: 0;\n        opacity: 0.9;\n        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\n      }\n    }\n  }\n}\n\n::v-deep .el-carousel__indicator {\n  .el-carousel__button {\n    background-color: rgba(255, 255, 255, 0.5);\n    \n    &.is-active {\n      background-color: #00c292;\n    }\n  }\n}\n</style>\n"]}]}