{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\components\\BookingDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\components\\BookingDialog.vue", "mtime": 1750596787915}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "venue", "Object", "data", "dialogVisible", "submitting", "bookingForm", "bookingDate", "timeSlot", "peopleCount", "phone", "remark", "rules", "required", "message", "trigger", "pattern", "timeSlots", "label", "value", "disabled", "datePickerOptions", "disabledDate", "time", "getTime", "Date", "now", "computed", "totalPrice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "watch", "val", "initForm", "$emit", "methods", "_this", "$http", "url", "concat", "$storage", "get", "method", "then", "_ref", "code", "yonghuPhone", "submitBooking", "_this2", "console", "log", "role", "userId", "sessionTable", "$message", "warning", "$router", "push", "handleClose", "$refs", "validate", "valid", "bookingData", "changdiId", "id", "changdiOrderTruePrice", "changdiOrderTypes", "s<PERSON><PERSON><PERSON><PERSON>", "buyTime", "formatDate", "insertTime", "toISOString", "slice", "replace", "yonghuId", "_ref2", "success", "error", "msg", "catch", "resetFields", "date", "d", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate"], "sources": ["src/views/components/BookingDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"场地预约\"\n    :visible.sync=\"dialogVisible\"\n    width=\"600px\"\n    :before-close=\"handleClose\"\n    class=\"booking-dialog\">\n    \n    <div class=\"booking-content\">\n      <!-- 场地信息 -->\n      <div class=\"venue-summary\">\n        <div class=\"venue-image\">\n          <img :src=\"venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" :alt=\"venue.changdiName\">\n        </div>\n        <div class=\"venue-info\">\n          <h3>{{ venue.changdiName }}</h3>\n          <div class=\"venue-details\">\n            <div class=\"detail-item\">\n              <i class=\"el-icon-location\"></i>\n              <span>{{ venue.changdiValue }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <i class=\"el-icon-star-on\"></i>\n              <span>{{ venue.banquanValue }}</span>\n            </div>\n          </div>\n          <div class=\"venue-price\">\n            <span class=\"current-price\">¥{{ venue.changdiNewMoney }}</span>\n            <span v-if=\"venue.changdiOldMoney !== venue.changdiNewMoney\" class=\"original-price\">\n              ¥{{ venue.changdiOldMoney }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 预约表单 -->\n      <el-form :model=\"bookingForm\" :rules=\"rules\" ref=\"bookingForm\" label-width=\"100px\">\n        <el-form-item label=\"预约日期\" prop=\"bookingDate\">\n          <el-date-picker\n            v-model=\"bookingForm.bookingDate\"\n            type=\"date\"\n            placeholder=\"选择预约日期\"\n            :picker-options=\"datePickerOptions\"\n            style=\"width: 100%\">\n          </el-date-picker>\n        </el-form-item>\n        \n        <el-form-item label=\"时间段\" prop=\"timeSlot\">\n          <el-select v-model=\"bookingForm.timeSlot\" placeholder=\"请选择时间段\" style=\"width: 100%\">\n            <el-option\n              v-for=\"slot in timeSlots\"\n              :key=\"slot.value\"\n              :label=\"slot.label\"\n              :value=\"slot.value\"\n              :disabled=\"slot.disabled\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"预约人数\" prop=\"peopleCount\">\n          <el-input-number\n            v-model=\"bookingForm.peopleCount\"\n            :min=\"1\"\n            :max=\"20\"\n            style=\"width: 100%\">\n          </el-input-number>\n        </el-form-item>\n        \n        <el-form-item label=\"联系电话\" prop=\"phone\">\n          <el-input v-model=\"bookingForm.phone\" placeholder=\"请输入联系电话\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"备注信息\">\n          <el-input\n            v-model=\"bookingForm.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入备注信息（选填）\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 价格信息 -->\n      <div class=\"price-summary\">\n        <div class=\"price-item\">\n          <span>场地费用：</span>\n          <span>¥{{ venue.changdiNewMoney }}</span>\n        </div>\n        <div class=\"price-item\">\n          <span>预约人数：</span>\n          <span>{{ bookingForm.peopleCount }}人</span>\n        </div>\n        <div class=\"price-item total\">\n          <span>总计费用：</span>\n          <span class=\"total-price\">¥{{ totalPrice }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取消</el-button>\n      <el-button type=\"primary\" @click=\"submitBooking\" :loading=\"submitting\">\n        确认预约\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nexport default {\n  name: 'BookingDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    venue: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  data() {\n    return {\n      dialogVisible: false,\n      submitting: false,\n      \n      bookingForm: {\n        bookingDate: '',\n        timeSlot: '',\n        peopleCount: 1,\n        phone: '',\n        remark: ''\n      },\n      \n      rules: {\n        bookingDate: [\n          { required: true, message: '请选择预约日期', trigger: 'change' }\n        ],\n        timeSlot: [\n          { required: true, message: '请选择时间段', trigger: 'change' }\n        ],\n        peopleCount: [\n          { required: true, message: '请输入预约人数', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入联系电话', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\n        ]\n      },\n      \n      timeSlots: [\n        { label: '08:00-10:00', value: '08:00-10:00', disabled: false },\n        { label: '10:00-12:00', value: '10:00-12:00', disabled: false },\n        { label: '12:00-14:00', value: '12:00-14:00', disabled: false },\n        { label: '14:00-16:00', value: '14:00-16:00', disabled: false },\n        { label: '16:00-18:00', value: '16:00-18:00', disabled: false },\n        { label: '18:00-20:00', value: '18:00-20:00', disabled: false },\n        { label: '20:00-22:00', value: '20:00-22:00', disabled: false }\n      ],\n      \n      datePickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期\n        }\n      }\n    }\n  },\n  \n  computed: {\n    totalPrice() {\n      return (this.venue.changdiNewMoney || 0) * (this.bookingForm.peopleCount || 1)\n    }\n  },\n  \n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.initForm()\n      }\n    },\n    \n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  \n  methods: {\n    // 初始化表单\n    initForm() {\n      // 获取用户信息\n      this.$http({\n        url: `${this.$storage.get(\"sessionTable\")}/session`,\n        method: \"get\"\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.bookingForm.phone = data.data.yonghuPhone || ''\n        }\n      })\n      \n      // 重置表单\n      this.bookingForm = {\n        bookingDate: '',\n        timeSlot: '',\n        peopleCount: 1,\n        phone: this.bookingForm.phone || '',\n        remark: ''\n      }\n    },\n    \n    // 提交预约\n    submitBooking() {\n      console.log('开始提交预约')\n\n      // 检查登录状态\n      const role = this.$storage.get('role')\n      const userId = this.$storage.get('userId')\n      const sessionTable = this.$storage.get('sessionTable')\n\n      console.log('用户信息:', { role, userId, sessionTable })\n\n      if (!role) {\n        this.$message.warning('请先登录账户')\n        this.$router.push('/login')\n        return\n      }\n\n      if (role !== '用户') {\n        this.$message.warning('只有用户可以预约场地，当前角色：' + role)\n        this.handleClose()\n        return\n      }\n\n      this.$refs.bookingForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n\n          const bookingData = {\n            changdiId: this.venue.id,\n            changdiOrderTruePrice: this.totalPrice,\n            changdiOrderTypes: 1, // 预约状态\n            shijianduan: this.bookingForm.timeSlot,\n            buyTime: this.formatDate(this.bookingForm.bookingDate),\n            insertTime: new Date().toISOString().slice(0, 19).replace('T', ' '),\n            yonghuId: userId || 1 // 用户ID\n          }\n\n          console.log('预约数据:', bookingData)\n\n          this.$http({\n            url: 'changdiOrder/save',\n            method: 'post',\n            data: bookingData\n          }).then(({ data }) => {\n            console.log('预约响应:', data)\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('预约成功！')\n              this.$emit('booking-success')\n              this.handleClose()\n            } else {\n              this.$message.error(data.msg || '预约失败，请稍后重试')\n              console.error('预约失败:', data)\n            }\n          }).catch((error) => {\n            console.error('预约请求错误:', error)\n            this.submitting = false\n            this.$message.error('网络错误，请稍后重试')\n          })\n        } else {\n          console.log('表单验证失败')\n        }\n      })\n    },\n    \n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n      this.$refs.bookingForm.resetFields()\n    },\n\n    // 格式化日期\n    formatDate(date) {\n      if (!date) return ''\n      const d = new Date(date)\n      const year = d.getFullYear()\n      const month = String(d.getMonth() + 1).padStart(2, '0')\n      const day = String(d.getDate()).padStart(2, '0')\n      return `${year}-${month}-${day}`\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .booking-dialog {\n  .el-dialog__header {\n    background: linear-gradient(45deg, #00c292, #00a085);\n    color: white;\n    padding: 20px 30px;\n    \n    .el-dialog__title {\n      color: white;\n      font-size: 18px;\n      font-weight: 600;\n    }\n    \n    .el-dialog__close {\n      color: white;\n      font-size: 20px;\n    }\n  }\n  \n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n.booking-content {\n  .venue-summary {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 30px;\n    padding: 20px;\n    background: #f8fffe;\n    border-radius: 12px;\n    border: 1px solid rgba(0, 194, 146, 0.1);\n    \n    .venue-image {\n      flex: 0 0 120px;\n      \n      img {\n        width: 120px;\n        height: 90px;\n        object-fit: cover;\n        border-radius: 8px;\n      }\n    }\n    \n    .venue-info {\n      flex: 1;\n      \n      h3 {\n        font-size: 20px;\n        color: #2c3e50;\n        margin: 0 0 15px 0;\n      }\n      \n      .venue-details {\n        margin-bottom: 15px;\n        \n        .detail-item {\n          display: flex;\n          align-items: center;\n          margin-bottom: 5px;\n          color: #666;\n          font-size: 14px;\n          \n          i {\n            color: #00c292;\n            margin-right: 8px;\n            width: 16px;\n          }\n        }\n      }\n      \n      .venue-price {\n        .current-price {\n          font-size: 20px;\n          font-weight: 700;\n          color: #00c292;\n        }\n        \n        .original-price {\n          font-size: 14px;\n          color: #999;\n          text-decoration: line-through;\n          margin-left: 10px;\n        }\n      }\n    }\n  }\n  \n  .price-summary {\n    margin-top: 30px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    \n    .price-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 10px;\n      \n      &.total {\n        border-top: 1px solid #ddd;\n        padding-top: 10px;\n        margin-top: 10px;\n        font-weight: 600;\n        \n        .total-price {\n          font-size: 18px;\n          color: #00c292;\n        }\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  \n  .el-button {\n    border-radius: 8px;\n    font-weight: 600;\n    \n    &.el-button--primary {\n      background: linear-gradient(45deg, #00c292, #00a085);\n      border: none;\n      \n      &:hover {\n        background: linear-gradient(45deg, #00a085, #008f75);\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;;;;AA6GA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EAEAG,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MAEAC,WAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,MAAA;MACA;MAEAC,KAAA;QACAL,WAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,WAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEAE,SAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,EACA;MAEAC,iBAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;EACA;EAEAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,aAAA3B,KAAA,CAAA4B,eAAA,eAAAvB,WAAA,CAAAG,WAAA;IACA;EACA;EAEAqB,KAAA;IACAjC,OAAA,WAAAA,QAAAkC,GAAA;MACA,KAAA3B,aAAA,GAAA2B,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,QAAA;MACA;IACA;IAEA5B,aAAA,WAAAA,cAAA2B,GAAA;MACA,KAAAE,KAAA,mBAAAF,GAAA;IACA;EACA;EAEAG,OAAA;IACA;IACAF,QAAA,WAAAA,SAAA;MAAA,IAAAG,KAAA;MACA;MACA,KAAAC,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAAC,QAAA,CAAAC,GAAA;QACAC,MAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAxC,IAAA,GAAAwC,IAAA,CAAAxC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAyC,IAAA;UACAT,KAAA,CAAA7B,WAAA,CAAAI,KAAA,GAAAP,IAAA,CAAAA,IAAA,CAAA0C,WAAA;QACA;MACA;;MAEA;MACA,KAAAvC,WAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA,OAAAJ,WAAA,CAAAI,KAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAmC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACAC,OAAA,CAAAC,GAAA;;MAEA;MACA,IAAAC,IAAA,QAAAX,QAAA,CAAAC,GAAA;MACA,IAAAW,MAAA,QAAAZ,QAAA,CAAAC,GAAA;MACA,IAAAY,YAAA,QAAAb,QAAA,CAAAC,GAAA;MAEAQ,OAAA,CAAAC,GAAA;QAAAC,IAAA,EAAAA,IAAA;QAAAC,MAAA,EAAAA,MAAA;QAAAC,YAAA,EAAAA;MAAA;MAEA,KAAAF,IAAA;QACA,KAAAG,QAAA,CAAAC,OAAA;QACA,KAAAC,OAAA,CAAAC,IAAA;QACA;MACA;MAEA,IAAAN,IAAA;QACA,KAAAG,QAAA,CAAAC,OAAA,sBAAAJ,IAAA;QACA,KAAAO,WAAA;QACA;MACA;MAEA,KAAAC,KAAA,CAAApD,WAAA,CAAAqD,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAb,MAAA,CAAA1C,UAAA;UAEA,IAAAwD,WAAA;YACAC,SAAA,EAAAf,MAAA,CAAA9C,KAAA,CAAA8D,EAAA;YACAC,qBAAA,EAAAjB,MAAA,CAAAnB,UAAA;YACAqC,iBAAA;YAAA;YACAC,WAAA,EAAAnB,MAAA,CAAAzC,WAAA,CAAAE,QAAA;YACA2D,OAAA,EAAApB,MAAA,CAAAqB,UAAA,CAAArB,MAAA,CAAAzC,WAAA,CAAAC,WAAA;YACA8D,UAAA,MAAA5C,IAAA,GAAA6C,WAAA,GAAAC,KAAA,QAAAC,OAAA;YACAC,QAAA,EAAAtB,MAAA;UACA;UAEAH,OAAA,CAAAC,GAAA,UAAAY,WAAA;UAEAd,MAAA,CAAAX,KAAA;YACAC,GAAA;YACAI,MAAA;YACAtC,IAAA,EAAA0D;UACA,GAAAnB,IAAA,WAAAgC,KAAA;YAAA,IAAAvE,IAAA,GAAAuE,KAAA,CAAAvE,IAAA;YACA6C,OAAA,CAAAC,GAAA,UAAA9C,IAAA;YACA4C,MAAA,CAAA1C,UAAA;YACA,IAAAF,IAAA,IAAAA,IAAA,CAAAyC,IAAA;cACAG,MAAA,CAAAM,QAAA,CAAAsB,OAAA;cACA5B,MAAA,CAAAd,KAAA;cACAc,MAAA,CAAAU,WAAA;YACA;cACAV,MAAA,CAAAM,QAAA,CAAAuB,KAAA,CAAAzE,IAAA,CAAA0E,GAAA;cACA7B,OAAA,CAAA4B,KAAA,UAAAzE,IAAA;YACA;UACA,GAAA2E,KAAA,WAAAF,KAAA;YACA5B,OAAA,CAAA4B,KAAA,YAAAA,KAAA;YACA7B,MAAA,CAAA1C,UAAA;YACA0C,MAAA,CAAAM,QAAA,CAAAuB,KAAA;UACA;QACA;UACA5B,OAAA,CAAAC,GAAA;QACA;MACA;IACA;IAEA;IACAQ,WAAA,WAAAA,YAAA;MACA,KAAArD,aAAA;MACA,KAAAsD,KAAA,CAAApD,WAAA,CAAAyE,WAAA;IACA;IAEA;IACAX,UAAA,WAAAA,WAAAY,IAAA;MACA,KAAAA,IAAA;MACA,IAAAC,CAAA,OAAAxD,IAAA,CAAAuD,IAAA;MACA,IAAAE,IAAA,GAAAD,CAAA,CAAAE,WAAA;MACA,IAAAC,KAAA,GAAAC,MAAA,CAAAJ,CAAA,CAAAK,QAAA,QAAAC,QAAA;MACA,IAAAC,GAAA,GAAAH,MAAA,CAAAJ,CAAA,CAAAQ,OAAA,IAAAF,QAAA;MACA,UAAAjD,MAAA,CAAA4C,IAAA,OAAA5C,MAAA,CAAA8C,KAAA,OAAA9C,MAAA,CAAAkD,GAAA;IACA;EACA;AACA", "ignoreList": []}]}