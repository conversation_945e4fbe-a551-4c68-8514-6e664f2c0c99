{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\components\\BookingDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\components\\BookingDialog.vue", "mtime": 1750595523594}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "visible", "type", "Boolean", "default", "venue", "Object", "data", "dialogVisible", "submitting", "bookingForm", "bookingDate", "timeSlot", "peopleCount", "phone", "remark", "rules", "required", "message", "trigger", "pattern", "timeSlots", "label", "value", "disabled", "datePickerOptions", "disabledDate", "time", "getTime", "Date", "now", "computed", "totalPrice", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "watch", "val", "initForm", "$emit", "methods", "_this", "$http", "url", "concat", "$storage", "get", "method", "then", "_ref", "code", "yonghuPhone", "submitBooking", "_this2", "$refs", "validate", "valid", "bookingData", "changdiId", "id", "changdiOrderTruePrice", "changdiOrderTypes", "s<PERSON><PERSON><PERSON><PERSON>", "buyTime", "insertTime", "toISOString", "slice", "replace", "_ref2", "$message", "success", "handleClose", "error", "msg", "catch", "resetFields"], "sources": ["src/views/components/BookingDialog.vue"], "sourcesContent": ["<template>\n  <el-dialog\n    title=\"场地预约\"\n    :visible.sync=\"dialogVisible\"\n    width=\"600px\"\n    :before-close=\"handleClose\"\n    class=\"booking-dialog\">\n    \n    <div class=\"booking-content\">\n      <!-- 场地信息 -->\n      <div class=\"venue-summary\">\n        <div class=\"venue-image\">\n          <img :src=\"venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" :alt=\"venue.changdiName\">\n        </div>\n        <div class=\"venue-info\">\n          <h3>{{ venue.changdiName }}</h3>\n          <div class=\"venue-details\">\n            <div class=\"detail-item\">\n              <i class=\"el-icon-location\"></i>\n              <span>{{ venue.changdiValue }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <i class=\"el-icon-star-on\"></i>\n              <span>{{ venue.banquanValue }}</span>\n            </div>\n          </div>\n          <div class=\"venue-price\">\n            <span class=\"current-price\">¥{{ venue.changdiNewMoney }}</span>\n            <span v-if=\"venue.changdiOldMoney !== venue.changdiNewMoney\" class=\"original-price\">\n              ¥{{ venue.changdiOldMoney }}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 预约表单 -->\n      <el-form :model=\"bookingForm\" :rules=\"rules\" ref=\"bookingForm\" label-width=\"100px\">\n        <el-form-item label=\"预约日期\" prop=\"bookingDate\">\n          <el-date-picker\n            v-model=\"bookingForm.bookingDate\"\n            type=\"date\"\n            placeholder=\"选择预约日期\"\n            :picker-options=\"datePickerOptions\"\n            style=\"width: 100%\">\n          </el-date-picker>\n        </el-form-item>\n        \n        <el-form-item label=\"时间段\" prop=\"timeSlot\">\n          <el-select v-model=\"bookingForm.timeSlot\" placeholder=\"请选择时间段\" style=\"width: 100%\">\n            <el-option\n              v-for=\"slot in timeSlots\"\n              :key=\"slot.value\"\n              :label=\"slot.label\"\n              :value=\"slot.value\"\n              :disabled=\"slot.disabled\">\n            </el-option>\n          </el-select>\n        </el-form-item>\n        \n        <el-form-item label=\"预约人数\" prop=\"peopleCount\">\n          <el-input-number\n            v-model=\"bookingForm.peopleCount\"\n            :min=\"1\"\n            :max=\"20\"\n            style=\"width: 100%\">\n          </el-input-number>\n        </el-form-item>\n        \n        <el-form-item label=\"联系电话\" prop=\"phone\">\n          <el-input v-model=\"bookingForm.phone\" placeholder=\"请输入联系电话\"></el-input>\n        </el-form-item>\n        \n        <el-form-item label=\"备注信息\">\n          <el-input\n            v-model=\"bookingForm.remark\"\n            type=\"textarea\"\n            :rows=\"3\"\n            placeholder=\"请输入备注信息（选填）\">\n          </el-input>\n        </el-form-item>\n      </el-form>\n\n      <!-- 价格信息 -->\n      <div class=\"price-summary\">\n        <div class=\"price-item\">\n          <span>场地费用：</span>\n          <span>¥{{ venue.changdiNewMoney }}</span>\n        </div>\n        <div class=\"price-item\">\n          <span>预约人数：</span>\n          <span>{{ bookingForm.peopleCount }}人</span>\n        </div>\n        <div class=\"price-item total\">\n          <span>总计费用：</span>\n          <span class=\"total-price\">¥{{ totalPrice }}</span>\n        </div>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">取消</el-button>\n      <el-button type=\"primary\" @click=\"submitBooking\" :loading=\"submitting\">\n        确认预约\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nexport default {\n  name: 'BookingDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    venue: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  data() {\n    return {\n      dialogVisible: false,\n      submitting: false,\n      \n      bookingForm: {\n        bookingDate: '',\n        timeSlot: '',\n        peopleCount: 1,\n        phone: '',\n        remark: ''\n      },\n      \n      rules: {\n        bookingDate: [\n          { required: true, message: '请选择预约日期', trigger: 'change' }\n        ],\n        timeSlot: [\n          { required: true, message: '请选择时间段', trigger: 'change' }\n        ],\n        peopleCount: [\n          { required: true, message: '请输入预约人数', trigger: 'blur' }\n        ],\n        phone: [\n          { required: true, message: '请输入联系电话', trigger: 'blur' },\n          { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }\n        ]\n      },\n      \n      timeSlots: [\n        { label: '08:00-10:00', value: '08:00-10:00', disabled: false },\n        { label: '10:00-12:00', value: '10:00-12:00', disabled: false },\n        { label: '12:00-14:00', value: '12:00-14:00', disabled: false },\n        { label: '14:00-16:00', value: '14:00-16:00', disabled: false },\n        { label: '16:00-18:00', value: '16:00-18:00', disabled: false },\n        { label: '18:00-20:00', value: '18:00-20:00', disabled: false },\n        { label: '20:00-22:00', value: '20:00-22:00', disabled: false }\n      ],\n      \n      datePickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7; // 不能选择今天之前的日期\n        }\n      }\n    }\n  },\n  \n  computed: {\n    totalPrice() {\n      return (this.venue.changdiNewMoney || 0) * (this.bookingForm.peopleCount || 1)\n    }\n  },\n  \n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.initForm()\n      }\n    },\n    \n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  \n  methods: {\n    // 初始化表单\n    initForm() {\n      // 获取用户信息\n      this.$http({\n        url: `${this.$storage.get(\"sessionTable\")}/session`,\n        method: \"get\"\n      }).then(({ data }) => {\n        if (data && data.code === 0) {\n          this.bookingForm.phone = data.data.yonghuPhone || ''\n        }\n      })\n      \n      // 重置表单\n      this.bookingForm = {\n        bookingDate: '',\n        timeSlot: '',\n        peopleCount: 1,\n        phone: this.bookingForm.phone || '',\n        remark: ''\n      }\n    },\n    \n    // 提交预约\n    submitBooking() {\n      this.$refs.bookingForm.validate((valid) => {\n        if (valid) {\n          this.submitting = true\n          \n          const bookingData = {\n            changdiId: this.venue.id,\n            changdiOrderTruePrice: this.totalPrice,\n            changdiOrderTypes: 1, // 预约状态\n            shijianduan: this.bookingForm.timeSlot,\n            buyTime: this.bookingForm.bookingDate,\n            insertTime: new Date().toISOString().slice(0, 19).replace('T', ' ')\n          }\n          \n          this.$http({\n            url: 'changdiOrder/save',\n            method: 'post',\n            data: bookingData\n          }).then(({ data }) => {\n            this.submitting = false\n            if (data && data.code === 0) {\n              this.$message.success('预约成功！')\n              this.$emit('booking-success')\n              this.handleClose()\n            } else {\n              this.$message.error(data.msg || '预约失败')\n            }\n          }).catch(() => {\n            this.submitting = false\n            this.$message.error('网络错误，请稍后重试')\n          })\n        }\n      })\n    },\n    \n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n      this.$refs.bookingForm.resetFields()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .booking-dialog {\n  .el-dialog__header {\n    background: linear-gradient(45deg, #00c292, #00a085);\n    color: white;\n    padding: 20px 30px;\n    \n    .el-dialog__title {\n      color: white;\n      font-size: 18px;\n      font-weight: 600;\n    }\n    \n    .el-dialog__close {\n      color: white;\n      font-size: 20px;\n    }\n  }\n  \n  .el-dialog__body {\n    padding: 30px;\n  }\n}\n\n.booking-content {\n  .venue-summary {\n    display: flex;\n    gap: 20px;\n    margin-bottom: 30px;\n    padding: 20px;\n    background: #f8fffe;\n    border-radius: 12px;\n    border: 1px solid rgba(0, 194, 146, 0.1);\n    \n    .venue-image {\n      flex: 0 0 120px;\n      \n      img {\n        width: 120px;\n        height: 90px;\n        object-fit: cover;\n        border-radius: 8px;\n      }\n    }\n    \n    .venue-info {\n      flex: 1;\n      \n      h3 {\n        font-size: 20px;\n        color: #2c3e50;\n        margin: 0 0 15px 0;\n      }\n      \n      .venue-details {\n        margin-bottom: 15px;\n        \n        .detail-item {\n          display: flex;\n          align-items: center;\n          margin-bottom: 5px;\n          color: #666;\n          font-size: 14px;\n          \n          i {\n            color: #00c292;\n            margin-right: 8px;\n            width: 16px;\n          }\n        }\n      }\n      \n      .venue-price {\n        .current-price {\n          font-size: 20px;\n          font-weight: 700;\n          color: #00c292;\n        }\n        \n        .original-price {\n          font-size: 14px;\n          color: #999;\n          text-decoration: line-through;\n          margin-left: 10px;\n        }\n      }\n    }\n  }\n  \n  .price-summary {\n    margin-top: 30px;\n    padding: 20px;\n    background: #f8f9fa;\n    border-radius: 8px;\n    \n    .price-item {\n      display: flex;\n      justify-content: space-between;\n      margin-bottom: 10px;\n      \n      &.total {\n        border-top: 1px solid #ddd;\n        padding-top: 10px;\n        margin-top: 10px;\n        font-weight: 600;\n        \n        .total-price {\n          font-size: 18px;\n          color: #00c292;\n        }\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  \n  .el-button {\n    border-radius: 8px;\n    font-weight: 600;\n    \n    &.el-button--primary {\n      background: linear-gradient(45deg, #00c292, #00a085);\n      border: none;\n      \n      &:hover {\n        background: linear-gradient(45deg, #00a085, #008f75);\n      }\n    }\n  }\n}\n</style>\n"], "mappings": ";;;;AA6GA;EACAA,IAAA;EACAC,KAAA;IACAC,OAAA;MACAC,IAAA,EAAAC,OAAA;MACAC,OAAA;IACA;IACAC,KAAA;MACAH,IAAA,EAAAI,MAAA;MACAF,OAAA,WAAAA,SAAA;QAAA;MAAA;IACA;EACA;EAEAG,IAAA,WAAAA,KAAA;IACA;MACAC,aAAA;MACAC,UAAA;MAEAC,WAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA;QACAC,MAAA;MACA;MAEAC,KAAA;QACAL,WAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAP,QAAA,GACA;UAAAK,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAN,WAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,KAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,GACA;UAAAC,OAAA;UAAAF,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEAE,SAAA,GACA;QAAAC,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,GACA;QAAAF,KAAA;QAAAC,KAAA;QAAAC,QAAA;MAAA,EACA;MAEAC,iBAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;IACA;EACA;EAEAC,QAAA;IACAC,UAAA,WAAAA,WAAA;MACA,aAAA3B,KAAA,CAAA4B,eAAA,eAAAvB,WAAA,CAAAG,WAAA;IACA;EACA;EAEAqB,KAAA;IACAjC,OAAA,WAAAA,QAAAkC,GAAA;MACA,KAAA3B,aAAA,GAAA2B,GAAA;MACA,IAAAA,GAAA;QACA,KAAAC,QAAA;MACA;IACA;IAEA5B,aAAA,WAAAA,cAAA2B,GAAA;MACA,KAAAE,KAAA,mBAAAF,GAAA;IACA;EACA;EAEAG,OAAA;IACA;IACAF,QAAA,WAAAA,SAAA;MAAA,IAAAG,KAAA;MACA;MACA,KAAAC,KAAA;QACAC,GAAA,KAAAC,MAAA,MAAAC,QAAA,CAAAC,GAAA;QACAC,MAAA;MACA,GAAAC,IAAA,WAAAC,IAAA;QAAA,IAAAxC,IAAA,GAAAwC,IAAA,CAAAxC,IAAA;QACA,IAAAA,IAAA,IAAAA,IAAA,CAAAyC,IAAA;UACAT,KAAA,CAAA7B,WAAA,CAAAI,KAAA,GAAAP,IAAA,CAAAA,IAAA,CAAA0C,WAAA;QACA;MACA;;MAEA;MACA,KAAAvC,WAAA;QACAC,WAAA;QACAC,QAAA;QACAC,WAAA;QACAC,KAAA,OAAAJ,WAAA,CAAAI,KAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAmC,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA1C,WAAA,CAAA2C,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,MAAA,CAAA1C,UAAA;UAEA,IAAA8C,WAAA;YACAC,SAAA,EAAAL,MAAA,CAAA9C,KAAA,CAAAoD,EAAA;YACAC,qBAAA,EAAAP,MAAA,CAAAnB,UAAA;YACA2B,iBAAA;YAAA;YACAC,WAAA,EAAAT,MAAA,CAAAzC,WAAA,CAAAE,QAAA;YACAiD,OAAA,EAAAV,MAAA,CAAAzC,WAAA,CAAAC,WAAA;YACAmD,UAAA,MAAAjC,IAAA,GAAAkC,WAAA,GAAAC,KAAA,QAAAC,OAAA;UACA;UAEAd,MAAA,CAAAX,KAAA;YACAC,GAAA;YACAI,MAAA;YACAtC,IAAA,EAAAgD;UACA,GAAAT,IAAA,WAAAoB,KAAA;YAAA,IAAA3D,IAAA,GAAA2D,KAAA,CAAA3D,IAAA;YACA4C,MAAA,CAAA1C,UAAA;YACA,IAAAF,IAAA,IAAAA,IAAA,CAAAyC,IAAA;cACAG,MAAA,CAAAgB,QAAA,CAAAC,OAAA;cACAjB,MAAA,CAAAd,KAAA;cACAc,MAAA,CAAAkB,WAAA;YACA;cACAlB,MAAA,CAAAgB,QAAA,CAAAG,KAAA,CAAA/D,IAAA,CAAAgE,GAAA;YACA;UACA,GAAAC,KAAA;YACArB,MAAA,CAAA1C,UAAA;YACA0C,MAAA,CAAAgB,QAAA,CAAAG,KAAA;UACA;QACA;MACA;IACA;IAEA;IACAD,WAAA,WAAAA,YAAA;MACA,KAAA7D,aAAA;MACA,KAAA4C,KAAA,CAAA1C,WAAA,CAAA+D,WAAA;IACA;EACA;AACA", "ignoreList": []}]}