{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\login.vue", "mtime": 1750594624583}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AA4TA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div class=\"login-container\">\r\n        <!-- 背景装饰 -->\r\n        <div class=\"background-decoration\">\r\n            <div class=\"decoration-circle circle-1\"></div>\r\n            <div class=\"decoration-circle circle-2\"></div>\r\n            <div class=\"decoration-circle circle-3\"></div>\r\n        </div>\r\n\r\n        <!-- 主要内容区域 -->\r\n        <div class=\"main-content\">\r\n            <!-- 左侧信息区域 -->\r\n            <div class=\"info-section\">\r\n                <div class=\"brand-info\">\r\n                    <div class=\"logo-container\">\r\n                        <i class=\"el-icon-trophy-1\"></i>\r\n                        <h1>体育馆管理系统</h1>\r\n                    </div>\r\n                    <p class=\"brand-description\">\r\n                        现代化的体育场馆预约管理平台<br>\r\n                        为您提供便捷、高效的场地预约服务\r\n                    </p>\r\n                    <div class=\"features\">\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-time\"></i>\r\n                            <span>24小时在线预约</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-location\"></i>\r\n                            <span>多场地智能管理</span>\r\n                        </div>\r\n                        <div class=\"feature-item\">\r\n                            <i class=\"el-icon-user\"></i>\r\n                            <span>用户友好界面</span>\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n\r\n            <!-- 右侧登录表单 -->\r\n            <div class=\"login-section\">\r\n                <div class=\"login-card\">\r\n                    <div class=\"card-header\">\r\n                        <h2>欢迎登录</h2>\r\n                        <p>请输入您的账户信息</p>\r\n                    </div>\r\n\r\n                    <el-form class=\"login-form\" :model=\"rulesForm\" @submit.native.prevent=\"login\">\r\n                        <el-form-item class=\"form-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-user input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.username\"\r\n                                    placeholder=\"请输入用户名\"\r\n                                    size=\"large\"\r\n                                    clearable>\r\n                                </el-input>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item class=\"form-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-lock input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.password\"\r\n                                    type=\"password\"\r\n                                    placeholder=\"请输入密码\"\r\n                                    size=\"large\"\r\n                                    show-password\r\n                                    clearable>\r\n                                </el-input>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item v-if=\"showCaptcha\" class=\"form-item captcha-item\">\r\n                            <div class=\"input-wrapper\">\r\n                                <i class=\"el-icon-key input-icon\"></i>\r\n                                <el-input\r\n                                    v-model=\"rulesForm.code\"\r\n                                    placeholder=\"请输入验证码\"\r\n                                    size=\"large\"\r\n                                    clearable>\r\n                                </el-input>\r\n                                <div class=\"captcha-code\" @click=\"getRandCode(4)\">\r\n                                    <span v-for=\"(item, index) in codes\" :key=\"index\"\r\n                                          :style=\"{color:item.color,transform:item.rotate,fontSize:item.size}\">\r\n                                        {{ item.num }}\r\n                                    </span>\r\n                                </div>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-form-item class=\"form-item role-item\">\r\n                            <label class=\"role-label\">选择角色</label>\r\n                            <div class=\"role-options\">\r\n                                <el-radio-group v-model=\"rulesForm.role\" class=\"role-group\">\r\n                                    <el-radio\r\n                                        v-for=\"item in menus\"\r\n                                        v-if=\"item.hasBackLogin=='是'\"\r\n                                        :key=\"item.roleName\"\r\n                                        :label=\"item.roleName\"\r\n                                        class=\"role-radio\">\r\n                                        {{ item.roleName }}\r\n                                    </el-radio>\r\n                                </el-radio-group>\r\n                            </div>\r\n                        </el-form-item>\r\n\r\n                        <el-button\r\n                            type=\"primary\"\r\n                            @click=\"login()\"\r\n                            class=\"login-button\"\r\n                            size=\"large\"\r\n                            :loading=\"loading\">\r\n                            <i class=\"el-icon-right\"></i>\r\n                            立即登录\r\n                        </el-button>\r\n\r\n                        <div class=\"form-footer\">\r\n                            <div class=\"register-link\" @click=\"register('yonghu')\">\r\n                                <i class=\"el-icon-user-solid\"></i>\r\n                                还没有账户？立即注册\r\n                            </div>\r\n                        </div>\r\n                    </el-form>\r\n                </div>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import menu from \"@/utils/menu\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                loading: false,\r\n                showCaptcha: false, // 是否显示验证码\r\n                rulesForm: {\r\n                    username: \"\",\r\n                    password: \"\",\r\n                    role: \"\",\r\n                    code: '',\r\n                },\r\n                menus: [],\r\n                tableName: \"\",\r\n                codes: [{\r\n                    num: 1,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 2,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 3,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 4,\r\n                    color: '#00c292',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                }],\r\n            };\r\n        },\r\n        mounted() {\r\n            let menus = menu.list();\r\n            this.menus = menus;\r\n        },\r\n        created() {\r\n            this.setInputColor()\r\n            this.getRandCode()\r\n        },\r\n        methods: {\r\n            setInputColor(){\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{\r\n                        el.style.backgroundColor = \"rgba(255, 255, 255, 1)\"\r\n                        el.style.color = \"rgba(51, 51, 51, 1)\"\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                        el.style.borderRadius = \"30px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{\r\n                        el.style.color = \"rgba(59, 160, 215, 1)\"\r\n                    })\r\n                    setTimeout(()=>{\r\n                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{\r\n                            el.style.color = \"#fff\"\r\n                        })\r\n                    },350)\r\n                })\r\n\r\n            },\r\n            register(tableName){\r\n                this.$storage.set(\"loginTable\", tableName);\r\n                this.$router.push({path:'/register'})\r\n            },\r\n            // 登陆\r\n            login() {\r\n                // 验证码检查\r\n                if (this.showCaptcha) {\r\n                    let code = ''\r\n                    for(let i in this.codes) {\r\n                        code += this.codes[i].num\r\n                    }\r\n                    if (!this.rulesForm.code) {\r\n                        this.$message.error(\"请输入验证码\");\r\n                        return;\r\n                    }\r\n                    if (this.rulesForm.code.toLowerCase() != code.toLowerCase()) {\r\n                        this.$message.error(\"验证码输入有误\");\r\n                        this.getRandCode()\r\n                        return;\r\n                    }\r\n                }\r\n\r\n                // 表单验证\r\n                if (!this.rulesForm.username) {\r\n                    this.$message.error(\"请输入用户名\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.password) {\r\n                    this.$message.error(\"请输入密码\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.role) {\r\n                    this.$message.error(\"请选择角色\");\r\n                    return;\r\n                }\r\n\r\n                // 获取表名\r\n                let menus = this.menus;\r\n                for (let i = 0; i < menus.length; i++) {\r\n                    if (menus[i].roleName == this.rulesForm.role) {\r\n                        this.tableName = menus[i].tableName;\r\n                    }\r\n                }\r\n\r\n                // 开始登录\r\n                this.loading = true;\r\n                this.$http({\r\n                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n                    method: \"post\"\r\n                }).then(({ data }) => {\r\n                    this.loading = false;\r\n                    if (data && data.code === 0) {\r\n                        this.$storage.set(\"Token\", data.token);\r\n                        this.$storage.set(\"role\", this.rulesForm.role);\r\n                        this.$storage.set(\"sessionTable\", this.tableName);\r\n                        this.$storage.set(\"adminName\", this.rulesForm.username);\r\n\r\n                        this.$message({\r\n                            message: \"登录成功！\",\r\n                            type: \"success\",\r\n                            duration: 1500,\r\n                            onClose: () => {\r\n                                this.$router.replace({ path: \"/index/\" });\r\n                            }\r\n                        });\r\n                    } else {\r\n                        this.$message.error(data.msg || \"登录失败，请检查用户名和密码\");\r\n                    }\r\n                }).catch(() => {\r\n                    this.loading = false;\r\n                    this.$message.error(\"网络错误，请稍后重试\");\r\n                });\r\n            },\r\n            getRandCode(len = 4){\r\n                this.randomString(len)\r\n            },\r\n            randomString(len = 4) {\r\n                let chars = [\r\n                    \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\",\r\n                    \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\",\r\n                    \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\",\r\n                    \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\",\r\n                    \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"0\", \"1\", \"2\",\r\n                    \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"\r\n                ]\r\n                let colors = [\"0\", \"1\", \"2\",\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\"]\r\n                let sizes = ['14', '15', '16', '17', '18']\r\n\r\n                let output = [];\r\n                for (let i = 0; i < len; i++) {\r\n                    // 随机验证码\r\n                    let key = Math.floor(Math.random()*chars.length)\r\n                    this.codes[i].num = chars[key]\r\n                    // 随机验证码颜色\r\n                    let code = '#'\r\n                    for (let j = 0; j < 6; j++) {\r\n                        let key = Math.floor(Math.random()*colors.length)\r\n                        code += colors[key]\r\n                    }\r\n                    this.codes[i].color = code\r\n                    // 随机验证码方向\r\n                    let rotate = Math.floor(Math.random()*60)\r\n                    let plus = Math.floor(Math.random()*2)\r\n                    if(plus == 1) rotate = '-'+rotate\r\n                    this.codes[i].rotate = 'rotate('+rotate+'deg)'\r\n                    // 随机验证码字体大小\r\n                    let size = Math.floor(Math.random()*sizes.length)\r\n                    this.codes[i].size = sizes[size]+'px'\r\n                }\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.login-container {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n  position: relative;\r\n  overflow: hidden;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n\r\n  // 背景装饰\r\n  .background-decoration {\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    pointer-events: none;\r\n\r\n    .decoration-circle {\r\n      position: absolute;\r\n      border-radius: 50%;\r\n      background: rgba(255, 255, 255, 0.1);\r\n      animation: float 6s ease-in-out infinite;\r\n\r\n      &.circle-1 {\r\n        width: 200px;\r\n        height: 200px;\r\n        top: 10%;\r\n        left: 10%;\r\n        animation-delay: 0s;\r\n      }\r\n\r\n      &.circle-2 {\r\n        width: 150px;\r\n        height: 150px;\r\n        top: 60%;\r\n        right: 15%;\r\n        animation-delay: 2s;\r\n      }\r\n\r\n      &.circle-3 {\r\n        width: 100px;\r\n        height: 100px;\r\n        bottom: 20%;\r\n        left: 20%;\r\n        animation-delay: 4s;\r\n      }\r\n    }\r\n  }\r\n\r\n  // 主要内容区域\r\n  .main-content {\r\n    display: flex;\r\n    width: 100%;\r\n    max-width: 1200px;\r\n    margin: 0 auto;\r\n    padding: 40px;\r\n    gap: 60px;\r\n    align-items: center;\r\n\r\n    // 左侧信息区域\r\n    .info-section {\r\n      flex: 1;\r\n      color: white;\r\n\r\n      .brand-info {\r\n        .logo-container {\r\n          display: flex;\r\n          align-items: center;\r\n          margin-bottom: 30px;\r\n\r\n          i {\r\n            font-size: 48px;\r\n            color: #00c292;\r\n            margin-right: 20px;\r\n          }\r\n\r\n          h1 {\r\n            font-size: 36px;\r\n            font-weight: 700;\r\n            margin: 0;\r\n            background: linear-gradient(45deg, #00c292, #00e5ff);\r\n            -webkit-background-clip: text;\r\n            -webkit-text-fill-color: transparent;\r\n            background-clip: text;\r\n          }\r\n        }\r\n\r\n        .brand-description {\r\n          font-size: 18px;\r\n          line-height: 1.6;\r\n          margin-bottom: 40px;\r\n          opacity: 0.9;\r\n        }\r\n\r\n        .features {\r\n          .feature-item {\r\n            display: flex;\r\n            align-items: center;\r\n            margin-bottom: 20px;\r\n\r\n            i {\r\n              font-size: 20px;\r\n              color: #00c292;\r\n              margin-right: 15px;\r\n              width: 24px;\r\n            }\r\n\r\n            span {\r\n              font-size: 16px;\r\n              opacity: 0.9;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    // 右侧登录表单\r\n    .login-section {\r\n      flex: 0 0 450px;\r\n\r\n      .login-card {\r\n        background: rgba(255, 255, 255, 0.95);\r\n        backdrop-filter: blur(20px);\r\n        border-radius: 20px;\r\n        padding: 40px;\r\n        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);\r\n        border: 1px solid rgba(255, 255, 255, 0.2);\r\n\r\n        .card-header {\r\n          text-align: center;\r\n          margin-bottom: 40px;\r\n\r\n          h2 {\r\n            font-size: 28px;\r\n            font-weight: 700;\r\n            color: #2c3e50;\r\n            margin: 0 0 10px 0;\r\n          }\r\n\r\n          p {\r\n            color: #666;\r\n            font-size: 16px;\r\n            margin: 0;\r\n          }\r\n        }\r\n\r\n        .login-form {\r\n          .form-item {\r\n            margin-bottom: 25px;\r\n\r\n            .input-wrapper {\r\n              position: relative;\r\n\r\n              .input-icon {\r\n                position: absolute;\r\n                left: 15px;\r\n                top: 50%;\r\n                transform: translateY(-50%);\r\n                color: #00c292;\r\n                font-size: 18px;\r\n                z-index: 2;\r\n              }\r\n\r\n              ::v-deep .el-input__inner {\r\n                height: 50px;\r\n                padding-left: 50px;\r\n                border: 2px solid #e8f4f8;\r\n                border-radius: 12px;\r\n                font-size: 16px;\r\n                transition: all 0.3s ease;\r\n                background: #f8fffe !important;\r\n                color: #333 !important;\r\n\r\n                &:focus {\r\n                  border-color: #00c292;\r\n                  box-shadow: 0 0 0 3px rgba(0, 194, 146, 0.1);\r\n                  background: white !important;\r\n                  color: #333 !important;\r\n                }\r\n\r\n                &::placeholder {\r\n                  color: #999 !important;\r\n                }\r\n              }\r\n            }\r\n\r\n            // 验证码特殊样式\r\n            &.captcha-item {\r\n              .input-wrapper {\r\n                display: flex;\r\n                gap: 10px;\r\n\r\n                .el-input {\r\n                  flex: 1;\r\n                }\r\n\r\n                .captcha-code {\r\n                  width: 120px;\r\n                  height: 50px;\r\n                  background: linear-gradient(45deg, #00c292, #00a085);\r\n                  border-radius: 12px;\r\n                  display: flex;\r\n                  align-items: center;\r\n                  justify-content: center;\r\n                  cursor: pointer;\r\n                  transition: all 0.3s ease;\r\n\r\n                  &:hover {\r\n                    transform: scale(1.05);\r\n                  }\r\n\r\n                  span {\r\n                    font-weight: 600;\r\n                    color: white;\r\n                    margin: 0 2px;\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            // 角色选择样式\r\n            &.role-item {\r\n              .role-label {\r\n                display: block;\r\n                margin-bottom: 15px;\r\n                font-weight: 600;\r\n                color: #2c3e50 !important;\r\n                font-size: 16px;\r\n              }\r\n\r\n              .role-options {\r\n                .role-group {\r\n                  display: flex;\r\n                  flex-wrap: wrap;\r\n                  gap: 15px;\r\n\r\n                  .role-radio {\r\n                    margin: 0;\r\n\r\n                    ::v-deep .el-radio__input.is-checked .el-radio__inner {\r\n                      background-color: #00c292;\r\n                      border-color: #00c292;\r\n                    }\r\n\r\n                    ::v-deep .el-radio__input.is-checked + .el-radio__label {\r\n                      color: #00c292 !important;\r\n                      font-weight: 600;\r\n                    }\r\n\r\n                    ::v-deep .el-radio__label {\r\n                      font-size: 16px;\r\n                      color: #666 !important;\r\n                    }\r\n                  }\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          .login-button {\r\n            width: 100%;\r\n            height: 50px;\r\n            font-size: 18px;\r\n            font-weight: 600;\r\n            border-radius: 12px;\r\n            background: linear-gradient(45deg, #00c292, #00a085);\r\n            border: none;\r\n            margin-top: 20px;\r\n            transition: all 0.3s ease;\r\n\r\n            &:hover {\r\n              transform: translateY(-2px);\r\n              box-shadow: 0 8px 25px rgba(0, 194, 146, 0.3);\r\n            }\r\n\r\n            &:active {\r\n              transform: translateY(0);\r\n            }\r\n\r\n            i {\r\n              margin-right: 8px;\r\n            }\r\n          }\r\n\r\n          .form-footer {\r\n            text-align: center;\r\n            margin-top: 30px;\r\n\r\n            .register-link {\r\n              color: #00c292;\r\n              cursor: pointer;\r\n              font-size: 16px;\r\n              transition: all 0.3s ease;\r\n              display: inline-flex;\r\n              align-items: center;\r\n\r\n              &:hover {\r\n                color: #00a085;\r\n                transform: translateY(-1px);\r\n              }\r\n\r\n              i {\r\n                margin-right: 8px;\r\n                font-size: 18px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 动画效果\r\n@keyframes float {\r\n  0%, 100% {\r\n    transform: translateY(0px);\r\n  }\r\n  50% {\r\n    transform: translateY(-20px);\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 1024px) {\r\n  .login-container .main-content {\r\n    flex-direction: column;\r\n    gap: 40px;\r\n    padding: 20px;\r\n\r\n    .info-section {\r\n      text-align: center;\r\n\r\n      .brand-info .logo-container {\r\n        justify-content: center;\r\n\r\n        h1 {\r\n          font-size: 28px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .login-section {\r\n      flex: none;\r\n      width: 100%;\r\n      max-width: 450px;\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .login-container .main-content {\r\n    padding: 15px;\r\n\r\n    .login-section .login-card {\r\n      padding: 30px 20px;\r\n\r\n      .card-header h2 {\r\n        font-size: 24px;\r\n      }\r\n    }\r\n\r\n    .info-section .brand-info .logo-container h1 {\r\n      font-size: 24px;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}