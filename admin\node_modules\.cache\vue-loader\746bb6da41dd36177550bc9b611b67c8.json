{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\login.vue?vue&type=style&index=0&id=7589b93f&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\login.vue", "mtime": 1750589328266}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["login.vue"], "names": [], "mappings": ";AA+aA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "login.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n    <div>\r\n        <div class=\"container loginIn\" style=\"backgroundImage: url(/tiyuguan/img/img/back-img-bg.jpg)\">\r\n\r\n            <div :class=\"2 == 1 ? 'left' : 2 == 2 ? 'left center' : 'left right'\" style=\"backgroundColor: rgba(255, 255, 255, 0.9); borderRadius: 15px; boxShadow: 0 8px 32px rgba(0, 0, 0, 0.1); backdropFilter: blur(10px)\">\r\n                <el-form class=\"login-form\" label-position=\"left\" :label-width=\"2 == 3 ? '56px' : '0px'\">\r\n                    <div class=\"title-container\"><h3 class=\"title\" style=\"color: #2c3e50; fontWeight: bold; marginBottom: 30px\">体育馆使用预约平台</h3></div>\r\n                    <el-form-item :label=\"2 == 3 ? '用户名' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"user\" /></span>\r\n                        <el-input placeholder=\"请输入用户名\" name=\"username\" type=\"text\" v-model=\"rulesForm.username\" />\r\n                    </el-form-item>\r\n                    <el-form-item :label=\"2 == 3 ? '密码' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"password\" /></span>\r\n                        <el-input placeholder=\"请输入密码\" name=\"password\" type=\"password\" v-model=\"rulesForm.password\" />\r\n                    </el-form-item>\r\n                    <el-form-item v-if=\"0 == '1'\" class=\"code\" :label=\"2 == 3 ? '验证码' : ''\" :class=\"'style'+2\">\r\n                        <span v-if=\"2 != 3\" class=\"svg-container\" style=\"color:rgba(59, 160, 215, 1);line-height:44px\"><svg-icon icon-class=\"code\" /></span>\r\n                        <el-input placeholder=\"请输入验证码\" name=\"code\" type=\"text\" v-model=\"rulesForm.code\" />\r\n                        <div class=\"getCodeBt\" @click=\"getRandCode(4)\" style=\"height:44px;line-height:44px\">\r\n                            <span v-for=\"(item, index) in codes\" :key=\"index\" :style=\"{color:item.color,transform:item.rotate,fontSize:item.size}\">{{ item.num }}</span>\r\n                        </div>\r\n                    </el-form-item>\r\n                    <el-form-item label=\"角色\" prop=\"loginInRole\" class=\"role\">\r\n                        <el-radio\r\n                                v-for=\"item in menus\"\r\n                                v-if=\"item.hasBackLogin=='是'\"\r\n                                v-bind:key=\"item.roleName\"\r\n                                v-model=\"rulesForm.role\"\r\n                                :label=\"item.roleName\"\r\n                        >{{item.roleName}}</el-radio>\r\n                    </el-form-item>\r\n                    <el-button type=\"primary\" @click=\"login()\" class=\"loginInBt\" style=\"padding:0;font-size:16px;border-radius:25px;height:44px;line-height:44px;width:100%;backgroundColor:#00c292; borderColor:#00c292; color:#fff; marginTop:20px; boxShadow: 0 4px 12px rgba(0, 194, 146, 0.3)\">{{'1' == '1' ? '登录' : 'login'}}</el-button>\r\n                    <el-form-item class=\"setting\">\r\n                                                                                                                        \t\t\t\t<div style=\"color:rgba(25, 169, 123, 1)\" class=\"register\" @click=\"register('yonghu')\">用户注册</div>\r\n                                                                                                                                                                                                                                                                                                                                            <!-- <div style=\"color:rgba(14, 14, 14, 0.95)\" class=\"reset\">修改密码</div> -->\r\n                    </el-form-item>\r\n                </el-form>\r\n            </div>\r\n\r\n        </div>\r\n    </div>\r\n</template>\r\n<script>\r\n    import menu from \"@/utils/menu\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                rulesForm: {\r\n                    username: \"\",\r\n                    password: \"\",\r\n                    role: \"\",\r\n                    code: '',\r\n                },\r\n                menus: [],\r\n                tableName: \"\",\r\n                codes: [{\r\n                    num: 1,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 2,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 3,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                },{\r\n                    num: 4,\r\n                    color: '#000',\r\n                    rotate: '10deg',\r\n                    size: '16px'\r\n                }],\r\n            };\r\n        },\r\n        mounted() {\r\n            let menus = menu.list();\r\n            this.menus = menus;\r\n        },\r\n        created() {\r\n            this.setInputColor()\r\n            this.getRandCode()\r\n        },\r\n        methods: {\r\n            setInputColor(){\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.loginIn .el-input__inner').forEach(el=>{\r\n                        el.style.backgroundColor = \"rgba(255, 255, 255, 1)\"\r\n                        el.style.color = \"rgba(51, 51, 51, 1)\"\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                        el.style.borderRadius = \"30px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .style3 .el-form-item__label').forEach(el=>{\r\n                        el.style.height = \"44px\"\r\n                        el.style.lineHeight = \"44px\"\r\n                    })\r\n                    document.querySelectorAll('.loginIn .el-form-item__label').forEach(el=>{\r\n                        el.style.color = \"rgba(59, 160, 215, 1)\"\r\n                    })\r\n                    setTimeout(()=>{\r\n                        document.querySelectorAll('.loginIn .role .el-radio__label').forEach(el=>{\r\n                            el.style.color = \"#fff\"\r\n                        })\r\n                    },350)\r\n                })\r\n\r\n            },\r\n            register(tableName){\r\n                this.$storage.set(\"loginTable\", tableName);\r\n                this.$router.push({path:'/register'})\r\n            },\r\n            // 登陆\r\n            login() {\r\n                let code = ''\r\n                for(let i in this.codes) {\r\n                    code += this.codes[i].num\r\n                }\r\n                if ('0' == '1' && !this.rulesForm.code) {\r\n                    this.$message.error(\"请输入验证码\");\r\n                    return;\r\n                }\r\n                if ('0' == '1' && this.rulesForm.code.toLowerCase() != code.toLowerCase()) {\r\n                    this.$message.error(\"验证码输入有误\");\r\n                    this.getRandCode()\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.username) {\r\n                    this.$message.error(\"请输入用户名\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.password) {\r\n                    this.$message.error(\"请输入密码\");\r\n                    return;\r\n                }\r\n                if (!this.rulesForm.role) {\r\n                    this.$message.error(\"请选择角色\");\r\n                    return;\r\n                }\r\n                let menus = this.menus;\r\n                for (let i = 0; i < menus.length; i++) {\r\n                    if (menus[i].roleName == this.rulesForm.role) {\r\n                        this.tableName = menus[i].tableName;\r\n                    }\r\n                }\r\n                this.$http({\r\n                    url: `${this.tableName}/login?username=${this.rulesForm.username}&password=${this.rulesForm.password}`,\r\n                    method: \"post\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.$storage.set(\"Token\", data.token);\r\n                        this.$storage.set(\"role\", this.rulesForm.role);\r\n                        this.$storage.set(\"sessionTable\", this.tableName);\r\n                        this.$storage.set(\"adminName\", this.rulesForm.username);\r\n                        this.$router.replace({ path: \"/index/\" });\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            getRandCode(len = 4){\r\n                this.randomString(len)\r\n            },\r\n            randomString(len = 4) {\r\n                let chars = [\r\n                    \"a\", \"b\", \"c\", \"d\", \"e\", \"f\", \"g\", \"h\", \"i\", \"j\", \"k\",\r\n                    \"l\", \"m\", \"n\", \"o\", \"p\", \"q\", \"r\", \"s\", \"t\", \"u\", \"v\",\r\n                    \"w\", \"x\", \"y\", \"z\", \"A\", \"B\", \"C\", \"D\", \"E\", \"F\", \"G\",\r\n                    \"H\", \"I\", \"J\", \"K\", \"L\", \"M\", \"N\", \"O\", \"P\", \"Q\", \"R\",\r\n                    \"S\", \"T\", \"U\", \"V\", \"W\", \"X\", \"Y\", \"Z\", \"0\", \"1\", \"2\",\r\n                    \"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\"\r\n                ]\r\n                let colors = [\"0\", \"1\", \"2\",\"3\", \"4\", \"5\", \"6\", \"7\", \"8\", \"9\", \"a\", \"b\", \"c\", \"d\", \"e\", \"f\"]\r\n                let sizes = ['14', '15', '16', '17', '18']\r\n\r\n                let output = [];\r\n                for (let i = 0; i < len; i++) {\r\n                    // 随机验证码\r\n                    let key = Math.floor(Math.random()*chars.length)\r\n                    this.codes[i].num = chars[key]\r\n                    // 随机验证码颜色\r\n                    let code = '#'\r\n                    for (let j = 0; j < 6; j++) {\r\n                        let key = Math.floor(Math.random()*colors.length)\r\n                        code += colors[key]\r\n                    }\r\n                    this.codes[i].color = code\r\n                    // 随机验证码方向\r\n                    let rotate = Math.floor(Math.random()*60)\r\n                    let plus = Math.floor(Math.random()*2)\r\n                    if(plus == 1) rotate = '-'+rotate\r\n                    this.codes[i].rotate = 'rotate('+rotate+'deg)'\r\n                    // 随机验证码字体大小\r\n                    let size = Math.floor(Math.random()*sizes.length)\r\n                    this.codes[i].size = sizes[size]+'px'\r\n                }\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n    .loginIn {\r\n        min-height: 100vh;\r\n        position: relative;\r\n        background-repeat: no-repeat;\r\n        background-position: center center;\r\n        background-size: cover;\r\n        background-attachment: fixed;\r\n\r\n    .left {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n\r\n    .login-form {\r\n        background-color: transparent;\r\n        width: 100%;\r\n        right: inherit;\r\n        padding: 0 12px;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        justify-content: center;\r\n        flex-direction: column;\r\n    }\r\n\r\n    .title-container {\r\n        text-align: center;\r\n        font-size: 24px;\r\n\r\n    .title {\r\n        margin: 20px 0;\r\n    }\r\n    }\r\n\r\n    .el-form-item {\r\n        position: relative;\r\n\r\n    .svg-container {\r\n        padding: 6px 5px 6px 15px;\r\n        color: #889aa4;\r\n        vertical-align: middle;\r\n        display: inline-block;\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        z-index: 1;\r\n        padding: 0;\r\n        line-height: 40px;\r\n        width: 30px;\r\n        text-align: center;\r\n    }\r\n\r\n    .el-input {\r\n        display: inline-block;\r\n        height: 40px;\r\n        width: 100%;\r\n\r\n    & ::v-deep input {\r\n          background: transparent;\r\n          border: 0px;\r\n          -webkit-appearance: none;\r\n          padding: 0 15px 0 30px;\r\n          color: #fff;\r\n          height: 40px;\r\n      }\r\n    }\r\n\r\n    }\r\n\r\n\r\n    }\r\n\r\n    .center {\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        width: 380px;\r\n        transform: translate3d(-50%,-50%,0);\r\n        height: auto;\r\n        border-radius: 15px;\r\n        padding: 40px 30px;\r\n        box-sizing: border-box;\r\n    }\r\n\r\n    .right {\r\n        position: absolute;\r\n        left: inherit;\r\n        right: 0;\r\n        top: 0;\r\n        width: 360px;\r\n        height: 100%;\r\n    }\r\n\r\n    .code {\r\n    .el-form-item__content {\r\n        position: relative;\r\n\r\n    .getCodeBt {\r\n        position: absolute;\r\n        right: 0;\r\n        top: 0;\r\n        line-height: 40px;\r\n        width: 100px;\r\n        background-color: rgba(51,51,51,0.4);\r\n        color: #fff;\r\n        text-align: center;\r\n        border-radius: 0 4px 4px 0;\r\n        height: 40px;\r\n        overflow: hidden;\r\n\r\n    span {\r\n        padding: 0 5px;\r\n        display: inline-block;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n    }\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 130px 0 30px;\r\n      }\r\n    }\r\n    }\r\n    }\r\n\r\n    .setting {\r\n    & ::v-deep .el-form-item__content {\r\n          padding: 0 15px;\r\n          box-sizing: border-box;\r\n          line-height: 32px;\r\n          height: 32px;\r\n          font-size: 14px;\r\n          color: #999;\r\n          margin: 0 !important;\r\n\r\n    .register {\r\n        float: left;\r\n        width: 50%;\r\n    }\r\n\r\n    .reset {\r\n        float: right;\r\n        width: 50%;\r\n        text-align: right;\r\n    }\r\n    }\r\n    }\r\n\r\n    .style2 {\r\n        padding-left: 30px;\r\n\r\n    .svg-container {\r\n        left: -30px !important;\r\n    }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .code.style2, .code.style3 {\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 115px 0 15px;\r\n      }\r\n    }\r\n    }\r\n\r\n    .style3 {\r\n    & ::v-deep .el-form-item__label {\r\n          padding-right: 6px;\r\n      }\r\n\r\n    .el-input {\r\n    & ::v-deep input {\r\n          padding: 0 15px !important;\r\n      }\r\n    }\r\n    }\r\n\r\n    .role {\r\n    & ::v-deep .el-form-item__label {\r\n          width: 56px !important;\r\n      }\r\n\r\n    & ::v-deep .el-radio {\r\n          margin-right: 12px;\r\n      }\r\n    }\r\n\r\n    }\r\n\r\n    /* 添加输入框样式优化 */\r\n    .el-input__inner {\r\n        border-radius: 8px !important;\r\n        border: 1px solid #e0e0e0 !important;\r\n        transition: all 0.3s ease !important;\r\n    }\r\n\r\n    .el-input__inner:focus {\r\n        border-color: #00c292 !important;\r\n        box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2) !important;\r\n    }\r\n\r\n    /* 登录按钮悬停效果 */\r\n    .loginInBt:hover {\r\n        background-color: #00a085 !important;\r\n        border-color: #00a085 !important;\r\n        transform: translateY(-2px) !important;\r\n        box-shadow: 0 6px 16px rgba(0, 194, 146, 0.4) !important;\r\n    }\r\n\r\n    /* 标题样式优化 */\r\n    .title {\r\n        text-align: center;\r\n        font-size: 24px !important;\r\n        font-weight: 600 !important;\r\n        margin-bottom: 30px !important;\r\n        color: #2c3e50 !important;\r\n    }\r\n</style>\r\n\r\n"]}]}