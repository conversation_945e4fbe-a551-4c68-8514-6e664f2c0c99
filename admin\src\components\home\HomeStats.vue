<template>
  <div class="stats-container">
    <el-row :gutter="20">
      <el-col :span="6" v-for="(stat, index) in statsData" :key="index">
        <div class="stat-card" :class="`stat-${index + 1}`">
          <div class="stat-icon">
            <i :class="stat.icon"></i>
          </div>
          <div class="stat-content">
            <div class="stat-number">{{ stat.value }}</div>
            <div class="stat-label">{{ stat.label }}</div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'HomeStats',
  data() {
    return {
      statsData: [
        {
          label: '总用户数',
          value: 0,
          icon: 'el-icon-user'
        },
        {
          label: '场地数量',
          value: 0,
          icon: 'el-icon-office-building'
        },
        {
          label: '今日预约',
          value: 0,
          icon: 'el-icon-date'
        },
        {
          label: '总收入',
          value: 0,
          icon: 'el-icon-money'
        }
      ]
    }
  },
  mounted() {
    this.loadStatsData()
  },
  methods: {
    loadStatsData() {
      // 获取用户统计
      this.$http({
        url: 'users/page',
        method: 'get',
        params: { page: 1, limit: 1 }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.statsData[0].value = data.total || 0
        }
      }).catch(() => {})

      // 获取场地统计
      this.$http({
        url: 'changdi/page',
        method: 'get',
        params: { page: 1, limit: 1 }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.statsData[1].value = data.total || 0
        }
      }).catch(() => {})

      // 获取预约统计
      this.$http({
        url: 'yuyuexinxi/page',
        method: 'get',
        params: { page: 1, limit: 1 }
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.statsData[2].value = data.total || 0
        }
      }).catch(() => {})

      // 模拟收入数据
      this.statsData[3].value = '¥' + (Math.random() * 100000).toFixed(0)
    }
  }
}
</script>

<style lang="scss" scoped>
.stats-container {
  margin-bottom: 20px;
  
  .stat-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    transition: all 0.3s ease;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 15px;
      
      i {
        font-size: 24px;
        color: white;
      }
    }
    
    .stat-content {
      flex: 1;
      
      .stat-number {
        font-size: 24px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 5px;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
      }
    }
    
    &.stat-1 .stat-icon {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    &.stat-2 .stat-icon {
      background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    }
    
    &.stat-3 .stat-icon {
      background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    &.stat-4 .stat-icon {
      background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
    }
  }
}
</style>
