<template>
  <div class="login-test-container">
    <h2>登录功能测试页面</h2>
    
    <div class="test-section">
      <h3>1. 登录API测试</h3>
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="用户名">
          <el-input v-model="testForm.username" placeholder="输入用户名"></el-input>
        </el-form-item>
        <el-form-item label="密码">
          <el-input v-model="testForm.password" type="password" placeholder="输入密码"></el-input>
        </el-form-item>
        <el-form-item label="角色">
          <el-select v-model="testForm.role" placeholder="选择角色">
            <el-option label="管理员" value="管理员"></el-option>
            <el-option label="用户" value="用户"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button @click="testLogin" type="primary" :loading="testing">测试登录</el-button>
          <el-button @click="testQuickLogin">快速填入管理员</el-button>
        </el-form-item>
      </el-form>
    </div>
    
    <div class="test-section">
      <h3>2. 菜单数据测试</h3>
      <el-button @click="loadMenus" type="success">加载菜单数据</el-button>
      <div v-if="menus.length > 0" class="menu-list">
        <h4>可用角色:</h4>
        <ul>
          <li v-for="menu in menus" :key="menu.roleName">
            <strong>{{ menu.roleName }}</strong> - {{ menu.tableName }} 
            <span v-if="menu.hasBackLogin === '是'" class="available">✓ 可登录</span>
            <span v-else class="unavailable">✗ 不可登录</span>
          </li>
        </ul>
      </div>
    </div>
    
    <div class="test-section">
      <h3>3. 存储测试</h3>
      <el-button @click="testStorage">测试本地存储</el-button>
      <div v-if="storageInfo" class="storage-info">
        <h4>存储信息:</h4>
        <pre>{{ JSON.stringify(storageInfo, null, 2) }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h3>4. 网络连接测试</h3>
      <el-button @click="testConnection">测试后端连接</el-button>
      <div v-if="connectionStatus" class="connection-status">
        <p :class="connectionStatus.success ? 'success' : 'error'">
          {{ connectionStatus.message }}
        </p>
      </div>
    </div>
    
    <div class="test-section">
      <h3>5. 测试结果</h3>
      <div class="test-results">
        <h4>测试日志:</h4>
        <div class="log-container">
          <div v-for="(log, index) in testLogs" :key="index" :class="'log-' + log.type">
            [{{ log.time }}] {{ log.message }}
          </div>
        </div>
        <el-button @click="clearLogs" size="small">清空日志</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import menu from "@/utils/menu";

export default {
  name: 'LoginTest',
  data() {
    return {
      testing: false,
      testForm: {
        username: '',
        password: '',
        role: ''
      },
      menus: [],
      storageInfo: null,
      connectionStatus: null,
      testLogs: []
    }
  },
  mounted() {
    this.addLog('info', '登录测试页面已加载');
    this.loadMenus();
  },
  methods: {
    addLog(type, message) {
      this.testLogs.push({
        type: type,
        message: message,
        time: new Date().toLocaleTimeString()
      });
    },
    
    clearLogs() {
      this.testLogs = [];
    },
    
    loadMenus() {
      try {
        this.menus = menu.list();
        this.addLog('success', `成功加载 ${this.menus.length} 个菜单项`);
      } catch (error) {
        this.addLog('error', `加载菜单失败: ${error.message}`);
      }
    },
    
    testQuickLogin() {
      this.testForm.username = 'admin';
      this.testForm.password = 'admin';
      this.testForm.role = '管理员';
      this.addLog('info', '已填入管理员测试账号');
    },
    
    testLogin() {
      if (!this.testForm.username || !this.testForm.password || !this.testForm.role) {
        this.$message.error('请填写完整的登录信息');
        return;
      }
      
      this.testing = true;
      this.addLog('info', `开始测试登录: ${this.testForm.username}`);
      
      // 获取表名
      let tableName = '';
      for (let menu of this.menus) {
        if (menu.roleName === this.testForm.role) {
          tableName = menu.tableName;
          break;
        }
      }
      
      if (!tableName) {
        this.addLog('error', '未找到对应的表名');
        this.testing = false;
        return;
      }
      
      this.$http({
        url: `${tableName}/login?username=${this.testForm.username}&password=${this.testForm.password}`,
        method: "post"
      }).then(({ data }) => {
        this.testing = false;
        if (data && data.code === 0) {
          this.addLog('success', '登录测试成功');
          this.$message.success('登录测试成功！');
        } else {
          this.addLog('error', `登录失败: ${data.msg || '未知错误'}`);
          this.$message.error(data.msg || '登录失败');
        }
      }).catch((error) => {
        this.testing = false;
        this.addLog('error', `网络错误: ${error.message}`);
        this.$message.error('网络错误');
      });
    },
    
    testStorage() {
      try {
        // 测试存储功能
        const testData = {
          testKey: 'testValue',
          timestamp: new Date().toISOString()
        };
        
        this.$storage.set('testData', testData);
        const retrieved = this.$storage.get('testData');
        
        this.storageInfo = {
          stored: testData,
          retrieved: retrieved,
          success: JSON.stringify(testData) === JSON.stringify(retrieved)
        };
        
        this.addLog('success', '本地存储测试成功');
      } catch (error) {
        this.addLog('error', `存储测试失败: ${error.message}`);
      }
    },
    
    testConnection() {
      this.addLog('info', '测试后端连接...');
      
      this.$http({
        url: 'users/page',
        method: 'get',
        params: { page: 1, limit: 1 }
      }).then(() => {
        this.connectionStatus = {
          success: true,
          message: '后端连接正常'
        };
        this.addLog('success', '后端连接测试成功');
      }).catch((error) => {
        this.connectionStatus = {
          success: false,
          message: `后端连接失败: ${error.message}`
        };
        this.addLog('error', `后端连接测试失败: ${error.message}`);
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.login-test-container {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.test-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
}

.menu-list {
  margin-top: 15px;
  
  ul {
    list-style: none;
    padding: 0;
    
    li {
      padding: 8px 0;
      border-bottom: 1px solid #eee;
      
      .available {
        color: #67c23a;
        font-weight: bold;
      }
      
      .unavailable {
        color: #f56c6c;
        font-weight: bold;
      }
    }
  }
}

.storage-info, .connection-status {
  margin-top: 15px;
  
  pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-size: 12px;
  }
  
  .success {
    color: #67c23a;
    font-weight: bold;
  }
  
  .error {
    color: #f56c6c;
    font-weight: bold;
  }
}

.test-results {
  .log-container {
    max-height: 300px;
    overflow-y: auto;
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 12px;
    margin-bottom: 10px;
    
    .log-info {
      color: #409eff;
    }
    
    .log-success {
      color: #67c23a;
    }
    
    .log-error {
      color: #f56c6c;
    }
  }
}
</style>
