{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\dictionaryChangdi\\list.vue?vue&type=style&index=0&id=6aaef36a&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\dictionaryChangdi\\list.vue", "mtime": 1750592851615}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["list.vue"], "names": [], "mappings": ";AA+hBA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "list.vue", "sourceRoot": "src/views/modules/dictionaryChangdi", "sourcesContent": ["<template>\r\n    <div class=\"changdi-type-container\">\r\n        <!-- 页面头部 -->\r\n        <div class=\"page-header\">\r\n            <div class=\"header-content\">\r\n                <div class=\"title-section\">\r\n                    <h2><i class=\"el-icon-menu\"></i> 场地类型管理</h2>\r\n                    <p>管理体育馆的场地类型分类，支持增删改查操作</p>\r\n                </div>\r\n                <div class=\"stats-section\">\r\n                    <el-card class=\"stats-card\">\r\n                        <div class=\"stats-content\">\r\n                            <div class=\"stats-number\">{{ totalPage }}</div>\r\n                            <div class=\"stats-label\">总类型数</div>\r\n                        </div>\r\n                        <i class=\"el-icon-collection stats-icon\"></i>\r\n                    </el-card>\r\n                </div>\r\n            </div>\r\n        </div>\r\n\r\n        <!-- 列表页 -->\r\n        <div v-if=\"showFlag\">\r\n            <el-card class=\"search-card\">\r\n                <div class=\"search-section\">\r\n                    <el-form :inline=\"true\" :model=\"searchForm\" class=\"search-form\">\r\n                        <el-form-item label=\"类型名称\">\r\n                            <el-input\r\n                                v-model=\"searchForm.indexNameSearch\"\r\n                                placeholder=\"请输入场地类型名称\"\r\n                                clearable\r\n                                prefix-icon=\"el-icon-search\"\r\n                                style=\"width: 250px;\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                        <el-form-item>\r\n                            <el-button type=\"primary\" icon=\"el-icon-search\" @click=\"search()\">查询</el-button>\r\n                            <el-button icon=\"el-icon-refresh\" @click=\"resetSearch()\">重置</el-button>\r\n                        </el-form-item>\r\n                    </el-form>\r\n                </div>\r\n\r\n                <div class=\"action-section\">\r\n                    <el-button\r\n                        v-if=\"isAuth('dictionaryChangdi','新增')\"\r\n                        type=\"success\"\r\n                        icon=\"el-icon-plus\"\r\n                        @click=\"addOrUpdateHandler()\">\r\n                        新增类型\r\n                    </el-button>\r\n                    <el-button\r\n                        v-if=\"isAuth('dictionaryChangdi','删除')\"\r\n                        :disabled=\"dataListSelections.length <= 0\"\r\n                        type=\"danger\"\r\n                        icon=\"el-icon-delete\"\r\n                        @click=\"deleteHandler()\">\r\n                        批量删除 ({{ dataListSelections.length }})\r\n                    </el-button>\r\n                </div>\r\n            </el-card>\r\n            <el-card class=\"table-card\">\r\n                <el-table\r\n                    class=\"changdi-type-table\"\r\n                    :data=\"dataList\"\r\n                    v-loading=\"dataListLoading\"\r\n                    @selection-change=\"selectionChangeHandler\"\r\n                    stripe\r\n                    border\r\n                    style=\"width: 100%\">\r\n\r\n                    <el-table-column\r\n                        type=\"selection\"\r\n                        width=\"55\"\r\n                        align=\"center\">\r\n                    </el-table-column>\r\n\r\n                    <el-table-column\r\n                        label=\"序号\"\r\n                        type=\"index\"\r\n                        width=\"80\"\r\n                        align=\"center\">\r\n                    </el-table-column>\r\n\r\n                    <el-table-column\r\n                        prop=\"codeIndex\"\r\n                        label=\"类型编码\"\r\n                        width=\"150\"\r\n                        align=\"center\"\r\n                        sortable>\r\n                        <template slot-scope=\"scope\">\r\n                            <el-tag type=\"info\" size=\"small\">{{ scope.row.codeIndex }}</el-tag>\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column\r\n                        prop=\"indexName\"\r\n                        label=\"类型名称\"\r\n                        align=\"center\"\r\n                        sortable>\r\n                        <template slot-scope=\"scope\">\r\n                            <span class=\"type-name\">{{ scope.row.indexName }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column\r\n                        label=\"创建时间\"\r\n                        prop=\"createTime\"\r\n                        width=\"180\"\r\n                        align=\"center\"\r\n                        sortable>\r\n                        <template slot-scope=\"scope\">\r\n                            <i class=\"el-icon-time\"></i>\r\n                            <span>{{ scope.row.createTime || '暂无' }}</span>\r\n                        </template>\r\n                    </el-table-column>\r\n\r\n                    <el-table-column\r\n                        label=\"操作\"\r\n                        width=\"220\"\r\n                        align=\"center\"\r\n                        fixed=\"right\">\r\n                        <template slot-scope=\"scope\">\r\n                            <el-button\r\n                                v-if=\"isAuth('dictionaryChangdi','查看')\"\r\n                                type=\"info\"\r\n                                icon=\"el-icon-view\"\r\n                                size=\"mini\"\r\n                                @click=\"addOrUpdateHandler(scope.row.id,'info')\">\r\n                                详情\r\n                            </el-button>\r\n                            <el-button\r\n                                v-if=\"isAuth('dictionaryChangdi','修改')\"\r\n                                type=\"primary\"\r\n                                icon=\"el-icon-edit\"\r\n                                size=\"mini\"\r\n                                @click=\"addOrUpdateHandler(scope.row.id)\">\r\n                                编辑\r\n                            </el-button>\r\n                            <el-button\r\n                                v-if=\"isAuth('dictionaryChangdi','删除')\"\r\n                                type=\"danger\"\r\n                                icon=\"el-icon-delete\"\r\n                                size=\"mini\"\r\n                                @click=\"deleteHandler(scope.row.id)\">\r\n                                删除\r\n                            </el-button>\r\n                        </template>\r\n                    </el-table-column>\r\n                </el-table>\r\n\r\n                <div class=\"pagination-wrapper\">\r\n                    <el-pagination\r\n                        @size-change=\"sizeChangeHandle\"\r\n                        @current-change=\"currentChangeHandle\"\r\n                        :current-page=\"pageIndex\"\r\n                        :page-sizes=\"[10, 20, 50, 100]\"\r\n                        :page-size=\"pageSize\"\r\n                        :total=\"totalPage\"\r\n                        layout=\"total, sizes, prev, pager, next, jumper\"\r\n                        background>\r\n                    </el-pagination>\r\n                </div>\r\n            </el-card>\r\n        </div>\r\n        <!-- 添加/修改页面  将父组件的search方法传递给子组件-->\r\n        <add-or-update v-if=\"addOrUpdateFlag\" :parent=\"this\" ref=\"addOrUpdate\"></add-or-update>\r\n\r\n    </div>\r\n</template>\r\n<script>\r\n    import AddOrUpdate from \"./add-or-update\";\r\n    import styleJs from \"../../../utils/style.js\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                searchForm: {\r\n                    key: \"\"\r\n                },\r\n                form:{},\r\n                dataList: [],\r\n                pageIndex: 1,\r\n                pageSize: 10,\r\n                totalPage: 0,\r\n                dataListLoading: false,\r\n                dataListSelections: [],\r\n                showFlag: true,\r\n                sfshVisiable: false,\r\n                shForm: {},\r\n                chartVisiable: false,\r\n                addOrUpdateFlag:false,\r\n                contents:null,\r\n                layouts: '',\r\n\r\n\r\n            };\r\n        },\r\n        created() {\r\n            this.contents = styleJs.listStyle();\r\n            this.init();\r\n            this.getDataList();\r\n            this.contentStyleChange()\r\n        },\r\n        mounted() {\r\n\r\n        },\r\n        filters: {\r\n            htmlfilter: function (val) {\r\n                return val.replace(/<[^>]*>/g).replace(/undefined/g,'');\r\n            }\r\n        },\r\n        components: {\r\n            AddOrUpdate,\r\n        },\r\n        methods: {\r\n            contentStyleChange() {\r\n                this.contentSearchStyleChange()\r\n                this.contentBtnAdAllStyleChange()\r\n                this.contentSearchBtnStyleChange()\r\n                this.contentTableBtnStyleChange()\r\n                this.contentPageStyleChange()\r\n            },\r\n            contentSearchStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.form-content .slt .el-input__inner').forEach(el=>{\r\n                    let textAlign = 'left'\r\n                    if(this.contents.inputFontPosition == 2) textAlign = 'center'\r\n                if(this.contents.inputFontPosition == 3) textAlign = 'right'\r\n                el.style.textAlign = textAlign\r\n                el.style.height = this.contents.inputHeight\r\n                el.style.lineHeight = this.contents.inputHeight\r\n                el.style.color = this.contents.inputFontColor\r\n                el.style.fontSize = this.contents.inputFontSize\r\n                el.style.borderWidth = this.contents.inputBorderWidth\r\n                el.style.borderStyle = this.contents.inputBorderStyle\r\n                el.style.borderColor = this.contents.inputBorderColor\r\n                el.style.borderRadius = this.contents.inputBorderRadius\r\n                el.style.backgroundColor = this.contents.inputBgColor\r\n            })\r\n                if(this.contents.inputTitle) {\r\n                    document.querySelectorAll('.form-content .slt .el-form-item__label').forEach(el=>{\r\n                        el.style.color = this.contents.inputTitleColor\r\n                    el.style.fontSize = this.contents.inputTitleSize\r\n                    el.style.lineHeight = this.contents.inputHeight\r\n                })\r\n                }\r\n                setTimeout(()=>{\r\n                    document.querySelectorAll('.form-content .slt .el-input__prefix').forEach(el=>{\r\n                    el.style.color = this.contents.inputIconColor\r\n                el.style.lineHeight = this.contents.inputHeight\r\n            })\r\n                document.querySelectorAll('.form-content .slt .el-input__suffix').forEach(el=>{\r\n                    el.style.color = this.contents.inputIconColor\r\n                el.style.lineHeight = this.contents.inputHeight\r\n            })\r\n                document.querySelectorAll('.form-content .slt .el-input__icon').forEach(el=>{\r\n                    el.style.lineHeight = this.contents.inputHeight\r\n            })\r\n            },10)\r\n\r\n            })\r\n            },\r\n            // 搜索按钮\r\n            contentSearchBtnStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.form-content .slt .el-button--success').forEach(el=>{\r\n                    el.style.height = this.contents.searchBtnHeight\r\n                el.style.color = this.contents.searchBtnFontColor\r\n                el.style.fontSize = this.contents.searchBtnFontSize\r\n                el.style.borderWidth = this.contents.searchBtnBorderWidth\r\n                el.style.borderStyle = this.contents.searchBtnBorderStyle\r\n                el.style.borderColor = this.contents.searchBtnBorderColor\r\n                el.style.borderRadius = this.contents.searchBtnBorderRadius\r\n                el.style.backgroundColor = this.contents.searchBtnBgColor\r\n            })\r\n            })\r\n            },\r\n            // 新增、批量删除\r\n            contentBtnAdAllStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.form-content .ad .el-button--success').forEach(el=>{\r\n                    el.style.height = this.contents.btnAdAllHeight\r\n                el.style.color = this.contents.btnAdAllAddFontColor\r\n                el.style.fontSize = this.contents.btnAdAllFontSize\r\n                el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                el.style.backgroundColor = this.contents.btnAdAllAddBgColor\r\n            })\r\n                document.querySelectorAll('.form-content .ad .el-button--danger').forEach(el=>{\r\n                    el.style.height = this.contents.btnAdAllHeight\r\n                el.style.color = this.contents.btnAdAllDelFontColor\r\n                el.style.fontSize = this.contents.btnAdAllFontSize\r\n                el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                el.style.backgroundColor = this.contents.btnAdAllDelBgColor\r\n            })\r\n                document.querySelectorAll('.form-content .ad .el-button--warning').forEach(el=>{\r\n                    el.style.height = this.contents.btnAdAllHeight\r\n                el.style.color = this.contents.btnAdAllWarnFontColor\r\n                el.style.fontSize = this.contents.btnAdAllFontSize\r\n                el.style.borderWidth = this.contents.btnAdAllBorderWidth\r\n                el.style.borderStyle = this.contents.btnAdAllBorderStyle\r\n                el.style.borderColor = this.contents.btnAdAllBorderColor\r\n                el.style.borderRadius = this.contents.btnAdAllBorderRadius\r\n                el.style.backgroundColor = this.contents.btnAdAllWarnBgColor\r\n            })\r\n            })\r\n            },\r\n            // 表格\r\n            rowStyle({ row, rowIndex}) {\r\n                if (rowIndex % 2 == 1) {\r\n                    if(this.contents.tableStripe) {\r\n                        return {color:this.contents.tableStripeFontColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            cellStyle({ row, rowIndex}){\r\n                if (rowIndex % 2 == 1) {\r\n                    if(this.contents.tableStripe) {\r\n                        return {backgroundColor:this.contents.tableStripeBgColor}\r\n                    }\r\n                } else {\r\n                    return ''\r\n                }\r\n            },\r\n            headerRowStyle({ row, rowIndex}){\r\n                return {color: this.contents.tableHeaderFontColor}\r\n            },\r\n            headerCellStyle({ row, rowIndex}){\r\n                return {backgroundColor: this.contents.tableHeaderBgColor}\r\n            },\r\n            // 表格按钮\r\n            contentTableBtnStyleChange(){\r\n                // this.$nextTick(()=>{\r\n                //   setTimeout(()=>{\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--success').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDetailFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDetailBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--primary').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnEditFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnEditBgColor\r\n                //     })\r\n                //     document.querySelectorAll('.table-content .tables .el-table__body .el-button--danger').forEach(el=>{\r\n                //       el.style.height = this.contents.tableBtnHeight\r\n                //       el.style.color = this.contents.tableBtnDelFontColor\r\n                //       el.style.fontSize = this.contents.tableBtnFontSize\r\n                //       el.style.borderWidth = this.contents.tableBtnBorderWidth\r\n                //       el.style.borderStyle = this.contents.tableBtnBorderStyle\r\n                //       el.style.borderColor = this.contents.tableBtnBorderColor\r\n                //       el.style.borderRadius = this.contents.tableBtnBorderRadius\r\n                //       el.style.backgroundColor = this.contents.tableBtnDelBgColor\r\n                //     })\r\n\r\n                //   }, 50)\r\n                // })\r\n            },\r\n            // 分页\r\n            contentPageStyleChange(){\r\n                let arr = []\r\n\r\n                if(this.contents.pageTotal) arr.push('total')\r\n                if(this.contents.pageSizes) arr.push('sizes')\r\n                if(this.contents.pagePrevNext){\r\n                    arr.push('prev')\r\n                    if(this.contents.pagePager) arr.push('pager')\r\n                    arr.push('next')\r\n                }\r\n                if(this.contents.pageJumper) arr.push('jumper')\r\n                this.layouts = arr.join()\r\n                this.contents.pageEachNum = 10\r\n            },\r\n\r\n            init () {\r\n            },\r\n            search() {\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 重置搜索\r\n            resetSearch() {\r\n                this.searchForm.indexNameSearch = '';\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 获取数据列表\r\n            getDataList() {\r\n                this.dataListLoading = true;\r\n                let params = {\r\n                    page: this.pageIndex,\r\n                    limit: this.pageSize,\r\n                    sort: 'id',\r\n                }\r\n                if(this.searchForm.indexNameSearch!='' && this.searchForm.indexNameSearch!=undefined){\r\n                    params['indexName'] = this.searchForm.indexNameSearch\r\n                }\r\n                //本表的\r\n                params['dicCode'] = \"changdi_types\"//编码名字\r\n                params['dicName'] = \"场地类型名称\",//汉字名字\r\n                this.$http({\r\n                    url: \"dictionary/page\",\r\n                    method: \"get\",\r\n                    params: params\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.dataList = data.data.list;\r\n                    this.totalPage = data.data.total;\r\n                } else {\r\n                    this.dataList = [];\r\n                    this.totalPage = 0;\r\n                }\r\n                this.dataListLoading = false;\r\n            });\r\n            },\r\n            // 每页数\r\n            sizeChangeHandle(val) {\r\n                this.pageSize = val;\r\n                this.pageIndex = 1;\r\n                this.getDataList();\r\n            },\r\n            // 当前页\r\n            currentChangeHandle(val) {\r\n                this.pageIndex = val;\r\n                this.getDataList();\r\n            },\r\n            // 多选\r\n            selectionChangeHandler(val) {\r\n                this.dataListSelections = val;\r\n            },\r\n            // 添加/修改\r\n            addOrUpdateHandler(id,type) {\r\n                this.showFlag = false;\r\n                this.addOrUpdateFlag = true;\r\n                this.crossAddOrUpdateFlag = false;\r\n                if(type!='info'){\r\n                    type = 'else';\r\n                }\r\n                this.$nextTick(() => {\r\n                    this.$refs.addOrUpdate.init(id,type);\r\n            });\r\n            },\r\n            // 查看评论\r\n            // 审核窗口\r\n            shDialog(row){\r\n                this.sfshVisiable = !this.sfshVisiable;\r\n                if(row){\r\n                    this.shForm = {\r\n                        huodongbianhao: row.huodongbianhao,\r\n                        huodongmingcheng: row.huodongmingcheng,\r\n                        huodongleixing: row.huodongleixing,\r\n                        huodongdizhi: row.huodongdizhi,\r\n                        huodongriqi: row.huodongriqi,\r\n                        gerenzhanghao: row.gerenzhanghao,\r\n                        xingming: row.xingming,\r\n                        shenqingriqi: row.shenqingriqi,\r\n                        sfsh: row.sfsh,\r\n                        shhf: row.shhf,\r\n                        id: row.id\r\n                    }\r\n                }\r\n            },\r\n            // 审核\r\n            shHandler(){\r\n                this.$confirm(`确定操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                    url: \"dictionary/update\",\r\n                    method: \"post\",\r\n                    data: this.shForm\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.$message({\r\n                        message: \"操作成功\",\r\n                        type: \"success\",\r\n                        duration: 1500,\r\n                        onClose: () => {\r\n                        this.getDataList();\r\n                    this.shDialog()\r\n                }\r\n                });\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n            });\r\n            },\r\n            // 删除\r\n            deleteHandler(id) {\r\n                var ids = id\r\n                        ? [Number(id)]\r\n                        : this.dataListSelections.map(item => {\r\n                    return Number(item.id);\r\n            });\r\n                this.$confirm(`确定进行[${id ? \"删除\" : \"批量删除\"}]操作?`, \"提示\", {\r\n                    confirmButtonText: \"确定\",\r\n                    cancelButtonText: \"取消\",\r\n                    type: \"warning\"\r\n                }).then(() => {\r\n                    this.$http({\r\n                    url: \"dictionary/delete\",\r\n                    method: \"post\",\r\n                    data: ids\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                    this.$message({\r\n                        message: \"操作成功\",\r\n                        type: \"success\",\r\n                        duration: 1500,\r\n                        onClose: () => {\r\n                        this.search();\r\n                }\r\n                });\r\n                } else {\r\n                    this.$message.error(data.msg);\r\n                }\r\n            });\r\n            });\r\n            },\r\n        }\r\n\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.changdi-type-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n\r\n  .page-header {\r\n    margin-bottom: 20px;\r\n\r\n    .header-content {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n\r\n      .title-section {\r\n        h2 {\r\n          font-size: 24px;\r\n          color: #2c3e50;\r\n          margin: 0 0 8px 0;\r\n\r\n          i {\r\n            color: #00c292;\r\n            margin-right: 10px;\r\n          }\r\n        }\r\n\r\n        p {\r\n          color: #909399;\r\n          margin: 0;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n\r\n      .stats-section {\r\n        .stats-card {\r\n          width: 200px;\r\n\r\n          ::v-deep .el-card__body {\r\n            padding: 20px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: space-between;\r\n          }\r\n\r\n          .stats-content {\r\n            .stats-number {\r\n              font-size: 28px;\r\n              font-weight: bold;\r\n              color: #00c292;\r\n              line-height: 1;\r\n            }\r\n\r\n            .stats-label {\r\n              font-size: 14px;\r\n              color: #909399;\r\n              margin-top: 5px;\r\n            }\r\n          }\r\n\r\n          .stats-icon {\r\n            font-size: 40px;\r\n            color: #00c292;\r\n            opacity: 0.3;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .search-card {\r\n    margin-bottom: 20px;\r\n\r\n    ::v-deep .el-card__body {\r\n      padding: 20px;\r\n    }\r\n\r\n    .search-section {\r\n      margin-bottom: 20px;\r\n\r\n      .search-form {\r\n        .el-form-item {\r\n          margin-bottom: 0;\r\n          margin-right: 20px;\r\n\r\n          ::v-deep .el-form-item__label {\r\n            font-weight: 600;\r\n            color: #2c3e50;\r\n          }\r\n\r\n          ::v-deep .el-input__inner {\r\n            border-radius: 6px;\r\n            border: 1px solid #dcdfe6;\r\n            transition: all 0.3s ease;\r\n\r\n            &:focus {\r\n              border-color: #00c292;\r\n              box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .action-section {\r\n      padding-top: 20px;\r\n      border-top: 1px solid #ebeef5;\r\n\r\n      .el-button {\r\n        border-radius: 6px;\r\n        padding: 10px 20px;\r\n        font-weight: 600;\r\n        margin-right: 10px;\r\n\r\n        &.el-button--success {\r\n          background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n          }\r\n        }\r\n\r\n        &.el-button--danger {\r\n          background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #ee5a52 0%, #dc4c64 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .table-card {\r\n    ::v-deep .el-card__body {\r\n      padding: 0;\r\n    }\r\n\r\n    .changdi-type-table {\r\n      ::v-deep .el-table__header {\r\n        background: #fafbfc;\r\n\r\n        th {\r\n          background: #fafbfc !important;\r\n          color: #2c3e50;\r\n          font-weight: 600;\r\n          border-bottom: 2px solid #00c292;\r\n        }\r\n      }\r\n\r\n      ::v-deep .el-table__body {\r\n        tr {\r\n          &:hover {\r\n            background: #f8f9fa !important;\r\n          }\r\n\r\n          td {\r\n            border-bottom: 1px solid #ebeef5;\r\n\r\n            .type-name {\r\n              font-weight: 600;\r\n              color: #2c3e50;\r\n            }\r\n\r\n            .el-tag {\r\n              font-weight: 600;\r\n            }\r\n\r\n            .el-button {\r\n              border-radius: 4px;\r\n              padding: 5px 10px;\r\n              margin: 0 2px;\r\n\r\n              &.el-button--mini {\r\n                font-size: 12px;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .pagination-wrapper {\r\n      padding: 20px;\r\n      text-align: center;\r\n      border-top: 1px solid #ebeef5;\r\n      background: #fafbfc;\r\n\r\n      ::v-deep .el-pagination {\r\n        .el-pagination__total,\r\n        .el-pagination__jump {\r\n          color: #606266;\r\n        }\r\n\r\n        .btn-prev,\r\n        .btn-next,\r\n        .el-pager li {\r\n          border-radius: 4px;\r\n          margin: 0 2px;\r\n\r\n          &:hover {\r\n            color: #00c292;\r\n          }\r\n\r\n          &.active {\r\n            background: #00c292;\r\n            border-color: #00c292;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n</style>\r\n\r\n\r\n\r\n"]}]}