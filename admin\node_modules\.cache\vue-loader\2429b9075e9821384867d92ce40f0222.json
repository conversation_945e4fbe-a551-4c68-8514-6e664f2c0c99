{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeChart.vue?vue&type=style&index=0&id=61131102&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeChart.vue", "mtime": 1750589717478}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:DQojaG9tZS1jaGFydCB7DQogIGJhY2tncm91bmQ6ICNmZmZmZmY7DQogIHBhZGRpbmc6IDIwcHggMDsNCn0NCg=="}, {"version": 3, "sources": ["HomeChart.vue"], "names": [], "mappings": ";AAyHA;AACA;AACA;AACA", "file": "HomeChart.vue", "sourceRoot": "src/components/home", "sourcesContent": ["<template>\r\n  <div id=\"home-chart\" style=\"width:100%;height:400px;\"></div>\r\n</template>\r\n<script>\r\nexport default {\r\n  mounted() {\r\n    this.homeChart();\r\n  },\r\n  methods: {\r\n    homeChart() {\r\n      // 基于准备好的dom，初始化echarts实例\r\n      var myChart = this.$echarts.init(document.getElementById(\"home-chart\"));\r\n      // 指定图表的配置项和数据\r\n      var option = {\r\n        tooltip: {\r\n          trigger: \"axis\"\r\n        },\r\n        legend: {\r\n          data: [\"访问量\", \"用户量\", \"收入\"]\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"3%\",\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          boundaryGap: false,\r\n          data: [\r\n            \"1月\",\r\n            \"2月\",\r\n            \"3月\",\r\n            \"4月\",\r\n            \"5月\",\r\n            \"6月\",\r\n            \"7月\",\r\n            \"8月\",\r\n            \"9月\",\r\n            \"10月\",\r\n            \"11月\",\r\n            \"12月\"\r\n          ]\r\n        },\r\n        yAxis: {\r\n          type: \"value\"\r\n        },\r\n        series: [\r\n          {\r\n            name: \"访问量\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              120,\r\n              132,\r\n              101,\r\n              134,\r\n              90,\r\n              230,\r\n              210,\r\n              120,\r\n              132,\r\n              101,\r\n              134,\r\n              90,\r\n              230\r\n            ]\r\n          },\r\n          {\r\n            name: \"用户量\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              220,\r\n              182,\r\n              191,\r\n              234,\r\n              290,\r\n              330,\r\n              310,\r\n              182,\r\n              191,\r\n              234,\r\n              290,\r\n              330,\r\n              310\r\n            ]\r\n          },\r\n          {\r\n            name: \"收入\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              150,\r\n              232,\r\n              201,\r\n              154,\r\n              190,\r\n              330,\r\n              410,\r\n              232,\r\n              201,\r\n              154,\r\n              190,\r\n              330,\r\n              410\r\n            ]\r\n          }\r\n        ]\r\n      };\r\n      // // 使用刚指定的配置项和数据显示图表\r\n      myChart.setOption(option);\r\n      //根据窗口的大小变动图表\r\n      window.onresize = function() {\r\n        myChart.resize();\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n#home-chart {\r\n  background: #ffffff;\r\n  padding: 20px 0;\r\n}\r\n</style>\r\n\r\n"]}]}