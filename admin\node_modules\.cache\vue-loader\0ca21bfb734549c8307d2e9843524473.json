{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\components\\VenueDetailsDialog.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\components\\VenueDetailsDialog.vue", "mtime": 1750596240454}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["VenueDetailsDialog.vue"], "names": [], "mappings": ";AAmHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "VenueDetailsDialog.vue", "sourceRoot": "src/views/components", "sourcesContent": ["<template>\n  <el-dialog\n    title=\"场地详情\"\n    :visible.sync=\"dialogVisible\"\n    width=\"800px\"\n    :before-close=\"handleClose\"\n    class=\"details-dialog\">\n    \n    <div class=\"details-content\">\n      <!-- 场地基本信息 -->\n      <div class=\"venue-header\">\n        <div class=\"venue-image\">\n          <img :src=\"venue.changdiPhoto || '/tiyuguan/img/noimg.jpg'\" :alt=\"venue.changdiName\">\n        </div>\n        <div class=\"venue-basic-info\">\n          <h2>{{ venue.changdiName }}</h2>\n          <div class=\"venue-code\">场地编号：{{ venue.changdiUuidNumber }}</div>\n          <div class=\"venue-status\">\n            <el-tag :type=\"venue.shangxiaTypes === 1 ? 'success' : 'danger'\">\n              {{ venue.shangxiaTypes === 1 ? '可预约' : '暂停预约' }}\n            </el-tag>\n          </div>\n          <div class=\"venue-price\">\n            <span class=\"current-price\">¥{{ venue.changdiNewMoney }}</span>\n            <span v-if=\"venue.changdiOldMoney !== venue.changdiNewMoney\" class=\"original-price\">\n              ¥{{ venue.changdiOldMoney }}\n            </span>\n            <span class=\"price-unit\">/时段</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- 详细信息 -->\n      <div class=\"venue-details\">\n        <el-row :gutter=\"30\">\n          <el-col :span=\"12\">\n            <div class=\"detail-section\">\n              <h3><i class=\"el-icon-info\"></i> 基本信息</h3>\n              <div class=\"detail-list\">\n                <div class=\"detail-item\">\n                  <span class=\"label\">场地类型：</span>\n                  <span class=\"value\">{{ venue.changdiValue }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <span class=\"label\">半全场：</span>\n                  <span class=\"value\">{{ venue.banquanValue }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <span class=\"label\">开放时间：</span>\n                  <span class=\"value\">{{ venue.shijianduan || '08:00-22:00' }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <span class=\"label\">点击次数：</span>\n                  <span class=\"value\">{{ venue.changdiClicknum }}次</span>\n                </div>\n              </div>\n            </div>\n          </el-col>\n          \n          <el-col :span=\"12\">\n            <div class=\"detail-section\">\n              <h3><i class=\"el-icon-star-on\"></i> 推荐信息</h3>\n              <div class=\"recommendation\">\n                <p>{{ venue.tuijian || '暂无推荐信息' }}</p>\n              </div>\n            </div>\n          </el-col>\n        </el-row>\n      </div>\n\n      <!-- 场地描述 -->\n      <div class=\"venue-description\" v-if=\"venue.changdiContent\">\n        <h3><i class=\"el-icon-document\"></i> 场地介绍</h3>\n        <div class=\"description-content\" v-html=\"venue.changdiContent\"></div>\n      </div>\n\n      <!-- 可预约时间段 -->\n      <div class=\"available-slots\">\n        <h3><i class=\"el-icon-time\"></i> 可预约时间段</h3>\n        <div class=\"time-slots\">\n          <div v-for=\"slot in timeSlots\" :key=\"slot.value\" class=\"time-slot\" :class=\"{ disabled: slot.disabled }\">\n            <div class=\"slot-time\">{{ slot.label }}</div>\n            <div class=\"slot-status\">\n              <el-tag :type=\"slot.disabled ? 'danger' : 'success'\" size=\"mini\">\n                {{ slot.disabled ? '已预约' : '可预约' }}\n              </el-tag>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      <!-- 预约须知 -->\n      <div class=\"booking-notice\">\n        <h3><i class=\"el-icon-warning\"></i> 预约须知</h3>\n        <ul class=\"notice-list\">\n          <li>请提前至少1小时预约，当天预约需要电话确认</li>\n          <li>预约成功后请按时到场，迟到超过15分钟将自动取消</li>\n          <li>如需取消预约，请提前2小时联系客服</li>\n          <li>场地内禁止吸烟，请爱护场地设施</li>\n          <li>运动时请注意安全，建议穿着运动服装和运动鞋</li>\n        </ul>\n      </div>\n    </div>\n\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"handleClose\">关闭</el-button>\n      <el-button type=\"primary\" @click=\"bookVenue\" :disabled=\"venue.shangxiaTypes !== 1\">\n        <i class=\"el-icon-date\"></i>\n        立即预约\n      </el-button>\n    </div>\n  </el-dialog>\n</template>\n\n<script>\nexport default {\n  name: 'VenueDetailsDialog',\n  props: {\n    visible: {\n      type: Boolean,\n      default: false\n    },\n    venue: {\n      type: Object,\n      default: () => ({})\n    }\n  },\n  \n  data() {\n    return {\n      dialogVisible: false,\n      \n      timeSlots: [\n        { label: '08:00-10:00', value: '08:00-10:00', disabled: false },\n        { label: '10:00-12:00', value: '10:00-12:00', disabled: true },\n        { label: '12:00-14:00', value: '12:00-14:00', disabled: false },\n        { label: '14:00-16:00', value: '14:00-16:00', disabled: false },\n        { label: '16:00-18:00', value: '16:00-18:00', disabled: true },\n        { label: '18:00-20:00', value: '18:00-20:00', disabled: false },\n        { label: '20:00-22:00', value: '20:00-22:00', disabled: false }\n      ]\n    }\n  },\n  \n  watch: {\n    visible(val) {\n      this.dialogVisible = val\n      if (val) {\n        this.loadVenueDetails()\n      }\n    },\n    \n    dialogVisible(val) {\n      this.$emit('update:visible', val)\n    }\n  },\n  \n  methods: {\n    // 加载场地详情\n    loadVenueDetails() {\n      // 这里可以加载更详细的场地信息\n      // 包括已预约的时间段等\n    },\n    \n    // 预约场地\n    bookVenue() {\n      const role = this.$storage.get('role')\n      if (!role || role !== '用户') {\n        this.$message.warning('请先登录用户账户')\n        this.$router.push('/login')\n        return\n      }\n\n      this.$emit('book-venue', this.venue)\n      this.handleClose()\n    },\n    \n    // 关闭对话框\n    handleClose() {\n      this.dialogVisible = false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n::v-deep .details-dialog {\n  .el-dialog__header {\n    background: linear-gradient(45deg, #00c292, #00a085);\n    color: white;\n    padding: 20px 30px;\n    \n    .el-dialog__title {\n      color: white;\n      font-size: 18px;\n      font-weight: 600;\n    }\n    \n    .el-dialog__close {\n      color: white;\n      font-size: 20px;\n    }\n  }\n  \n  .el-dialog__body {\n    padding: 30px;\n    max-height: 70vh;\n    overflow-y: auto;\n  }\n}\n\n.details-content {\n  .venue-header {\n    display: flex;\n    gap: 25px;\n    margin-bottom: 30px;\n    padding: 25px;\n    background: #f8fffe;\n    border-radius: 12px;\n    border: 1px solid rgba(0, 194, 146, 0.1);\n    \n    .venue-image {\n      flex: 0 0 200px;\n      \n      img {\n        width: 200px;\n        height: 150px;\n        object-fit: cover;\n        border-radius: 8px;\n      }\n    }\n    \n    .venue-basic-info {\n      flex: 1;\n      \n      h2 {\n        font-size: 24px;\n        color: #2c3e50;\n        margin: 0 0 15px 0;\n      }\n      \n      .venue-code {\n        color: #666;\n        margin-bottom: 10px;\n        font-size: 14px;\n      }\n      \n      .venue-status {\n        margin-bottom: 15px;\n      }\n      \n      .venue-price {\n        .current-price {\n          font-size: 28px;\n          font-weight: 700;\n          color: #00c292;\n        }\n        \n        .original-price {\n          font-size: 18px;\n          color: #999;\n          text-decoration: line-through;\n          margin-left: 10px;\n        }\n        \n        .price-unit {\n          font-size: 14px;\n          color: #666;\n          margin-left: 5px;\n        }\n      }\n    }\n  }\n  \n  .venue-details {\n    margin-bottom: 30px;\n    \n    .detail-section {\n      h3 {\n        font-size: 18px;\n        color: #2c3e50;\n        margin-bottom: 20px;\n        \n        i {\n          color: #00c292;\n          margin-right: 8px;\n        }\n      }\n      \n      .detail-list {\n        .detail-item {\n          display: flex;\n          margin-bottom: 12px;\n          \n          .label {\n            flex: 0 0 80px;\n            color: #666;\n            font-size: 14px;\n          }\n          \n          .value {\n            color: #2c3e50;\n            font-size: 14px;\n          }\n        }\n      }\n      \n      .recommendation {\n        p {\n          color: #666;\n          line-height: 1.6;\n          margin: 0;\n        }\n      }\n    }\n  }\n  \n  .venue-description {\n    margin-bottom: 30px;\n    \n    h3 {\n      font-size: 18px;\n      color: #2c3e50;\n      margin-bottom: 15px;\n      \n      i {\n        color: #00c292;\n        margin-right: 8px;\n      }\n    }\n    \n    .description-content {\n      color: #666;\n      line-height: 1.6;\n      padding: 20px;\n      background: #f8f9fa;\n      border-radius: 8px;\n    }\n  }\n  \n  .available-slots {\n    margin-bottom: 30px;\n    \n    h3 {\n      font-size: 18px;\n      color: #2c3e50;\n      margin-bottom: 20px;\n      \n      i {\n        color: #00c292;\n        margin-right: 8px;\n      }\n    }\n    \n    .time-slots {\n      display: grid;\n      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));\n      gap: 15px;\n      \n      .time-slot {\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n        padding: 15px;\n        background: white;\n        border: 2px solid #e8f4f8;\n        border-radius: 8px;\n        transition: all 0.3s ease;\n        \n        &:not(.disabled):hover {\n          border-color: #00c292;\n          background: #f8fffe;\n        }\n        \n        &.disabled {\n          background: #f5f5f5;\n          border-color: #ddd;\n        }\n        \n        .slot-time {\n          font-weight: 600;\n          color: #2c3e50;\n        }\n      }\n    }\n  }\n  \n  .booking-notice {\n    h3 {\n      font-size: 18px;\n      color: #2c3e50;\n      margin-bottom: 15px;\n      \n      i {\n        color: #f56c6c;\n        margin-right: 8px;\n      }\n    }\n    \n    .notice-list {\n      margin: 0;\n      padding-left: 20px;\n      \n      li {\n        color: #666;\n        line-height: 1.6;\n        margin-bottom: 8px;\n      }\n    }\n  }\n}\n\n.dialog-footer {\n  text-align: right;\n  \n  .el-button {\n    border-radius: 8px;\n    font-weight: 600;\n    \n    &.el-button--primary {\n      background: linear-gradient(45deg, #00c292, #00a085);\n      border: none;\n      \n      &:hover:not(:disabled) {\n        background: linear-gradient(45deg, #00a085, #008f75);\n      }\n      \n      &:disabled {\n        background: #ddd;\n        color: #999;\n      }\n    }\n  }\n}\n</style>\n"]}]}