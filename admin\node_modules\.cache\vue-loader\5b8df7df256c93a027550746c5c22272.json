{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\center.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\center.vue", "mtime": 1750590342011}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["center.vue"], "names": [], "mappings": ";AAqIA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "center.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"center-container\">\r\n    <div class=\"center-header\">\r\n      <h2>个人信息管理</h2>\r\n      <p>管理您的个人资料和账户信息</p>\r\n    </div>\r\n\r\n    <el-card class=\"center-card\">\r\n      <div slot=\"header\">\r\n        <span>基本信息</span>\r\n      </div>\r\n\r\n      <el-form\r\n        class=\"center-form\"\r\n        ref=\"ruleForm\"\r\n        :model=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        label-width=\"120px\"\r\n      >\r\n        <el-row :gutter=\"20\">\r\n          <!-- 用户信息字段 -->\r\n          <el-col :span=\"12\" v-if=\"flag=='yonghu'\">\r\n            <el-form-item label=\"用户姓名\" prop=\"yonghuName\">\r\n              <el-input\r\n                v-model=\"ruleForm.yonghuName\"\r\n                placeholder=\"请输入用户姓名\"\r\n                clearable\r\n                prefix-icon=\"el-icon-user\">\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"flag=='yonghu'\">\r\n            <el-form-item label=\"用户手机号\" prop=\"yonghuPhone\">\r\n              <el-input\r\n                v-model=\"ruleForm.yonghuPhone\"\r\n                placeholder=\"请输入手机号\"\r\n                clearable\r\n                prefix-icon=\"el-icon-phone\">\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"flag=='yonghu'\">\r\n            <el-form-item label=\"身份证号\" prop=\"yonghuIdNumber\">\r\n              <el-input\r\n                v-model=\"ruleForm.yonghuIdNumber\"\r\n                placeholder=\"请输入身份证号\"\r\n                clearable\r\n                prefix-icon=\"el-icon-postcard\">\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"flag=='yonghu'\">\r\n            <el-form-item label=\"电子邮箱\" prop=\"yonghuEmail\">\r\n              <el-input\r\n                v-model=\"ruleForm.yonghuEmail\"\r\n                placeholder=\"请输入电子邮箱\"\r\n                clearable\r\n                prefix-icon=\"el-icon-message\">\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <el-col :span=\"12\" v-if=\"flag!='users'\">\r\n            <el-form-item label=\"性别\" prop=\"sexTypes\">\r\n              <el-select v-model=\"ruleForm.sexTypes\" placeholder=\"请选择性别\" style=\"width: 100%\">\r\n                <el-option\r\n                  v-for=\"(item,index) in sexTypesOptions\"\r\n                  v-bind:key=\"item.codeIndex\"\r\n                  :label=\"item.indexName\"\r\n                  :value=\"item.codeIndex\">\r\n                </el-option>\r\n              </el-select>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <!-- 管理员用户名字段 -->\r\n          <el-col :span=\"12\" v-if=\"flag=='users'\">\r\n            <el-form-item label=\"用户名\" prop=\"username\">\r\n              <el-input\r\n                v-model=\"ruleForm.username\"\r\n                placeholder=\"请输入用户名\"\r\n                prefix-icon=\"el-icon-user\">\r\n              </el-input>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <!-- 用户头像上传 -->\r\n          <el-col :span=\"24\" v-if=\"flag=='yonghu'\">\r\n            <el-form-item label=\"用户头像\" prop=\"yonghuPhoto\">\r\n              <file-upload\r\n                tip=\"点击上传头像照片\"\r\n                action=\"file/upload\"\r\n                :limit=\"1\"\r\n                :multiple=\"false\"\r\n                :fileUrls=\"ruleForm.yonghuPhoto?ruleForm.yonghuPhoto:''\"\r\n                @change=\"yonghuPhotoUploadChange\"\r\n              ></file-upload>\r\n            </el-form-item>\r\n          </el-col>\r\n\r\n          <!-- 操作按钮 -->\r\n          <el-col :span=\"24\">\r\n            <el-form-item>\r\n              <el-button type=\"primary\" @click=\"onUpdateHandler\" :loading=\"loading\">\r\n                <i class=\"el-icon-check\"></i>\r\n                保存修改\r\n              </el-button>\r\n              <el-button @click=\"resetForm\">\r\n                <i class=\"el-icon-refresh\"></i>\r\n                重置\r\n              </el-button>\r\n            </el-form-item>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n    </el-card>\r\n\r\n    <!-- 密码修改卡片 -->\r\n    <el-card class=\"center-card\" style=\"margin-top: 20px;\">\r\n      <div slot=\"header\">\r\n        <span>安全设置</span>\r\n      </div>\r\n      <el-button type=\"warning\" @click=\"goToPasswordChange\">\r\n        <i class=\"el-icon-key\"></i>\r\n        修改密码\r\n      </el-button>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n<script>\r\n// 数字，邮件，手机，url，身份证校验\r\nimport { isNumber,isIntNumer,isEmail,isMobile,isPhone,isURL,checkIdCard } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  data() {\r\n    return {\r\n      ruleForm: {},\r\n      flag: '',\r\n      usersFlag: false,\r\n      sexTypesOptions: [],\r\n      loading: false,\r\n      rules: {\r\n        yonghuName: [\r\n          { required: true, message: '请输入用户姓名', trigger: 'blur' }\r\n        ],\r\n        yonghuPhone: [\r\n          { required: true, message: '请输入手机号', trigger: 'blur' },\r\n          { validator: this.validatePhone, trigger: 'blur' }\r\n        ],\r\n        yonghuIdNumber: [\r\n          { required: true, message: '请输入身份证号', trigger: 'blur' },\r\n          { validator: this.validateIdCard, trigger: 'blur' }\r\n        ],\r\n        yonghuEmail: [\r\n          { validator: this.validateEmail, trigger: 'blur' }\r\n        ],\r\n        sexTypes: [\r\n          { required: true, message: '请选择性别', trigger: 'change' }\r\n        ],\r\n        username: [\r\n          { required: true, message: '请输入用户名', trigger: 'blur' },\r\n          { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.init();\r\n  },\r\n  methods: {\r\n    init() {\r\n      // 获取当前登录用户的信息\r\n      var table = this.$storage.get(\"sessionTable\");\r\n      this.sessionTable = this.$storage.get(\"sessionTable\");\r\n      this.role = this.$storage.get(\"role\");\r\n      this.flag = table;\r\n\r\n      // 获取用户信息\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.ruleForm = data.data;\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n\r\n      // 获取性别选项\r\n      this.$http({\r\n        url: `dictionary/page?page=1&limit=100&sort=&order=&dicCode=sex_types`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.sexTypesOptions = data.data.list;\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      });\r\n    },\r\n\r\n    // 表单验证方法\r\n    validatePhone(rule, value, callback) {\r\n      if (value && !isMobile(value)) {\r\n        callback(new Error('请输入正确的手机号格式'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n\r\n    validateIdCard(rule, value, callback) {\r\n      if (value && !checkIdCard(value)) {\r\n        callback(new Error('请输入正确的身份证号格式'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n\r\n    validateEmail(rule, value, callback) {\r\n      if (value && !isEmail(value)) {\r\n        callback(new Error('请输入正确的邮箱格式'));\r\n      } else {\r\n        callback();\r\n      }\r\n    },\r\n\r\n    yonghuPhotoUploadChange(fileUrls) {\r\n      this.ruleForm.yonghuPhoto = fileUrls;\r\n    },\r\n\r\n    resetForm() {\r\n      this.$refs.ruleForm.resetFields();\r\n      this.init();\r\n    },\r\n\r\n    goToPasswordChange() {\r\n      this.$router.push('/updatePassword');\r\n    },\r\n\r\n    onUpdateHandler() {\r\n      this.$refs.ruleForm.validate((valid) => {\r\n        if (valid) {\r\n          this.loading = true;\r\n          this.$http({\r\n            url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n            method: \"post\",\r\n            data: this.ruleForm\r\n          }).then(({ data }) => {\r\n            this.loading = false;\r\n            if (data && data.code === 0) {\r\n              this.$message({\r\n                message: \"修改信息成功\",\r\n                type: \"success\",\r\n                duration: 1500\r\n              });\r\n            } else {\r\n              this.$message.error(data.msg);\r\n            }\r\n          }).catch(() => {\r\n            this.loading = false;\r\n          });\r\n        } else {\r\n          this.$message.error('请检查表单信息');\r\n          return false;\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.center-container {\r\n  padding: 20px;\r\n\r\n  .center-header {\r\n    text-align: center;\r\n    margin-bottom: 30px;\r\n\r\n    h2 {\r\n      font-size: 28px;\r\n      color: #2c3e50;\r\n      margin-bottom: 10px;\r\n    }\r\n\r\n    p {\r\n      font-size: 16px;\r\n      color: #909399;\r\n    }\r\n  }\r\n\r\n  .center-card {\r\n    margin-bottom: 20px;\r\n\r\n    ::v-deep .el-card__header {\r\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\r\n      color: white;\r\n\r\n      span {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n      }\r\n    }\r\n\r\n    .center-form {\r\n      padding: 20px 0;\r\n\r\n      .el-form-item {\r\n        margin-bottom: 25px;\r\n\r\n        ::v-deep .el-form-item__label {\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n        }\r\n\r\n        ::v-deep .el-input__inner {\r\n          border-radius: 6px;\r\n          border: 1px solid #dcdfe6;\r\n          transition: all 0.3s ease;\r\n\r\n          &:focus {\r\n            border-color: #00c292;\r\n            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n          }\r\n        }\r\n\r\n        ::v-deep .el-select {\r\n          width: 100%;\r\n        }\r\n      }\r\n\r\n      .el-button {\r\n        border-radius: 6px;\r\n        padding: 12px 24px;\r\n        font-weight: 600;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n          }\r\n        }\r\n\r\n        &.el-button--warning {\r\n          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\r\n          border: none;\r\n          color: white;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #f5576c 0%, #e74c3c 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n::v-deep .el-upload {\r\n  .el-upload-dragger {\r\n    border-radius: 8px;\r\n    border: 2px dashed #d9d9d9;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      border-color: #00c292;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}