{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\index\\IndexHeader.vue", "mtime": 1750599440132}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["IndexHeader.vue"], "names": [], "mappings": ";AA4CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "IndexHeader.vue", "sourceRoot": "src/components/index", "sourcesContent": ["<template>\r\n    <!-- <el-header>\r\n        <el-menu background-color=\"#00c292\" text-color=\"#FFFFFF\" active-text-color=\"#FFFFFF\" mode=\"horizontal\">\r\n            <div class=\"fl title\">{{this.$project.projectName}}</div>\r\n            <div class=\"fr logout\" style=\"display:flex;\">\r\n                <el-menu-item index=\"3\">\r\n                    <div>{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n                </el-menu-item>\r\n                <el-menu-item @click=\"onLogout\" index=\"2\">\r\n                    <div>退出登�?/div>\r\n                </el-menu-item>\r\n            </div>\r\n        </el-menu>\r\n    </el-header> -->\r\n    <div class=\"navbar\" :style=\"{backgroundColor:heads.headBgColor,height:heads.headHeight,boxShadow:heads.headBoxShadow,lineHeight:heads.headHeight}\">\r\n        <div class=\"title-menu\" :style=\"{justifyContent:heads.headTitleStyle=='1'?'flex-start':'center'}\">\r\n            <el-image v-if=\"heads.headTitleImg\" class=\"title-img\" :style=\"{width:heads.headTitleImgWidth,height:heads.headTitleImgHeight,boxShadow:heads.headTitleImgBoxShadow,borderRadius:heads.headTitleImgBorderRadius}\" :src=\"heads.headTitleImgUrl\" fit=\"cover\"></el-image>\r\n            <div class=\"title-name\" :style=\"{color:heads.headFontColor,fontSize:heads.headFontSize}\">{{this.$project.projectName}}</div>\r\n\r\n            <!-- 导航菜单 -->\r\n            <div class=\"nav-menu\">\r\n                <div class=\"nav-item\" @click=\"goToHome\">\r\n                    <i class=\"el-icon-house\"></i>\r\n                    <span>首页</span>\r\n                </div>\r\n                <div class=\"nav-item\" @click=\"goToForum\">\r\n                    <i class=\"el-icon-chat-dot-round\"></i>\r\n                    <span>论坛</span>\r\n                </div>\r\n                <div class=\"nav-item\" @click=\"goToBooking\">\r\n                    <i class=\"el-icon-date\"></i>\r\n                    <span>预约</span>\r\n                </div>\r\n            </div>\r\n        </div>\r\n        <div class=\"right-menu\">\r\n            <div class=\"user-info\" :style=\"{color:heads.headUserInfoFontColor,fontSize:heads.headUserInfoFontSize}\">{{this.$storage.get('role')}} {{this.$storage.get('adminName')}}</div>\r\n\t\t\t<div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onIndexTap\">退出到前台</div>\r\n            <div class=\"logout\" :style=\"{color:heads.headLogoutFontColor,fontSize:heads.headLogoutFontSize}\" @click=\"onLogout\">退出登录</div>\r\n        </div>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\n    export default {\r\n        data() {\r\n            return {\r\n                dialogVisible: false,\r\n                ruleForm: {},\r\n                user: {},\r\n                heads: {\"headLogoutFontHoverColor\":\"#fff\",\"headFontSize\":\"20px\",\"headUserInfoFontColor\":\"#fff\",\"headBoxShadow\":\"0 2px 8px rgba(0,0,0,0.1)\",\"headTitleImgHeight\":\"44px\",\"headLogoutFontHoverBgColor\":\"#00c292\",\"headFontColor\":\"#fff\",\"headTitleImg\":false,\"headHeight\":\"60px\",\"headTitleImgBorderRadius\":\"22px\",\"headTitleImgUrl\":\"http://codegen.caihongy.cn/20201021/cc7d45d9c8164b58b18351764eba9be1.jpg\",\"headBgColor\":\"#34495e\",\"headTitleImgBoxShadow\":\"0 1px 6px #444\",\"headLogoutFontColor\":\"#fff\",\"headUserInfoFontSize\":\"16px\",\"headTitleImgWidth\":\"44px\",\"headTitleStyle\":\"1\",\"headLogoutFontSize\":\"16px\"},\r\n            };\r\n        },\r\n        created() {\r\n            this.setHeaderStyle()\r\n        },\r\n        mounted() {\r\n            let sessionTable = this.$storage.get(\"sessionTable\")\r\n            this.$http({\r\n                url: sessionTable + '/session',\r\n                method: \"get\"\r\n            }).then(({\r\n                         data\r\n                     }) => {\r\n                if (data && data.code === 0) {\r\n                    this.user = data.data;\r\n                } else {\r\n                    let message = this.$message\r\n                    message.error(data.msg);\r\n                }\r\n            });\r\n        },\r\n        methods: {\r\n            onLogout() {\r\n                let storage = this.$storage\r\n                let router = this.$router\r\n                storage.remove(\"Token\");\r\n                router.replace({\r\n                    name: \"login\"\r\n                });\r\n            },\r\n            onIndexTap(){\r\n                window.location.href = `${this.$base.indexUrl}`\r\n            },\r\n            // 导航方法\r\n            goToHome() {\r\n                this.$router.push('/')\r\n            },\r\n            goToForum() {\r\n                this.$router.push('/forum-new')\r\n            },\r\n            goToBooking() {\r\n                this.$router.push('/changdi-booking')\r\n            },\r\n            setHeaderStyle() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.navbar .right-menu .logout').forEach(el=>{\r\n                        el.addEventListener(\"mouseenter\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = this.heads.headLogoutFontHoverBgColor\r\n                            el.style.color = this.heads.headLogoutFontHoverColor\r\n                        })\r\n                        el.addEventListener(\"mouseleave\", e => {\r\n                            e.stopPropagation()\r\n                            el.style.backgroundColor = \"transparent\"\r\n                            el.style.color = this.heads.headLogoutFontColor\r\n                        })\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n    .navbar {\r\n        height: 60px;\r\n        line-height: 60px;\r\n        width: 100%;\r\n        padding: 0 34px;\r\n        box-sizing: border-box;\r\n        background-color: #ff00ff;\r\n        position: relative;\r\n        z-index: 111;\r\n\r\n    .right-menu {\r\n        position: absolute;\r\n        right: 34px;\r\n        top: 0;\r\n        height: 100%;\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        align-items: center;\r\n        z-index: 111;\r\n\r\n    .user-info {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n    }\r\n\r\n    .logout {\r\n        font-size: 16px;\r\n        color: red;\r\n        padding: 0 12px;\r\n        cursor: pointer;\r\n    }\r\n\r\n    }\r\n\r\n    .title-menu {\r\n        display: flex;\r\n        justify-content: flex-start;\r\n        align-items: center;\r\n        width: 100%;\r\n        height: 100%;\r\n\r\n    .title-img {\r\n        width: 44px;\r\n        height: 44px;\r\n        border-radius: 22px;\r\n        box-shadow: 0 1px 6px #444;\r\n        margin-right: 16px;\r\n    }\r\n\r\n    .title-name {\r\n        font-size: 24px;\r\n        color: #fff;\r\n        font-weight: 700;\r\n        margin-right: 40px;\r\n    }\r\n\r\n    .nav-menu {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 20px;\r\n\r\n        .nav-item {\r\n            display: flex;\r\n            align-items: center;\r\n            gap: 6px;\r\n            padding: 8px 16px;\r\n            border-radius: 6px;\r\n            cursor: pointer;\r\n            transition: all 0.3s ease;\r\n            color: rgba(255, 255, 255, 0.8);\r\n            font-size: 14px;\r\n\r\n            &:hover {\r\n                background-color: rgba(255, 255, 255, 0.1);\r\n                color: #fff;\r\n                transform: translateY(-1px);\r\n            }\r\n\r\n            i {\r\n                font-size: 16px;\r\n            }\r\n        }\r\n    }\r\n    }\r\n    }\r\n    // .el-header .fr {\r\n       // \tfloat: right;\r\n       // }\r\n\r\n    // .el-header .fl {\r\n       // \tfloat: left;\r\n       // }\r\n\r\n    // .el-header {\r\n       // \twidth: 100%;\r\n       // \tcolor: #333;\r\n       // \ttext-align: center;\r\n       // \tline-height: 60px;\r\n       // \tpadding: 0;\r\n       // \tz-index: 99;\r\n       // }\r\n\r\n    // .logo {\r\n       // \twidth: 60px;\r\n       // \theight: 60px;\r\n       // \tmargin-left: 70px;\r\n       // }\r\n\r\n    // .avator {\r\n       // \twidth: 40px;\r\n       // \theight: 40px;\r\n       // \tbackground: #ffffff;\r\n       // \tborder-radius: 50%;\r\n       // }\r\n\r\n    // .title {\r\n       // \tcolor: #ffffff;\r\n       // \tfont-size: 20px;\r\n       // \tfont-weight: bold;\r\n       // \tmargin-left: 20px;\r\n       // }\r\n</style>\r\n\r\n"]}]}