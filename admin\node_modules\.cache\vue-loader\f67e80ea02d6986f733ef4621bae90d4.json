{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue", "mtime": 1750585906349}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["update-password.vue"], "names": [], "mappings": ";AAyBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "update-password.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div>\r\n    <el-form\r\n      class=\"detail-form-content\"\r\n      ref=\"ruleForm\"\r\n      :rules=\"rules\"\r\n      :model=\"ruleForm\"\r\n      label-width=\"80px\"\r\n    >\r\n      <el-form-item label=\"原密�? prop=\"password\">\r\n        <el-input v-model=\"ruleForm.password\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"新密�? prop=\"newpassword\">\r\n        <el-input type=\"password\" v-model=\"ruleForm.newpassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item label=\"确认密码\" prop=\"repassword\">\r\n        <el-input type=\"password\" v-model=\"ruleForm.repassword\" show-password></el-input>\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" @click=\"onUpdateHandler\">确认</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      dialogVisible: false,\r\n      ruleForm: {},\r\n      user: {},\r\n      rules: {\r\n        password: [\r\n          {\r\n            required: true,\r\n            message: \"密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        newpassword: [\r\n          {\r\n            required: true,\r\n            message: \"新密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        repassword: [\r\n          {\r\n            required: true,\r\n            message: \"确认密码不能为空\",\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.$http({\r\n      url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n      method: \"get\"\r\n    }).then(({ data }) => {\r\n      if (data && data.code === 0) {\r\n        this.user = data.data;\r\n      } else {\r\n        this.$message.error(data.msg);\r\n      }\r\n    });\r\n  },\r\n  methods: {\r\n    onLogout() {\r\n      this.$storage.remove(\"Token\");\r\n      this.$router.replace({ name: \"login\" });\r\n    },\r\n    // 修改密码\r\n    onUpdateHandler() {\r\n      this.$refs[\"ruleForm\"].validate(valid => {\r\n        if (valid) {\r\n          var password = \"\";\r\n          if (this.user.mima) {\r\n            password = this.user.mima;\r\n          } else if (this.user.password) {\r\n            password = this.user.password;\r\n          }\r\n          if (this.ruleForm.password != password) {\r\n            this.$message.error(\"原密码错误\");\r\n            return;\r\n          }\r\n          if (this.ruleForm.newpassword != this.ruleForm.repassword) {\r\n            this.$message.error(\"两次密码输入不一致\");\r\n            return;\r\n          }\r\n          this.user.password = this.ruleForm.newpassword;\r\n          this.user.mima = this.ruleForm.newpassword;\r\n          this.$http({\r\n            url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n            method: \"post\",\r\n            data: this.user\r\n          }).then(({ data }) => {\r\n            if (data && data.code === 0) {\r\n              this.$message({\r\n                message: \"修改密码成功,下次登录系统生效\",\r\n                type: \"success\",\r\n                duration: 1500,\r\n                onClose: () => {\r\n                }\r\n              });\r\n            } else {\r\n              this.$message.error(data.msg);\r\n            }\r\n          });\r\n        }\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n</style>\r\n\r\n"]}]}