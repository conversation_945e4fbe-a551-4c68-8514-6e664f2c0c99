{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\update-password.vue", "mtime": 1750593551799}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["update-password.vue"], "names": [], "mappings": ";AA8GA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "update-password.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"update-password-container\">\r\n    <div class=\"password-header\">\r\n      <div class=\"header-content\">\r\n        <div class=\"title-section\">\r\n          <h2><i class=\"el-icon-lock\"></i> 修改密码</h2>\r\n          <p>为了您的账户安全，请定期更换密码</p>\r\n        </div>\r\n        <div class=\"security-tips\">\r\n          <el-alert\r\n            title=\"安全提示\"\r\n            type=\"info\"\r\n            :closable=\"false\"\r\n            show-icon>\r\n            <template slot=\"description\">\r\n              <ul>\r\n                <li>密码长度至少6位，建议包含字母、数字和特殊字符</li>\r\n                <li>不要使用过于简单的密码，如123456、password等</li>\r\n                <li>修改密码后需要重新登录</li>\r\n              </ul>\r\n            </template>\r\n          </el-alert>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <el-card class=\"password-card\">\r\n      <div class=\"user-info\">\r\n        <el-avatar :size=\"60\" :src=\"user.yonghuPhoto || user.touxiang\" icon=\"el-icon-user-solid\"></el-avatar>\r\n        <div class=\"user-details\">\r\n          <h3>{{ user.yonghuName || user.username || '用户' }}</h3>\r\n          <p>{{ user.yonghuPhone || user.phone || '暂无手机号' }}</p>\r\n        </div>\r\n      </div>\r\n\r\n      <el-form\r\n        class=\"password-form\"\r\n        ref=\"ruleForm\"\r\n        :rules=\"rules\"\r\n        :model=\"ruleForm\"\r\n        label-width=\"120px\">\r\n\r\n        <el-form-item label=\"当前密码\" prop=\"password\">\r\n          <el-input\r\n            v-model=\"ruleForm.password\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请输入当前密码\"\r\n            prefix-icon=\"el-icon-lock\"\r\n            clearable>\r\n          </el-input>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"新密码\" prop=\"newpassword\">\r\n          <el-input\r\n            v-model=\"ruleForm.newpassword\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请输入新密码\"\r\n            prefix-icon=\"el-icon-key\"\r\n            clearable\r\n            @input=\"checkPasswordStrength\">\r\n          </el-input>\r\n          <div class=\"password-strength\" v-if=\"ruleForm.newpassword\">\r\n            <div class=\"strength-label\">密码强度：</div>\r\n            <div class=\"strength-bar\">\r\n              <div\r\n                class=\"strength-fill\"\r\n                :class=\"passwordStrength.class\"\r\n                :style=\"{width: passwordStrength.width}\">\r\n              </div>\r\n            </div>\r\n            <span class=\"strength-text\" :class=\"passwordStrength.class\">\r\n              {{ passwordStrength.text }}\r\n            </span>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"确认新密码\" prop=\"repassword\">\r\n          <el-input\r\n            v-model=\"ruleForm.repassword\"\r\n            type=\"password\"\r\n            show-password\r\n            placeholder=\"请再次输入新密码\"\r\n            prefix-icon=\"el-icon-check\"\r\n            clearable>\r\n          </el-input>\r\n        </el-form-item>\r\n\r\n        <div class=\"form-actions\">\r\n          <el-button\r\n            type=\"primary\"\r\n            @click=\"onUpdateHandler\"\r\n            :loading=\"loading\"\r\n            size=\"large\">\r\n            <i class=\"el-icon-check\"></i>\r\n            确认修改\r\n          </el-button>\r\n          <el-button\r\n            @click=\"resetForm\"\r\n            size=\"large\">\r\n            <i class=\"el-icon-refresh\"></i>\r\n            重置\r\n          </el-button>\r\n        </div>\r\n      </el-form>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    // 自定义验证规则\r\n    const validatePassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请输入当前密码'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const validateNewPassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请输入新密码'));\r\n      } else if (value.length < 6) {\r\n        callback(new Error('密码长度至少6位'));\r\n      } else if (value === this.ruleForm.password) {\r\n        callback(new Error('新密码不能与当前密码相同'));\r\n      } else {\r\n        // 触发确认密码的重新验证\r\n        if (this.ruleForm.repassword !== '') {\r\n          this.$refs.ruleForm.validateField('repassword');\r\n        }\r\n        callback();\r\n      }\r\n    };\r\n\r\n    const validateRePassword = (rule, value, callback) => {\r\n      if (!value) {\r\n        callback(new Error('请确认新密码'));\r\n      } else if (value !== this.ruleForm.newpassword) {\r\n        callback(new Error('两次输入的密码不一致'));\r\n      } else {\r\n        callback();\r\n      }\r\n    };\r\n\r\n    return {\r\n      loading: false,\r\n      ruleForm: {\r\n        password: '',\r\n        newpassword: '',\r\n        repassword: ''\r\n      },\r\n      user: {},\r\n      passwordStrength: {\r\n        width: '0%',\r\n        class: '',\r\n        text: ''\r\n      },\r\n      rules: {\r\n        password: [{ validator: validatePassword, trigger: 'blur' }],\r\n        newpassword: [{ validator: validateNewPassword, trigger: 'blur' }],\r\n        repassword: [{ validator: validateRePassword, trigger: 'blur' }]\r\n      }\r\n    };\r\n  },\r\n  mounted() {\r\n    this.getUserInfo();\r\n  },\r\n  methods: {\r\n    // 获取用户信息\r\n    getUserInfo() {\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/session`,\r\n        method: \"get\"\r\n      }).then(({ data }) => {\r\n        if (data && data.code === 0) {\r\n          this.user = data.data;\r\n        } else {\r\n          this.$message.error(data.msg);\r\n        }\r\n      }).catch(() => {\r\n        this.$message.error('获取用户信息失败');\r\n      });\r\n    },\r\n\r\n    // 检查密码强度\r\n    checkPasswordStrength() {\r\n      const password = this.ruleForm.newpassword;\r\n      if (!password) {\r\n        this.passwordStrength = { width: '0%', class: '', text: '' };\r\n        return;\r\n      }\r\n\r\n      let score = 0;\r\n      let feedback = [];\r\n\r\n      // 长度检查\r\n      if (password.length >= 8) score += 25;\r\n      else if (password.length >= 6) score += 15;\r\n      else feedback.push('至少6位');\r\n\r\n      // 包含小写字母\r\n      if (/[a-z]/.test(password)) score += 25;\r\n      else feedback.push('包含小写字母');\r\n\r\n      // 包含大写字母\r\n      if (/[A-Z]/.test(password)) score += 25;\r\n      else feedback.push('包含大写字母');\r\n\r\n      // 包含数字\r\n      if (/\\d/.test(password)) score += 15;\r\n      else feedback.push('包含数字');\r\n\r\n      // 包含特殊字符\r\n      if (/[!@#$%^&*(),.?\":{}|<>]/.test(password)) score += 10;\r\n      else feedback.push('包含特殊字符');\r\n\r\n      // 设置强度显示\r\n      if (score < 30) {\r\n        this.passwordStrength = {\r\n          width: '25%',\r\n          class: 'weak',\r\n          text: '弱'\r\n        };\r\n      } else if (score < 60) {\r\n        this.passwordStrength = {\r\n          width: '50%',\r\n          class: 'medium',\r\n          text: '中等'\r\n        };\r\n      } else if (score < 80) {\r\n        this.passwordStrength = {\r\n          width: '75%',\r\n          class: 'strong',\r\n          text: '强'\r\n        };\r\n      } else {\r\n        this.passwordStrength = {\r\n          width: '100%',\r\n          class: 'very-strong',\r\n          text: '很强'\r\n        };\r\n      }\r\n    },\r\n\r\n    // 重置表单\r\n    resetForm() {\r\n      this.$refs.ruleForm.resetFields();\r\n      this.ruleForm = {\r\n        password: '',\r\n        newpassword: '',\r\n        repassword: ''\r\n      };\r\n      this.passwordStrength = { width: '0%', class: '', text: '' };\r\n    },\r\n\r\n    // 修改密码\r\n    onUpdateHandler() {\r\n      this.$refs[\"ruleForm\"].validate(valid => {\r\n        if (valid) {\r\n          // 验证原密码\r\n          const currentPassword = this.user.mima || this.user.password || '';\r\n          if (this.ruleForm.password !== currentPassword) {\r\n            this.$message.error(\"当前密码错误\");\r\n            return;\r\n          }\r\n\r\n          // 确认对话框\r\n          this.$confirm('确定要修改密码吗？修改后需要重新登录。', '确认修改', {\r\n            confirmButtonText: '确定',\r\n            cancelButtonText: '取消',\r\n            type: 'warning'\r\n          }).then(() => {\r\n            this.updatePassword();\r\n          }).catch(() => {\r\n            this.$message.info('已取消修改');\r\n          });\r\n        }\r\n      });\r\n    },\r\n\r\n    // 执行密码更新\r\n    updatePassword() {\r\n      this.loading = true;\r\n\r\n      // 更新用户密码\r\n      const updateData = { ...this.user };\r\n      updateData.password = this.ruleForm.newpassword;\r\n      updateData.mima = this.ruleForm.newpassword;\r\n\r\n      this.$http({\r\n        url: `${this.$storage.get(\"sessionTable\")}/update`,\r\n        method: \"post\",\r\n        data: updateData\r\n      }).then(({ data }) => {\r\n        this.loading = false;\r\n        if (data && data.code === 0) {\r\n          this.$message({\r\n            message: \"密码修改成功！请重新登录\",\r\n            type: \"success\",\r\n            duration: 2000,\r\n            onClose: () => {\r\n              // 清除登录信息并跳转到登录页\r\n              this.$storage.remove(\"Token\");\r\n              this.$storage.remove(\"role\");\r\n              this.$storage.remove(\"sessionTable\");\r\n              this.$storage.remove(\"adminName\");\r\n              this.$router.replace({ name: \"login\" });\r\n            }\r\n          });\r\n        } else {\r\n          this.$message.error(data.msg || '修改密码失败');\r\n        }\r\n      }).catch(() => {\r\n        this.loading = false;\r\n        this.$message.error('网络错误，请稍后重试');\r\n      });\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.update-password-container {\r\n  padding: 20px;\r\n  background: #f5f7fa;\r\n  min-height: calc(100vh - 84px);\r\n\r\n  .password-header {\r\n    margin-bottom: 30px;\r\n\r\n    .header-content {\r\n      .title-section {\r\n        text-align: center;\r\n        margin-bottom: 20px;\r\n\r\n        h2 {\r\n          font-size: 28px;\r\n          color: #2c3e50;\r\n          margin: 0 0 10px 0;\r\n\r\n          i {\r\n            color: #00c292;\r\n            margin-right: 10px;\r\n          }\r\n        }\r\n\r\n        p {\r\n          color: #909399;\r\n          margin: 0;\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      .security-tips {\r\n        max-width: 600px;\r\n        margin: 0 auto;\r\n\r\n        ::v-deep .el-alert {\r\n          border-radius: 8px;\r\n\r\n          .el-alert__description {\r\n            ul {\r\n              margin: 0;\r\n              padding-left: 20px;\r\n\r\n              li {\r\n                margin: 5px 0;\r\n                color: #606266;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .password-card {\r\n    max-width: 600px;\r\n    margin: 0 auto;\r\n    border-radius: 12px;\r\n    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\r\n\r\n    ::v-deep .el-card__body {\r\n      padding: 40px;\r\n    }\r\n\r\n    .user-info {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 40px;\r\n      padding: 20px;\r\n      background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n      border-radius: 8px;\r\n      color: white;\r\n\r\n      .user-details {\r\n        margin-left: 20px;\r\n\r\n        h3 {\r\n          margin: 0 0 5px 0;\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n        }\r\n\r\n        p {\r\n          margin: 0;\r\n          opacity: 0.9;\r\n          font-size: 14px;\r\n        }\r\n      }\r\n    }\r\n\r\n    .password-form {\r\n      .el-form-item {\r\n        margin-bottom: 30px;\r\n\r\n        ::v-deep .el-form-item__label {\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n          font-size: 16px;\r\n        }\r\n\r\n        ::v-deep .el-input__inner {\r\n          height: 50px;\r\n          line-height: 50px;\r\n          border-radius: 8px;\r\n          border: 2px solid #dcdfe6;\r\n          font-size: 16px;\r\n          transition: all 0.3s ease;\r\n\r\n          &:focus {\r\n            border-color: #00c292;\r\n            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n          }\r\n        }\r\n\r\n        ::v-deep .el-input__prefix {\r\n          left: 15px;\r\n\r\n          .el-input__icon {\r\n            line-height: 50px;\r\n            color: #909399;\r\n          }\r\n        }\r\n      }\r\n\r\n      .password-strength {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n\r\n        .strength-label {\r\n          font-size: 14px;\r\n          color: #606266;\r\n          white-space: nowrap;\r\n        }\r\n\r\n        .strength-bar {\r\n          flex: 1;\r\n          height: 6px;\r\n          background: #f0f0f0;\r\n          border-radius: 3px;\r\n          overflow: hidden;\r\n\r\n          .strength-fill {\r\n            height: 100%;\r\n            transition: all 0.3s ease;\r\n            border-radius: 3px;\r\n\r\n            &.weak {\r\n              background: #f56c6c;\r\n            }\r\n\r\n            &.medium {\r\n              background: #e6a23c;\r\n            }\r\n\r\n            &.strong {\r\n              background: #67c23a;\r\n            }\r\n\r\n            &.very-strong {\r\n              background: #00c292;\r\n            }\r\n          }\r\n        }\r\n\r\n        .strength-text {\r\n          font-size: 12px;\r\n          font-weight: 600;\r\n          white-space: nowrap;\r\n\r\n          &.weak {\r\n            color: #f56c6c;\r\n          }\r\n\r\n          &.medium {\r\n            color: #e6a23c;\r\n          }\r\n\r\n          &.strong {\r\n            color: #67c23a;\r\n          }\r\n\r\n          &.very-strong {\r\n            color: #00c292;\r\n          }\r\n        }\r\n      }\r\n\r\n      .form-actions {\r\n        text-align: center;\r\n        margin-top: 40px;\r\n        padding-top: 30px;\r\n        border-top: 1px solid #ebeef5;\r\n\r\n        .el-button {\r\n          border-radius: 8px;\r\n          padding: 15px 40px;\r\n          font-weight: 600;\r\n          font-size: 16px;\r\n          margin: 0 10px;\r\n\r\n          &.el-button--primary {\r\n            background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n            border: none;\r\n\r\n            &:hover {\r\n              background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 响应式设计\r\n@media (max-width: 768px) {\r\n  .update-password-container {\r\n    padding: 10px;\r\n\r\n    .password-card {\r\n      ::v-deep .el-card__body {\r\n        padding: 20px;\r\n      }\r\n\r\n      .user-info {\r\n        flex-direction: column;\r\n        text-align: center;\r\n\r\n        .user-details {\r\n          margin-left: 0;\r\n          margin-top: 15px;\r\n        }\r\n      }\r\n\r\n      .password-form {\r\n        .form-actions {\r\n          .el-button {\r\n            display: block;\r\n            width: 100%;\r\n            margin: 10px 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}