<template>
  <div class="update-password-container">
    <div class="password-header">
      <div class="header-content">
        <div class="title-section">
          <h2><i class="el-icon-lock"></i> 修改密码</h2>
          <p>为了您的账户安全，请定期更换密码</p>
        </div>
        <div class="security-tips">
          <el-alert
            title="安全提示"
            type="info"
            :closable="false"
            show-icon>
            <template slot="description">
              <ul>
                <li>密码长度至少6位，建议包含字母、数字和特殊字符</li>
                <li>不要使用过于简单的密码，如123456、password等</li>
                <li>修改密码后需要重新登录</li>
              </ul>
            </template>
          </el-alert>
        </div>
      </div>
    </div>

    <el-card class="password-card">
      <div class="user-info">
        <el-avatar :size="60" :src="user.yonghuPhoto || user.touxiang" icon="el-icon-user-solid"></el-avatar>
        <div class="user-details">
          <h3>{{ user.yonghuName || user.username || '用户' }}</h3>
          <p>{{ user.yonghuPhone || user.phone || '暂无手机号' }}</p>
        </div>
      </div>

      <el-form
        class="password-form"
        ref="ruleForm"
        :rules="rules"
        :model="ruleForm"
        label-width="120px">

        <el-form-item label="当前密码" prop="password">
          <el-input
            v-model="ruleForm.password"
            type="password"
            show-password
            placeholder="请输入当前密码"
            prefix-icon="el-icon-lock"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="新密码" prop="newpassword">
          <el-input
            v-model="ruleForm.newpassword"
            type="password"
            show-password
            placeholder="请输入新密码"
            prefix-icon="el-icon-key"
            clearable
            @input="checkPasswordStrength">
          </el-input>
          <div class="password-strength" v-if="ruleForm.newpassword">
            <div class="strength-label">密码强度：</div>
            <div class="strength-bar">
              <div
                class="strength-fill"
                :class="passwordStrength.class"
                :style="{width: passwordStrength.width}">
              </div>
            </div>
            <span class="strength-text" :class="passwordStrength.class">
              {{ passwordStrength.text }}
            </span>
          </div>
        </el-form-item>

        <el-form-item label="确认新密码" prop="repassword">
          <el-input
            v-model="ruleForm.repassword"
            type="password"
            show-password
            placeholder="请再次输入新密码"
            prefix-icon="el-icon-check"
            clearable>
          </el-input>
        </el-form-item>

        <div class="form-actions">
          <el-button
            type="primary"
            @click="onUpdateHandler"
            :loading="loading"
            size="large">
            <i class="el-icon-check"></i>
            确认修改
          </el-button>
          <el-button
            @click="resetForm"
            size="large">
            <i class="el-icon-refresh"></i>
            重置
          </el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>
<script>
export default {
  data() {
    // 自定义验证规则
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入当前密码'));
      } else {
        callback();
      }
    };

    const validateNewPassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入新密码'));
      } else if (value.length < 6) {
        callback(new Error('密码长度至少6位'));
      } else if (value === this.ruleForm.password) {
        callback(new Error('新密码不能与当前密码相同'));
      } else {
        // 触发确认密码的重新验证
        if (this.ruleForm.repassword !== '') {
          this.$refs.ruleForm.validateField('repassword');
        }
        callback();
      }
    };

    const validateRePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请确认新密码'));
      } else if (value !== this.ruleForm.newpassword) {
        callback(new Error('两次输入的密码不一致'));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      ruleForm: {
        password: '',
        newpassword: '',
        repassword: ''
      },
      user: {},
      passwordStrength: {
        width: '0%',
        class: '',
        text: ''
      },
      rules: {
        password: [{ validator: validatePassword, trigger: 'blur' }],
        newpassword: [{ validator: validateNewPassword, trigger: 'blur' }],
        repassword: [{ validator: validateRePassword, trigger: 'blur' }]
      }
    };
  },
  mounted() {
    this.getUserInfo();
  },
  methods: {
    // 获取用户信息
    getUserInfo() {
      this.$http({
        url: `${this.$storage.get("sessionTable")}/session`,
        method: "get"
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.user = data.data;
        } else {
          this.$message.error(data.msg);
        }
      }).catch(() => {
        this.$message.error('获取用户信息失败');
      });
    },

    // 检查密码强度
    checkPasswordStrength() {
      const password = this.ruleForm.newpassword;
      if (!password) {
        this.passwordStrength = { width: '0%', class: '', text: '' };
        return;
      }

      let score = 0;
      let feedback = [];

      // 长度检查
      if (password.length >= 8) score += 25;
      else if (password.length >= 6) score += 15;
      else feedback.push('至少6位');

      // 包含小写字母
      if (/[a-z]/.test(password)) score += 25;
      else feedback.push('包含小写字母');

      // 包含大写字母
      if (/[A-Z]/.test(password)) score += 25;
      else feedback.push('包含大写字母');

      // 包含数字
      if (/\d/.test(password)) score += 15;
      else feedback.push('包含数字');

      // 包含特殊字符
      if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score += 10;
      else feedback.push('包含特殊字符');

      // 设置强度显示
      if (score < 30) {
        this.passwordStrength = {
          width: '25%',
          class: 'weak',
          text: '弱'
        };
      } else if (score < 60) {
        this.passwordStrength = {
          width: '50%',
          class: 'medium',
          text: '中等'
        };
      } else if (score < 80) {
        this.passwordStrength = {
          width: '75%',
          class: 'strong',
          text: '强'
        };
      } else {
        this.passwordStrength = {
          width: '100%',
          class: 'very-strong',
          text: '很强'
        };
      }
    },

    // 重置表单
    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.ruleForm = {
        password: '',
        newpassword: '',
        repassword: ''
      };
      this.passwordStrength = { width: '0%', class: '', text: '' };
    },

    // 修改密码
    onUpdateHandler() {
      this.$refs["ruleForm"].validate(valid => {
        if (valid) {
          // 验证原密码
          const currentPassword = this.user.mima || this.user.password || '';
          if (this.ruleForm.password !== currentPassword) {
            this.$message.error("当前密码错误");
            return;
          }

          // 确认对话框
          this.$confirm('确定要修改密码吗？修改后需要重新登录。', '确认修改', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            this.updatePassword();
          }).catch(() => {
            this.$message.info('已取消修改');
          });
        }
      });
    },

    // 执行密码更新
    updatePassword() {
      this.loading = true;

      // 更新用户密码
      const updateData = { ...this.user };
      updateData.password = this.ruleForm.newpassword;
      updateData.mima = this.ruleForm.newpassword;

      this.$http({
        url: `${this.$storage.get("sessionTable")}/update`,
        method: "post",
        data: updateData
      }).then(({ data }) => {
        this.loading = false;
        if (data && data.code === 0) {
          this.$message({
            message: "密码修改成功！请重新登录",
            type: "success",
            duration: 2000,
            onClose: () => {
              // 清除登录信息并跳转到登录页
              this.$storage.remove("Token");
              this.$storage.remove("role");
              this.$storage.remove("sessionTable");
              this.$storage.remove("adminName");
              this.$router.replace({ name: "login" });
            }
          });
        } else {
          this.$message.error(data.msg || '修改密码失败');
        }
      }).catch(() => {
        this.loading = false;
        this.$message.error('网络错误，请稍后重试');
      });
    }
  }
};
</script>
<style lang="scss" scoped>
.update-password-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);

  .password-header {
    margin-bottom: 30px;

    .header-content {
      .title-section {
        text-align: center;
        margin-bottom: 20px;

        h2 {
          font-size: 28px;
          color: #2c3e50;
          margin: 0 0 10px 0;

          i {
            color: #00c292;
            margin-right: 10px;
          }
        }

        p {
          color: #909399;
          margin: 0;
          font-size: 16px;
        }
      }

      .security-tips {
        max-width: 600px;
        margin: 0 auto;

        ::v-deep .el-alert {
          border-radius: 8px;

          .el-alert__description {
            ul {
              margin: 0;
              padding-left: 20px;

              li {
                margin: 5px 0;
                color: #606266;
              }
            }
          }
        }
      }
    }
  }

  .password-card {
    max-width: 600px;
    margin: 0 auto;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

    ::v-deep .el-card__body {
      padding: 40px;
    }

    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: 40px;
      padding: 20px;
      background: linear-gradient(135deg, #00c292 0%, #00a085 100%);
      border-radius: 8px;
      color: white;

      .user-details {
        margin-left: 20px;

        h3 {
          margin: 0 0 5px 0;
          font-size: 20px;
          font-weight: 600;
        }

        p {
          margin: 0;
          opacity: 0.9;
          font-size: 14px;
        }
      }
    }

    .password-form {
      .el-form-item {
        margin-bottom: 30px;

        ::v-deep .el-form-item__label {
          font-weight: 600;
          color: #2c3e50;
          font-size: 16px;
        }

        ::v-deep .el-input__inner {
          height: 50px;
          line-height: 50px;
          border-radius: 8px;
          border: 2px solid #dcdfe6;
          font-size: 16px;
          transition: all 0.3s ease;

          &:focus {
            border-color: #00c292;
            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);
          }
        }

        ::v-deep .el-input__prefix {
          left: 15px;

          .el-input__icon {
            line-height: 50px;
            color: #909399;
          }
        }
      }

      .password-strength {
        margin-top: 10px;
        display: flex;
        align-items: center;
        gap: 10px;

        .strength-label {
          font-size: 14px;
          color: #606266;
          white-space: nowrap;
        }

        .strength-bar {
          flex: 1;
          height: 6px;
          background: #f0f0f0;
          border-radius: 3px;
          overflow: hidden;

          .strength-fill {
            height: 100%;
            transition: all 0.3s ease;
            border-radius: 3px;

            &.weak {
              background: #f56c6c;
            }

            &.medium {
              background: #e6a23c;
            }

            &.strong {
              background: #67c23a;
            }

            &.very-strong {
              background: #00c292;
            }
          }
        }

        .strength-text {
          font-size: 12px;
          font-weight: 600;
          white-space: nowrap;

          &.weak {
            color: #f56c6c;
          }

          &.medium {
            color: #e6a23c;
          }

          &.strong {
            color: #67c23a;
          }

          &.very-strong {
            color: #00c292;
          }
        }
      }

      .form-actions {
        text-align: center;
        margin-top: 40px;
        padding-top: 30px;
        border-top: 1px solid #ebeef5;

        .el-button {
          border-radius: 8px;
          padding: 15px 40px;
          font-weight: 600;
          font-size: 16px;
          margin: 0 10px;

          &.el-button--primary {
            background: linear-gradient(135deg, #00c292 0%, #00a085 100%);
            border: none;

            &:hover {
              background: linear-gradient(135deg, #00a085 0%, #008f75 100%);
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .update-password-container {
    padding: 10px;

    .password-card {
      ::v-deep .el-card__body {
        padding: 20px;
      }

      .user-info {
        flex-direction: column;
        text-align: center;

        .user-details {
          margin-left: 0;
          margin-top: 15px;
        }
      }

      .password-form {
        .form-actions {
          .el-button {
            display: block;
            width: 100%;
            margin: 10px 0;
          }
        }
      }
    }
  }
}
</style>

