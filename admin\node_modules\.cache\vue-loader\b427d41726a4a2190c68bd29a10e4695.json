{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdi\\add-or-update.vue?vue&type=style&index=0&id=4c93c9e0&lang=scss&scoped=true", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\views\\modules\\changdi\\add-or-update.vue", "mtime": 1750590566807}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["add-or-update.vue"], "names": [], "mappings": ";AA8sBA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA", "file": "add-or-update.vue", "sourceRoot": "src/views/modules/changdi", "sourcesContent": ["\r\n\r\n\r\n\r\n\r\n\r\n\r\n\r\n<template>\r\n    <div class=\"changdi-form-container\">\r\n        <div class=\"form-header\">\r\n            <h3 v-if=\"type === 'info'\">场地详情</h3>\r\n            <h3 v-else-if=\"!ruleForm.id\">新增场地</h3>\r\n            <h3 v-else>编辑场地</h3>\r\n        </div>\r\n\r\n        <el-card class=\"form-card\">\r\n            <el-form\r\n                class=\"changdi-form\"\r\n                ref=\"ruleForm\"\r\n                :model=\"ruleForm\"\r\n                :rules=\"rules\"\r\n                label-width=\"120px\">\r\n\r\n                <el-row :gutter=\"20\">\r\n                    <input id=\"updateId\" name=\"id\" type=\"hidden\">\r\n\r\n                    <!-- 基本信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">基本信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地编号\" prop=\"changdiUuidNumber\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiUuidNumber\"\r\n                                placeholder=\"请输入场地编号\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiUuidNumber || type === 'info'\"\r\n                                prefix-icon=\"el-icon-postcard\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地名称\" prop=\"changdiName\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiName\"\r\n                                placeholder=\"请输入场地名称\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiName || type === 'info'\"\r\n                                prefix-icon=\"el-icon-office-building\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地类型\" prop=\"changdiTypes\">\r\n                            <el-select\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.changdiTypes\"\r\n                                placeholder=\"请选择场地类型\"\r\n                                style=\"width: 100%\">\r\n                                <el-option\r\n                                    v-for=\"(item,index) in changdiTypesOptions\"\r\n                                    v-bind:key=\"item.codeIndex\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                </el-option>\r\n                            </el-select>\r\n                            <el-input\r\n                                v-else\r\n                                v-model=\"ruleForm.changdiValue\"\r\n                                placeholder=\"场地类型\"\r\n                                readonly\r\n                                prefix-icon=\"el-icon-menu\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"半全场\" prop=\"banquanTypes\">\r\n                            <el-select\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.banquanTypes\"\r\n                                placeholder=\"请选择半全场\"\r\n                                style=\"width: 100%\">\r\n                                <el-option\r\n                                    v-for=\"(item,index) in banquanTypesOptions\"\r\n                                    v-bind:key=\"item.codeIndex\"\r\n                                    :label=\"item.indexName\"\r\n                                    :value=\"item.codeIndex\">\r\n                                </el-option>\r\n                            </el-select>\r\n                            <el-input\r\n                                v-else\r\n                                v-model=\"ruleForm.banquanValue\"\r\n                                placeholder=\"半全场\"\r\n                                readonly\r\n                                prefix-icon=\"el-icon-s-grid\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <!-- 场地照片 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">场地图片</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"24\">\r\n                        <el-form-item label=\"场地照片\" prop=\"changdiPhoto\">\r\n                            <file-upload\r\n                                v-if=\"type !== 'info' && !ro.changdiPhoto\"\r\n                                tip=\"点击上传场地照片，支持多张图片\"\r\n                                action=\"file/upload\"\r\n                                :limit=\"5\"\r\n                                :multiple=\"true\"\r\n                                :fileUrls=\"ruleForm.changdiPhoto?ruleForm.changdiPhoto:''\"\r\n                                @change=\"changdiPhotoUploadChange\"\r\n                            ></file-upload>\r\n                            <div v-else-if=\"ruleForm.changdiPhoto\" class=\"photo-preview\">\r\n                                <img\r\n                                    v-for=\"(item,index) in (ruleForm.changdiPhoto || '').split(',')\"\r\n                                    :key=\"index\"\r\n                                    :src=\"item\"\r\n                                    class=\"preview-image\"\r\n                                    @click=\"previewImage(item)\">\r\n                            </div>\r\n                            <div v-else class=\"no-image\">暂无图片</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <!-- 价格信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">价格信息</div>\r\n                    </el-col>\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地原价\" prop=\"changdiOldMoney\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiOldMoney\"\r\n                                placeholder=\"请输入场地原价\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiOldMoney || type === 'info'\"\r\n                                prefix-icon=\"el-icon-price-tag\">\r\n                                <template slot=\"append\">元/小时</template>\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"场地现价\" prop=\"changdiNewMoney\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.changdiNewMoney\"\r\n                                placeholder=\"请输入场地现价\"\r\n                                clearable\r\n                                :readonly=\"ro.changdiNewMoney || type === 'info'\"\r\n                                prefix-icon=\"el-icon-sell\">\r\n                                <template slot=\"append\">元/小时</template>\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <!-- 时间信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">时间信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"可用时间段\" prop=\"shijianduan\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.shijianduan\"\r\n                                placeholder=\"例如：8-10,10-12,14-16,16-18\"\r\n                                clearable\r\n                                :readonly=\"ro.shijianduan || type === 'info'\"\r\n                                prefix-icon=\"el-icon-time\">\r\n                            </el-input>\r\n                            <div class=\"form-tip\">请用逗号分隔多个时间段，格式：开始时间-结束时间</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"12\">\r\n                        <el-form-item label=\"推荐餐厅\" prop=\"tuijian\">\r\n                            <el-input\r\n                                v-model=\"ruleForm.tuijian\"\r\n                                placeholder=\"请输入推荐的餐厅地点\"\r\n                                clearable\r\n                                :readonly=\"ro.tuijian || type === 'info'\"\r\n                                prefix-icon=\"el-icon-food\">\r\n                            </el-input>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                    <!-- 详细信息 -->\r\n                    <el-col :span=\"24\">\r\n                        <div class=\"section-title\">详细信息</div>\r\n                    </el-col>\r\n\r\n                    <el-col :span=\"24\">\r\n                        <el-form-item label=\"场地简介\" prop=\"changdiContent\">\r\n                            <editor\r\n                                v-if=\"type !== 'info'\"\r\n                                v-model=\"ruleForm.changdiContent\"\r\n                                class=\"editor\"\r\n                                action=\"file/upload\"\r\n                                placeholder=\"请输入场地的详细介绍...\">\r\n                            </editor>\r\n                            <div v-else-if=\"ruleForm.changdiContent\" class=\"content-preview\">\r\n                                <div v-html=\"ruleForm.changdiContent\"></div>\r\n                            </div>\r\n                            <div v-else class=\"no-content\">暂无简介</div>\r\n                        </el-form-item>\r\n                    </el-col>\r\n                </el-row>\r\n\r\n                <!-- 操作按钮 -->\r\n                <div class=\"form-actions\">\r\n                    <el-button\r\n                        v-if=\"type !== 'info'\"\r\n                        type=\"primary\"\r\n                        @click=\"onSubmit\"\r\n                        :loading=\"loading\">\r\n                        <i class=\"el-icon-check\"></i>\r\n                        {{ !ruleForm.id ? '新增场地' : '保存修改' }}\r\n                    </el-button>\r\n                    <el-button @click=\"back()\">\r\n                        <i class=\"el-icon-back\"></i>\r\n                        {{ type === 'info' ? '返回' : '取消' }}\r\n                    </el-button>\r\n                </div>\r\n            </el-form>\r\n        </el-card>\r\n    </div>\r\n</template>\r\n<script>\r\n    import styleJs from \"../../../utils/style.js\";\r\n    // 数字，邮件，手机，url，身份证校验\r\n    import { isNumber,isIntNumer,isEmail,isPhone, isMobile,isURL,checkIdCard } from \"@/utils/validate\";\r\n    export default {\r\n        data() {\r\n            return {\r\n                addEditForm:null,\r\n                id: '',\r\n                type: '',\r\n                sessionTable : \"\",//登录账户所在表名\r\n                role : \"\",//权限\r\n                loading: false,\r\n                ro:{\r\n                    changdiUuidNumber: false,\r\n                    changdiName: false,\r\n                    changdiPhoto: false,\r\n                    changdiTypes: false,\r\n                    changdiOldMoney: false,\r\n                    changdiNewMoney: false,\r\n                    shijianduan: false,\r\n                    shijianduanRen: false,\r\n                    changdiClicknum: false,\r\n                    banquanTypes: false,\r\n                    shangxiaTypes: false,\r\n                    tuijian: false,\r\n                    changdiDelete: false,\r\n                    changdiContent: false,\r\n                },\r\n                ruleForm: {\r\n                    changdiUuidNumber: new Date().getTime(),\r\n                    changdiName: '',\r\n                    changdiPhoto: '',\r\n                    changdiTypes: '',\r\n                    changdiOldMoney: '',\r\n                    changdiNewMoney: '',\r\n                    shijianduan: '8-10,10-12,14-16,16-18',\r\n                    shijianduanRen: '',\r\n                    changdiClicknum: '',\r\n                    banquanTypes: '',\r\n                    shangxiaTypes: '',\r\n                    tuijian: '',\r\n                    changdiDelete: '',\r\n                    changdiContent: '',\r\n                },\r\n                changdiTypesOptions : [],\r\n                banquanTypesOptions : [],\r\n                shangxiaTypesOptions : [],\r\n                rules: {\r\n                   changdiUuidNumber: [\r\n                              { required: true, message: '场地编号不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiName: [\r\n                              { required: true, message: '场地名称不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiPhoto: [\r\n                              { required: true, message: '场地照片不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiTypes: [\r\n                              { required: true, message: '场地类型不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiOldMoney: [\r\n                              { required: true, message: '场地原价不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[0-9]{0,6}(\\.[0-9]{1,2})?$/,\r\n                                  message: '只允许输入整数6位,小数2位的数字',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiNewMoney: [\r\n                              { required: true, message: '场地现价不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[0-9]{0,6}(\\.[0-9]{1,2})?$/,\r\n                                  message: '只允许输入整数6位,小数2位的数字',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   shijianduan: [\r\n                              { required: true, message: '时间段不能为空', trigger: 'blur' },\r\n                          ],\r\n                   shijianduanRen: [\r\n                              { required: true, message: '人数不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiClicknum: [\r\n                              { required: true, message: '点击次数不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   banquanTypes: [\r\n                              { required: true, message: '半全场不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   shangxiaTypes: [\r\n                              { required: true, message: '是否上架不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   tuijian: [\r\n                              { required: true, message: '推荐吃饭地点不能为空', trigger: 'blur' },\r\n                          ],\r\n                   changdiDelete: [\r\n                              { required: true, message: '逻辑删除不能为空', trigger: 'blur' },\r\n                              {  pattern: /^[1-9][0-9]*$/,\r\n                                  message: '只允许输入整数',\r\n                                  trigger: 'blur'\r\n                              }\r\n                          ],\r\n                   changdiContent: [\r\n                              { required: true, message: '场地简介不能为空', trigger: 'blur' },\r\n                          ],\r\n                }\r\n            };\r\n        },\r\n        props: [\"parent\"],\r\n        computed: {\r\n        },\r\n        created() {\r\n            //获取当前登录用户的信息\r\n            this.sessionTable = this.$storage.get(\"sessionTable\");\r\n            this.role = this.$storage.get(\"role\");\r\n\r\n            if (this.role != \"管理员\"){\r\n                this.ro.changdiOldMoney = true;\r\n                this.ro.changdiNewMoney = true;\r\n            }\r\n            this.addEditForm = styleJs.addStyle();\r\n            this.addEditStyleChange()\r\n            this.addEditUploadStyleChange()\r\n            //获取下拉框信息\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=changdi_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.changdiTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=banquan_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.banquanTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n                this.$http({\r\n                    url:`dictionary/page?page=1&limit=100&sort=&order=&dicCode=shangxia_types`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.shangxiaTypesOptions = data.data.list;\r\n                    }\r\n                });\r\n\r\n\r\n        },\r\n        mounted() {\r\n        },\r\n        methods: {\r\n            // 下载\r\n            download(file){\r\n                window.open(`${file}`)\r\n            },\r\n            // 初始化\r\n            init(id,type) {\r\n                if (id) {\r\n                    this.id = id;\r\n                    this.type = type;\r\n                }\r\n                if(this.type=='info'||this.type=='else'){\r\n                    this.info(id);\r\n                }else if(this.type=='cross'){\r\n                    var obj = this.$storage.getObj('crossObj');\r\n                    for (var o in obj){\r\n\r\n                      if(o=='changdiUuidNumber'){\r\n                          this.ruleForm.changdiUuidNumber = obj[o];\r\n                          this.ro.changdiUuidNumber = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiName'){\r\n                          this.ruleForm.changdiName = obj[o];\r\n                          this.ro.changdiName = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiPhoto'){\r\n                          this.ruleForm.changdiPhoto = obj[o];\r\n                          this.ro.changdiPhoto = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiTypes'){\r\n                          this.ruleForm.changdiTypes = obj[o];\r\n                          this.ro.changdiTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiOldMoney'){\r\n                          this.ruleForm.changdiOldMoney = obj[o];\r\n                          this.ro.changdiOldMoney = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiNewMoney'){\r\n                          this.ruleForm.changdiNewMoney = obj[o];\r\n                          this.ro.changdiNewMoney = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shijianduan'){\r\n                          this.ruleForm.shijianduan = obj[o];\r\n                          this.ro.shijianduan = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shijianduanRen'){\r\n                          this.ruleForm.shijianduanRen = obj[o];\r\n                          this.ro.shijianduanRen = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiClicknum'){\r\n                          this.ruleForm.changdiClicknum = obj[o];\r\n                          this.ro.changdiClicknum = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='banquanTypes'){\r\n                          this.ruleForm.banquanTypes = obj[o];\r\n                          this.ro.banquanTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='shangxiaTypes'){\r\n                          this.ruleForm.shangxiaTypes = obj[o];\r\n                          this.ro.shangxiaTypes = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='tuijian'){\r\n                          this.ruleForm.tuijian = obj[o];\r\n                          this.ro.tuijian = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiDelete'){\r\n                          this.ruleForm.changdiDelete = obj[o];\r\n                          this.ro.changdiDelete = true;\r\n                          continue;\r\n                      }\r\n                      if(o=='changdiContent'){\r\n                          this.ruleForm.changdiContent = obj[o];\r\n                          this.ro.changdiContent = true;\r\n                          continue;\r\n                      }\r\n                    }\r\n                }\r\n                // 获取用户信息\r\n                this.$http({\r\n                    url:`${this.$storage.get(\"sessionTable\")}/session`,\r\n                    method: \"get\"\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        var json = data.data;\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 多级联动参数\r\n            info(id) {\r\n                this.$http({\r\n                    url: `changdi/info/${id}`,\r\n                    method: 'get'\r\n                }).then(({ data }) => {\r\n                    if (data && data.code === 0) {\r\n                        this.ruleForm = data.data;\r\n                        //解决前台上传图片后台不显示的问题\r\n                        let reg=new RegExp('../../../upload','g')//g代表全部\r\n                    } else {\r\n                        this.$message.error(data.msg);\r\n                    }\r\n                });\r\n            },\r\n            // 图片预览\r\n            previewImage(url) {\r\n                this.$alert(`<img src=\"${url}\" style=\"width: 100%; max-width: 500px;\">`, '图片预览', {\r\n                    dangerouslyUseHTMLString: true,\r\n                    showConfirmButton: false,\r\n                    showCancelButton: true,\r\n                    cancelButtonText: '关闭'\r\n                });\r\n            },\r\n\r\n            // 提交\r\n            onSubmit() {\r\n                this.$refs[\"ruleForm\"].validate(valid => {\r\n                    if (valid) {\r\n                        this.loading = true;\r\n                        this.$http({\r\n                            url:`changdi/${!this.ruleForm.id ? \"save\" : \"update\"}`,\r\n                            method: \"post\",\r\n                            data: this.ruleForm\r\n                        }).then(({ data }) => {\r\n                            this.loading = false;\r\n                            if (data && data.code === 0) {\r\n                                this.$message({\r\n                                    message: \"操作成功\",\r\n                                    type: \"success\",\r\n                                    duration: 1500,\r\n                                    onClose: () => {\r\n                                        this.parent.showFlag = true;\r\n                                        this.parent.addOrUpdateFlag = false;\r\n                                        this.parent.changdiCrossAddOrUpdateFlag = false;\r\n                                        this.parent.search();\r\n                                        this.parent.contentStyleChange();\r\n                                    }\r\n                                });\r\n                            } else {\r\n                                this.$message.error(data.msg);\r\n                            }\r\n                        }).catch(() => {\r\n                            this.loading = false;\r\n                        });\r\n                    }\r\n                });\r\n            },\r\n            // 获取uuid\r\n            getUUID () {\r\n                return new Date().getTime();\r\n            },\r\n            // 返回\r\n            back() {\r\n                this.parent.showFlag = true;\r\n                this.parent.addOrUpdateFlag = false;\r\n                this.parent.changdiCrossAddOrUpdateFlag = false;\r\n                this.parent.contentStyleChange();\r\n            },\r\n            //图片\r\n            changdiPhotoUploadChange(fileUrls){\r\n                this.ruleForm.changdiPhoto = fileUrls;\r\n                this.addEditUploadStyleChange()\r\n            },\r\n\r\n            addEditStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    // input\r\n                    document.querySelectorAll('.addEdit-block .input .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputFontColor\r\n                        el.style.fontSize = this.addEditForm.inputFontSize\r\n                        el.style.borderWidth = this.addEditForm.inputBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.inputBorderStyle\r\n                        el.style.borderColor = this.addEditForm.inputBorderColor\r\n                        el.style.borderRadius = this.addEditForm.inputBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.inputBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .input .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.inputHeight\r\n                        el.style.color = this.addEditForm.inputLableColor\r\n                        el.style.fontSize = this.addEditForm.inputLableFontSize\r\n                    })\r\n                    // select\r\n                    document.querySelectorAll('.addEdit-block .select .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectFontColor\r\n                        el.style.fontSize = this.addEditForm.selectFontSize\r\n                        el.style.borderWidth = this.addEditForm.selectBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.selectBorderStyle\r\n                        el.style.borderColor = this.addEditForm.selectBorderColor\r\n                        el.style.borderRadius = this.addEditForm.selectBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.selectBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.selectHeight\r\n                        el.style.color = this.addEditForm.selectLableColor\r\n                        el.style.fontSize = this.addEditForm.selectLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .select .el-select__caret').forEach(el=>{\r\n                        el.style.color = this.addEditForm.selectIconFontColor\r\n                        el.style.fontSize = this.addEditForm.selectIconFontSize\r\n                    })\r\n                    // date\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateFontColor\r\n                        el.style.fontSize = this.addEditForm.dateFontSize\r\n                        el.style.borderWidth = this.addEditForm.dateBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.dateBorderStyle\r\n                        el.style.borderColor = this.addEditForm.dateBorderColor\r\n                        el.style.borderRadius = this.addEditForm.dateBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.dateBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                        el.style.color = this.addEditForm.dateLableColor\r\n                        el.style.fontSize = this.addEditForm.dateLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .date .el-input__icon').forEach(el=>{\r\n                        el.style.color = this.addEditForm.dateIconFontColor\r\n                        el.style.fontSize = this.addEditForm.dateIconFontSize\r\n                        el.style.lineHeight = this.addEditForm.dateHeight\r\n                    })\r\n                    // upload\r\n                    let iconLineHeight = parseInt(this.addEditForm.uploadHeight) - parseInt(this.addEditForm.uploadBorderWidth) * 2 + 'px'\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload--picture-card').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-form-item__label').forEach(el=>{\r\n                        el.style.lineHeight = this.addEditForm.uploadHeight\r\n                        el.style.color = this.addEditForm.uploadLableColor\r\n                        el.style.fontSize = this.addEditForm.uploadLableFontSize\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .upload .el-icon-plus').forEach(el=>{\r\n                        el.style.color = this.addEditForm.uploadIconFontColor\r\n                        el.style.fontSize = this.addEditForm.uploadIconFontSize\r\n                        el.style.lineHeight = iconLineHeight\r\n                        el.style.display = 'block'\r\n                    })\r\n                    // 多文本输入框\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-textarea__inner').forEach(el=>{\r\n                        el.style.height = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaFontColor\r\n                        el.style.fontSize = this.addEditForm.textareaFontSize\r\n                        el.style.borderWidth = this.addEditForm.textareaBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.textareaBorderStyle\r\n                        el.style.borderColor = this.addEditForm.textareaBorderColor\r\n                        el.style.borderRadius = this.addEditForm.textareaBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.textareaBgColor\r\n                    })\r\n                    document.querySelectorAll('.addEdit-block .textarea .el-form-item__label').forEach(el=>{\r\n                        // el.style.lineHeight = this.addEditForm.textareaHeight\r\n                        el.style.color = this.addEditForm.textareaLableColor\r\n                        el.style.fontSize = this.addEditForm.textareaLableFontSize\r\n                    })\r\n                    // 保存\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-success').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnSaveWidth\r\n                        el.style.height = this.addEditForm.btnSaveHeight\r\n                        el.style.color = this.addEditForm.btnSaveFontColor\r\n                        el.style.fontSize = this.addEditForm.btnSaveFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnSaveBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnSaveBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnSaveBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnSaveBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnSaveBgColor\r\n                    })\r\n                    // 返回\r\n                    document.querySelectorAll('.addEdit-block .btn .btn-close').forEach(el=>{\r\n                        el.style.width = this.addEditForm.btnCancelWidth\r\n                        el.style.height = this.addEditForm.btnCancelHeight\r\n                        el.style.color = this.addEditForm.btnCancelFontColor\r\n                        el.style.fontSize = this.addEditForm.btnCancelFontSize\r\n                        el.style.borderWidth = this.addEditForm.btnCancelBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.btnCancelBorderStyle\r\n                        el.style.borderColor = this.addEditForm.btnCancelBorderColor\r\n                        el.style.borderRadius = this.addEditForm.btnCancelBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.btnCancelBgColor\r\n                    })\r\n                })\r\n            },\r\n            addEditUploadStyleChange() {\r\n                this.$nextTick(()=>{\r\n                    document.querySelectorAll('.addEdit-block .upload .el-upload-list--picture-card .el-upload-list__item').forEach(el=>{\r\n                        el.style.width = this.addEditForm.uploadHeight\r\n                        el.style.height = this.addEditForm.uploadHeight\r\n                        el.style.borderWidth = this.addEditForm.uploadBorderWidth\r\n                        el.style.borderStyle = this.addEditForm.uploadBorderStyle\r\n                        el.style.borderColor = this.addEditForm.uploadBorderColor\r\n                        el.style.borderRadius = this.addEditForm.uploadBorderRadius\r\n                        el.style.backgroundColor = this.addEditForm.uploadBgColor\r\n                    })\r\n                })\r\n            },\r\n        }\r\n    };\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.changdi-form-container {\r\n  padding: 20px;\r\n\r\n  .form-header {\r\n    text-align: center;\r\n    margin-bottom: 30px;\r\n\r\n    h3 {\r\n      font-size: 24px;\r\n      color: #2c3e50;\r\n      margin: 0;\r\n    }\r\n  }\r\n\r\n  .form-card {\r\n    ::v-deep .el-card__body {\r\n      padding: 30px;\r\n    }\r\n\r\n    .changdi-form {\r\n      .section-title {\r\n        font-size: 16px;\r\n        font-weight: bold;\r\n        color: #2c3e50;\r\n        margin-bottom: 20px;\r\n        padding-bottom: 10px;\r\n        border-bottom: 2px solid #00c292;\r\n        position: relative;\r\n\r\n        &::after {\r\n          content: '';\r\n          position: absolute;\r\n          bottom: -2px;\r\n          left: 0;\r\n          width: 50px;\r\n          height: 2px;\r\n          background: #00c292;\r\n        }\r\n      }\r\n\r\n      .el-form-item {\r\n        margin-bottom: 25px;\r\n\r\n        ::v-deep .el-form-item__label {\r\n          font-weight: 600;\r\n          color: #2c3e50;\r\n        }\r\n\r\n        ::v-deep .el-input__inner {\r\n          border-radius: 6px;\r\n          border: 1px solid #dcdfe6;\r\n          transition: all 0.3s ease;\r\n\r\n          &:focus {\r\n            border-color: #00c292;\r\n            box-shadow: 0 0 0 2px rgba(0, 194, 146, 0.2);\r\n          }\r\n        }\r\n\r\n        ::v-deep .el-select {\r\n          width: 100%;\r\n        }\r\n\r\n        .form-tip {\r\n          font-size: 12px;\r\n          color: #909399;\r\n          margin-top: 5px;\r\n        }\r\n      }\r\n\r\n      .photo-preview {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n        gap: 10px;\r\n\r\n        .preview-image {\r\n          width: 100px;\r\n          height: 100px;\r\n          object-fit: cover;\r\n          border-radius: 8px;\r\n          cursor: pointer;\r\n          border: 2px solid #dcdfe6;\r\n          transition: all 0.3s ease;\r\n\r\n          &:hover {\r\n            border-color: #00c292;\r\n            transform: scale(1.05);\r\n          }\r\n        }\r\n      }\r\n\r\n      .no-image, .no-content {\r\n        color: #909399;\r\n        font-style: italic;\r\n        text-align: center;\r\n        padding: 20px;\r\n        background: #f8f9fa;\r\n        border-radius: 6px;\r\n        border: 1px dashed #dcdfe6;\r\n      }\r\n\r\n      .content-preview {\r\n        padding: 15px;\r\n        background: #f8f9fa;\r\n        border-radius: 6px;\r\n        border: 1px solid #dcdfe6;\r\n        min-height: 100px;\r\n\r\n        ::v-deep img {\r\n          max-width: 100%;\r\n          height: auto;\r\n        }\r\n      }\r\n    }\r\n\r\n    .form-actions {\r\n      text-align: center;\r\n      margin-top: 30px;\r\n      padding-top: 20px;\r\n      border-top: 1px solid #ebeef5;\r\n\r\n      .el-button {\r\n        border-radius: 6px;\r\n        padding: 12px 30px;\r\n        font-weight: 600;\r\n        margin: 0 10px;\r\n\r\n        &.el-button--primary {\r\n          background: linear-gradient(135deg, #00c292 0%, #00a085 100%);\r\n          border: none;\r\n\r\n          &:hover {\r\n            background: linear-gradient(135deg, #00a085 0%, #008f75 100%);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.editor {\r\n  height: 400px;\r\n  border-radius: 6px;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n\r\n  ::v-deep .ql-container {\r\n    height: 310px;\r\n  }\r\n\r\n  ::v-deep .ql-toolbar {\r\n    border-bottom: 1px solid #dcdfe6;\r\n  }\r\n}\r\n\r\n::v-deep .el-upload {\r\n  .el-upload-dragger {\r\n    border-radius: 8px;\r\n    border: 2px dashed #d9d9d9;\r\n    transition: all 0.3s ease;\r\n\r\n    &:hover {\r\n      border-color: #00c292;\r\n    }\r\n  }\r\n}\r\n</style>\r\n\r\n"]}]}