{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeChart.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeChart.vue", "mtime": 1750589717478}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:ZXhwb3J0IGRlZmF1bHQgewogIG1vdW50ZWQ6IGZ1bmN0aW9uIG1vdW50ZWQoKSB7CiAgICB0aGlzLmhvbWVDaGFydCgpOwogIH0sCiAgbWV0aG9kczogewogICAgaG9tZUNoYXJ0OiBmdW5jdGlvbiBob21lQ2hhcnQoKSB7CiAgICAgIC8vIOWfuuS6juWHhuWkh+WlveeahGRvbe+8jOWIneWni+WMlmVjaGFydHPlrp7kvosKICAgICAgdmFyIG15Q2hhcnQgPSB0aGlzLiRlY2hhcnRzLmluaXQoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoImhvbWUtY2hhcnQiKSk7CiAgICAgIC8vIOaMh+WumuWbvuihqOeahOmFjee9rumhueWSjOaVsOaNrgogICAgICB2YXIgb3B0aW9uID0gewogICAgICAgIHRvb2x0aXA6IHsKICAgICAgICAgIHRyaWdnZXI6ICJheGlzIgogICAgICAgIH0sCiAgICAgICAgbGVnZW5kOiB7CiAgICAgICAgICBkYXRhOiBbIuiuv+mXrumHjyIsICLnlKjmiLfph48iLCAi5pS25YWlIl0KICAgICAgICB9LAogICAgICAgIGdyaWQ6IHsKICAgICAgICAgIGxlZnQ6ICIzJSIsCiAgICAgICAgICByaWdodDogIjQlIiwKICAgICAgICAgIGJvdHRvbTogIjMlIiwKICAgICAgICAgIGNvbnRhaW5MYWJlbDogdHJ1ZQogICAgICAgIH0sCiAgICAgICAgeEF4aXM6IHsKICAgICAgICAgIHR5cGU6ICJjYXRlZ29yeSIsCiAgICAgICAgICBib3VuZGFyeUdhcDogZmFsc2UsCiAgICAgICAgICBkYXRhOiBbIjHmnIgiLCAiMuaciCIsICIz5pyIIiwgIjTmnIgiLCAiNeaciCIsICI25pyIIiwgIjfmnIgiLCAiOOaciCIsICI55pyIIiwgIjEw5pyIIiwgIjEx5pyIIiwgIjEy5pyIIl0KICAgICAgICB9LAogICAgICAgIHlBeGlzOiB7CiAgICAgICAgICB0eXBlOiAidmFsdWUiCiAgICAgICAgfSwKICAgICAgICBzZXJpZXM6IFt7CiAgICAgICAgICBuYW1lOiAi6K6/6Zeu6YePIiwKICAgICAgICAgIHR5cGU6ICJsaW5lIiwKICAgICAgICAgIHN0YWNrOiAi5oC76YePIiwKICAgICAgICAgIGRhdGE6IFsxMjAsIDEzMiwgMTAxLCAxMzQsIDkwLCAyMzAsIDIxMCwgMTIwLCAxMzIsIDEwMSwgMTM0LCA5MCwgMjMwXQogICAgICAgIH0sIHsKICAgICAgICAgIG5hbWU6ICLnlKjmiLfph48iLAogICAgICAgICAgdHlwZTogImxpbmUiLAogICAgICAgICAgc3RhY2s6ICLmgLvph48iLAogICAgICAgICAgZGF0YTogWzIyMCwgMTgyLCAxOTEsIDIzNCwgMjkwLCAzMzAsIDMxMCwgMTgyLCAxOTEsIDIzNCwgMjkwLCAzMzAsIDMxMF0KICAgICAgICB9LCB7CiAgICAgICAgICBuYW1lOiAi5pS25YWlIiwKICAgICAgICAgIHR5cGU6ICJsaW5lIiwKICAgICAgICAgIHN0YWNrOiAi5oC76YePIiwKICAgICAgICAgIGRhdGE6IFsxNTAsIDIzMiwgMjAxLCAxNTQsIDE5MCwgMzMwLCA0MTAsIDIzMiwgMjAxLCAxNTQsIDE5MCwgMzMwLCA0MTBdCiAgICAgICAgfV0KICAgICAgfTsKICAgICAgLy8gLy8g5L2/55So5Yia5oyH5a6a55qE6YWN572u6aG55ZKM5pWw5o2u5pi+56S65Zu+6KGoCiAgICAgIG15Q2hhcnQuc2V0T3B0aW9uKG9wdGlvbik7CiAgICAgIC8v5qC55o2u56qX5Y+j55qE5aSn5bCP5Y+Y5Yqo5Zu+6KGoCiAgICAgIHdpbmRvdy5vbnJlc2l6ZSA9IGZ1bmN0aW9uICgpIHsKICAgICAgICBteUNoYXJ0LnJlc2l6ZSgpOwogICAgICB9OwogICAgfQogIH0KfTs="}, {"version": 3, "names": ["mounted", "homeChart", "methods", "myChart", "$echarts", "init", "document", "getElementById", "option", "tooltip", "trigger", "legend", "data", "grid", "left", "right", "bottom", "containLabel", "xAxis", "type", "boundaryGap", "yAxis", "series", "name", "stack", "setOption", "window", "onresize", "resize"], "sources": ["src/components/home/<USER>"], "sourcesContent": ["<template>\r\n  <div id=\"home-chart\" style=\"width:100%;height:400px;\"></div>\r\n</template>\r\n<script>\r\nexport default {\r\n  mounted() {\r\n    this.homeChart();\r\n  },\r\n  methods: {\r\n    homeChart() {\r\n      // 基于准备好的dom，初始化echarts实例\r\n      var myChart = this.$echarts.init(document.getElementById(\"home-chart\"));\r\n      // 指定图表的配置项和数据\r\n      var option = {\r\n        tooltip: {\r\n          trigger: \"axis\"\r\n        },\r\n        legend: {\r\n          data: [\"访问量\", \"用户量\", \"收入\"]\r\n        },\r\n        grid: {\r\n          left: \"3%\",\r\n          right: \"4%\",\r\n          bottom: \"3%\",\r\n          containLabel: true\r\n        },\r\n        xAxis: {\r\n          type: \"category\",\r\n          boundaryGap: false,\r\n          data: [\r\n            \"1月\",\r\n            \"2月\",\r\n            \"3月\",\r\n            \"4月\",\r\n            \"5月\",\r\n            \"6月\",\r\n            \"7月\",\r\n            \"8月\",\r\n            \"9月\",\r\n            \"10月\",\r\n            \"11月\",\r\n            \"12月\"\r\n          ]\r\n        },\r\n        yAxis: {\r\n          type: \"value\"\r\n        },\r\n        series: [\r\n          {\r\n            name: \"访问量\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              120,\r\n              132,\r\n              101,\r\n              134,\r\n              90,\r\n              230,\r\n              210,\r\n              120,\r\n              132,\r\n              101,\r\n              134,\r\n              90,\r\n              230\r\n            ]\r\n          },\r\n          {\r\n            name: \"用户量\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              220,\r\n              182,\r\n              191,\r\n              234,\r\n              290,\r\n              330,\r\n              310,\r\n              182,\r\n              191,\r\n              234,\r\n              290,\r\n              330,\r\n              310\r\n            ]\r\n          },\r\n          {\r\n            name: \"收入\",\r\n            type: \"line\",\r\n            stack: \"总量\",\r\n            data: [\r\n              150,\r\n              232,\r\n              201,\r\n              154,\r\n              190,\r\n              330,\r\n              410,\r\n              232,\r\n              201,\r\n              154,\r\n              190,\r\n              330,\r\n              410\r\n            ]\r\n          }\r\n        ]\r\n      };\r\n      // // 使用刚指定的配置项和数据显示图表\r\n      myChart.setOption(option);\r\n      //根据窗口的大小变动图表\r\n      window.onresize = function() {\r\n        myChart.resize();\r\n      };\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n#home-chart {\r\n  background: #ffffff;\r\n  padding: 20px 0;\r\n}\r\n</style>\r\n\r\n"], "mappings": "AAIA;EACAA,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA;IACAD,SAAA,WAAAA,UAAA;MACA;MACA,IAAAE,OAAA,QAAAC,QAAA,CAAAC,IAAA,CAAAC,QAAA,CAAAC,cAAA;MACA;MACA,IAAAC,MAAA;QACAC,OAAA;UACAC,OAAA;QACA;QACAC,MAAA;UACAC,IAAA;QACA;QACAC,IAAA;UACAC,IAAA;UACAC,KAAA;UACAC,MAAA;UACAC,YAAA;QACA;QACAC,KAAA;UACAC,IAAA;UACAC,WAAA;UACAR,IAAA,GACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,OACA,OACA;QAEA;QACAS,KAAA;UACAF,IAAA;QACA;QACAG,MAAA,GACA;UACAC,IAAA;UACAJ,IAAA;UACAK,KAAA;UACAZ,IAAA,GACA,KACA,KACA,KACA,KACA,IACA,KACA,KACA,KACA,KACA,KACA,KACA,IACA;QAEA,GACA;UACAW,IAAA;UACAJ,IAAA;UACAK,KAAA;UACAZ,IAAA,GACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA;QAEA,GACA;UACAW,IAAA;UACAJ,IAAA;UACAK,KAAA;UACAZ,IAAA,GACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA,KACA;QAEA;MAEA;MACA;MACAT,OAAA,CAAAsB,SAAA,CAAAjB,MAAA;MACA;MACAkB,MAAA,CAAAC,QAAA;QACAxB,OAAA,CAAAyB,MAAA;MACA;IACA;EACA;AACA", "ignoreList": []}]}