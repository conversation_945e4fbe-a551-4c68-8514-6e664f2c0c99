<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>轮播图测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1">
    <link rel="stylesheet" href="../../layui/css/layui.css">
</head>
<body>

<div id="app">
    <h2>轮播图测试页面</h2>
    
    <!-- 轮播图容器 -->
    <div class="layui-carousel" id="test1" style="width: 100%; height: 400px;">
        <div carousel-item>
            <div v-for="(item,index) in swiperList" :key="index">
                <img style="width: 100%;height: 100%;object-fit:cover;" :src="item.img" :alt="item.name"/>
                <div style="position: absolute; bottom: 20px; left: 20px; background: rgba(0,0,0,0.6); color: white; padding: 8px 16px; border-radius: 4px;">
                    {{ item.name }}
                </div>
            </div>
        </div>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>轮播图数据:</h3>
        <ul>
            <li v-for="(item,index) in swiperList" :key="index">
                {{ item.name }}: {{ item.img }}
            </li>
        </ul>
    </div>
    
    <div style="margin-top: 20px;">
        <button @click="loadData" class="layui-btn">重新加载数据</button>
        <button @click="testDefault" class="layui-btn layui-btn-normal">测试默认图片</button>
    </div>
</div>

<script src="../../layui/layui.js"></script>
<script src="../../js/vue.js"></script>
<script src="../../js/config.js"></script>
<script src="../../modules/config.js"></script>
<script src="../../js/utils.js"></script>

<script>
var vue = new Vue({
    el: '#app',
    data: {
        swiperList: []
    },
    methods: {
        loadData() {
            console.log('开始加载轮播图数据...');
            
            layui.use(['carousel', 'http'], function() {
                var carousel = layui.carousel;
                var http = layui.http;
                
                http.request('config/list', 'get', {
                    page: 1,
                    limit: 10
                }, function (res) {
                    console.log('API响应:', res);
                    
                    if (res && res.data && res.data.list && res.data.list.length > 0) {
                        let swiperList = [];
                        res.data.list.forEach(element => {
                            if (element.value != null && element.value.trim() !== '') {
                                swiperList.push({
                                    img: element.value,
                                    name: element.name || '轮播图'
                                });
                            }
                        });

                        console.log('轮播图列表:', swiperList);
                        vue.swiperList = swiperList;

                        vue.$nextTick(() => {
                            carousel.render({
                                elem: '#test1',
                                width: '100%',
                                height: '400px',
                                arrow: 'hover',
                                anim: 'default',
                                autoplay: true,
                                interval: 3000,
                                indicator: 'inside'
                            });
                            console.log('轮播图初始化完成');
                        });
                    } else {
                        console.log('没有数据');
                        vue.testDefault();
                    }
                }, function(error) {
                    console.error('请求失败:', error);
                    vue.testDefault();
                });
            });
        },
        
        testDefault() {
            console.log('使用默认图片');
            this.swiperList = [{
                img: '../../xznstatic/img/162237296.jpg',
                name: '默认图片1'
            }, {
                img: '../../xznstatic/img/162240878.jpg',
                name: '默认图片2'
            }, {
                img: '../../xznstatic/img/1_092ZZ2503138.jpg',
                name: '默认图片3'
            }];
            
            this.$nextTick(() => {
                layui.use('carousel', function() {
                    var carousel = layui.carousel;
                    carousel.render({
                        elem: '#test1',
                        width: '100%',
                        height: '400px',
                        arrow: 'hover',
                        anim: 'default',
                        autoplay: true,
                        interval: 3000,
                        indicator: 'inside'
                    });
                });
            });
        }
    },
    
    mounted() {
        this.loadData();
    }
});
</script>

</body>
</html>
