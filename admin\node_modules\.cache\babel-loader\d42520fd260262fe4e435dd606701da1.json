{"remainingRequest": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js!D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js??ref--1-0!D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\1\\tiyuguan\\admin\\src\\components\\home\\HomeCard.vue", "mtime": 1750589903927}, {"path": "D:\\1\\tiyuguan\\admin\\babel.config.js", "mtime": 1642386765000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\1\\tiyuguan\\admin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:aW1wb3J0ICJjb3JlLWpzL21vZHVsZXMvZXMuYXJyYXkucHVzaC5qcyI7CmV4cG9ydCBkZWZhdWx0IHsKICBkYXRhOiBmdW5jdGlvbiBkYXRhKCkgewogICAgcmV0dXJuIHsKICAgICAgYWN0aW9uczogW3sKICAgICAgICB0aXRsZTogJ+WcuuWcsOeuoeeQhicsCiAgICAgICAgZGVzY3JpcHRpb246ICfnrqHnkIbkvZPogrLlnLrlnLDkv6Hmga8nLAogICAgICAgIGljb246ICdlbC1pY29uLW9mZmljZS1idWlsZGluZycsCiAgICAgICAgcm91dGU6ICcvY2hhbmdkaScKICAgICAgfSwgewogICAgICAgIHRpdGxlOiAn6aKE57qm566h55CGJywKICAgICAgICBkZXNjcmlwdGlvbjogJ+afpeeci+WSjOeuoeeQhumihOe6puiuouWNlScsCiAgICAgICAgaWNvbjogJ2VsLWljb24tZGF0ZScsCiAgICAgICAgcm91dGU6ICcvY2hhbmdkaU9yZGVyJwogICAgICB9LCB7CiAgICAgICAgdGl0bGU6ICfnlKjmiLfnrqHnkIYnLAogICAgICAgIGRlc2NyaXB0aW9uOiAn566h55CG57O757uf55So5oi3JywKICAgICAgICBpY29uOiAnZWwtaWNvbi11c2VyJywKICAgICAgICByb3V0ZTogJy95b25naHUnCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+WFrOWRiueuoeeQhicsCiAgICAgICAgZGVzY3JpcHRpb246ICflj5HluIPlkoznrqHnkIblhazlkYonLAogICAgICAgIGljb246ICdlbC1pY29uLWJlbGwnLAogICAgICAgIHJvdXRlOiAnL2dvbmdnYW8nCiAgICAgIH0sIHsKICAgICAgICB0aXRsZTogJ+iuuuWdm+euoeeQhicsCiAgICAgICAgZGVzY3JpcHRpb246ICfnrqHnkIborrrlnZvlhoXlrrknLAogICAgICAgIGljb246ICdlbC1pY29uLWNoYXQtZG90LXJvdW5kJywKICAgICAgICByb3V0ZTogJy9mb3J1bScKICAgICAgfV0KICAgIH07CiAgfSwKICBtZXRob2RzOiB7CiAgICBoYW5kbGVBY3Rpb246IGZ1bmN0aW9uIGhhbmRsZUFjdGlvbihhY3Rpb24pIHsKICAgICAgdGhpcy4kcm91dGVyLnB1c2goYWN0aW9uLnJvdXRlKTsKICAgIH0KICB9Cn07"}, {"version": 3, "names": ["data", "actions", "title", "description", "icon", "route", "methods", "handleAction", "action", "$router", "push"], "sources": ["src/components/home/<USER>"], "sourcesContent": ["<template>\r\n  <div class=\"quick-actions\">\r\n    <div class=\"action-item\" v-for=\"(action, index) in actions\" :key=\"index\" @click=\"handleAction(action)\">\r\n      <div class=\"action-icon\">\r\n        <i :class=\"action.icon\"></i>\r\n      </div>\r\n      <div class=\"action-content\">\r\n        <div class=\"action-title\">{{ action.title }}</div>\r\n        <div class=\"action-desc\">{{ action.description }}</div>\r\n      </div>\r\n      <div class=\"action-arrow\">\r\n        <i class=\"el-icon-arrow-right\"></i>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n<script>\r\nexport default {\r\n  data() {\r\n    return {\r\n      actions: [\r\n        {\r\n          title: '场地管理',\r\n          description: '管理体育场地信息',\r\n          icon: 'el-icon-office-building',\r\n          route: '/changdi'\r\n        },\r\n        {\r\n          title: '预约管理',\r\n          description: '查看和管理预约订单',\r\n          icon: 'el-icon-date',\r\n          route: '/changdiOrder'\r\n        },\r\n        {\r\n          title: '用户管理',\r\n          description: '管理系统用户',\r\n          icon: 'el-icon-user',\r\n          route: '/yonghu'\r\n        },\r\n        {\r\n          title: '公告管理',\r\n          description: '发布和管理公告',\r\n          icon: 'el-icon-bell',\r\n          route: '/gonggao'\r\n        },\r\n        {\r\n          title: '论坛管理',\r\n          description: '管理论坛内容',\r\n          icon: 'el-icon-chat-dot-round',\r\n          route: '/forum'\r\n        }\r\n      ]\r\n    }\r\n  },\r\n  methods: {\r\n    handleAction(action) {\r\n      this.$router.push(action.route)\r\n    }\r\n  }\r\n};\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n.box-card {\r\n  margin-right: 10px;\r\n  .header {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n  .content {\r\n    font-size: 30px;\r\n    font-weight: bold;\r\n    color: #666;\r\n    text-align: center;\r\n    .unit {\r\n      font-size: 16px;\r\n    }\r\n  }\r\n  .bottom {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n\r\n"], "mappings": ";AAiBA;EACAA,IAAA,WAAAA,KAAA;IACA;MACAC,OAAA,GACA;QACAC,KAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA,GACA;QACAH,KAAA;QACAC,WAAA;QACAC,IAAA;QACAC,KAAA;MACA;IAEA;EACA;EACAC,OAAA;IACAC,YAAA,WAAAA,aAAAC,MAAA;MACA,KAAAC,OAAA,CAAAC,IAAA,CAAAF,MAAA,CAAAH,KAAA;IACA;EACA;AACA", "ignoreList": []}]}